<template>
    <div v-if="rightsReady" ref="app">
        <el-config-provider :locale="localeLan">
            <router-view v-if="isRouterAlive" />
        </el-config-provider>
    </div>
</template>
<script>
export default {
    name: 'App',
};
</script>
<script setup>
import { ref, reactive, computed, onMounted, onBeforeMount, onBeforeUnmount, provide, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
import axios from 'axios';
import debounce from 'lodash/debounce';
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';
import en from 'element-plus/dist/locale/en.mjs';
import HTTP from '@/util/httpService.js';
import { DEBOUNCE_TIME } from '@/util/constants.js';
import commonStore from '@/store/commonStore.js';
import { showErrMsg } from '@/util/common.js';
import { useRoute, useRouter } from 'vue-router';
const { t, locale } = useI18n();
const store = commonStore();
const route = useRoute();
const router = useRouter();
const isRouterAlive = ref(true);
const reload = () => {
    isRouterAlive.value = false;
    nextTick(function () {
        isRouterAlive.value = true;
    });
};
provide('reload', reload);
// 权限设置
const rights = reactive({});
provide('rights', rights);
const rightsReady = ref(false);
const userId = ref('');
// 获取权限信息
const setDefaultJump = () => {
    const originalName = route.name;
    let isIncludes = false;
    let firstMenuId = '';
    function loopMenu(node) {
        node.forEach(item => {
            if (item.children && item.children.length > 0) {
                loopMenu(item.children);
            } else {
                if (!firstMenuId) {
                    firstMenuId = item.url;
                }
                if (item.id === originalName) {
                    isIncludes = true;
                }
            }
        });
    }
    loopMenu(rights.menuListVos);
    if (!isIncludes) {
        router.replace(`/${firstMenuId}`);
    }
};
const getOptLicense = () => {
    return new Promise((resolve, reject) => {
        if (userId.value) {
            HTTP.request('getOptLicense', {
                method: 'post',
                data: {
                    employeeId: userId.value,
                },
                complete: resp => {
                    if (resp.code === 0) {
                        rights.menuListVos = resp.data?.menuListVos || [];
                        if (!rights.menuListVos || !rights.menuListVos.length) {
                            router.replace('/noPermission');
                        } else if (rights.menuListVos && rights.menuListVos.length) {
                            setDefaultJump();
                        }
                        try {
                            rights.productPermissions = resp.data.permissions?.['1'] || {};
                        } catch (e) {
                            rights.productPermissions = {};
                            resolve();
                        }
                        try {
                            rights.projectPermissions = resp.data.permissions?.['2'] || {};
                        } catch (e) {
                            rights.projectPermissions = {};
                            resolve();
                        }
                        try {
                            rights.areaPermissions = resp.data.permissions?.['3'] || {};
                        } catch (e) {
                            rights.areaPermissions = {};
                            resolve();
                        }
                    } else {
                        router.replace('/noPermission');
                    }
                    resolve();
                },
                error: () => {
                    router.replace('/noPermission');
                    resolve();
                },
            });
        } else {
            resolve();
        }
    });
};
provide('getOptLicense', getOptLicense);
// 获取用户信息
const getUserInfo = () => {
    return new Promise((resolve, reject) => {
        if (route.name !== 'dcLogin') {
            HTTP.request('/api/security-manager/v1/uportal/auth/user-info', {
                method: 'post',
                complete: resp => {
                    if (resp.code === 0) {
                        store.setUserInfo(resp.data || null);
                        userId.value = resp.data?.id || '';
                    } else {
                        showErrMsg(resp);
                    }
                    resolve();
                },
                error: () => {
                    showErrMsg(t('tipMessage.networkError'));
                    resolve();
                },
            });
        } else {
            resolve();
        }
    });
};
const getConfig = () => {
    return new Promise((resolve, reject) => {
        HTTP.request('/api/security-manager/v1/uac-config-inner/get-config', {
            method: 'get',
            complete: resp => {
                if (resp.code === 0) {
                    store.setConfigInfo(resp.data || null);
                } else {
                    showErrMsg(resp);
                }
                resolve();
            },
            error: () => {
                showErrMsg(t('tipMessage.networkError'));
                resolve();
            },
        });
    });
};
// 语言设置
const localeLan = computed(() => {
    return locale.value === 'zh-CN' ? zhCn : en;
});
const updateLang = () => {
    let lang = window.languageOptionDash || navigator.language || 'zh-CN';
    locale.value = lang;
    axios.defaults.headers['accept-language'] = lang;
};
onBeforeMount(async () => {
    updateLang();
});

// 页面高度
const MinHeight = 600;
const headerH = 50;
const updateResize = () => {
    const totalHeight = window.innerHeight - headerH;
    store.setHeight(Math.max(totalHeight, MinHeight));
    store.setResize(!store.resize);
};
const debounceUpdateResize = debounce(updateResize, DEBOUNCE_TIME);
onMounted(async () => {
    // await getConfig();
    await getUserInfo();
    // await getOptLicense();
    setTimeout(() => {
        // 跳转那边也用了replace这边就无法切换到无权限页面,所以需要确保这边无权限跳转后再渲染
        rightsReady.value = true; // 权限查询完成后，渲染组件
    }, 0);
    updateResize();
    window.addEventListener('resize', debounceUpdateResize);
    // 监听localStoratge / sessionStorage的变化，响应修改国际化语言/全局变量
    window.addEventListener('storage', e => {
        if (e.key === 'language-option') {
            updateLang();
        }
    });
});
onBeforeUnmount(() => {
    window.removeEventListener('resize', debounceUpdateResize);
});
</script>
