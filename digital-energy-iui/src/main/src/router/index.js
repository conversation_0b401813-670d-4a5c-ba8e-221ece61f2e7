/* Started by AICoder, pid:ucb47ad69cy523b1486508f2815636812139375e */
import { createRouter, createWebHashHistory } from 'vue-router';
import PageNone from '@/views/pageNone.vue';
import Page404 from '@/views/page404.vue';
import Layout from '@/views/layout/index.vue';
import DcLogin from '@/views/layout/dcLogin.vue';
import NoPermissionPage from '@/views/pageNoPermission.vue';
export default createRouter({
    history: createWebHashHistory(process.env.BASE_URL),
    routes: [
        {
            path: '/dcLogin',
            name: 'dcLogin',
            component: Dc<PERSON>og<PERSON>
        },
        {
            path: '/:catchAll(.*)',
            name: 'NOT FOUND',
            component: PageNone,
        },
        {
            path: '/404',
            name: '404-NOT FOUND',
            component: Page404,
        },
        {
            path: '/noPermission',
            name: 'noPermission',
            component: Layout,
            redirect: '/noPermission/noPermissionPage',
            children: [
                {
                    path: 'noPermissionPage',
                    name: 'noPermissionPage',
                    component: NoPermissionPage,
                },
            ]
        },
    ],
});
/* Ended by AICoder, pid:ucb47ad69cy523b1486508f2815636812139375e */
