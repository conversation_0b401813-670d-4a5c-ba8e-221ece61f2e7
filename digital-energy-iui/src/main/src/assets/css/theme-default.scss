@use 'element-plus/theme-chalk/src/index.scss' as *;
@use 'element-plus/theme-chalk/src/dark/css-vars.scss' as *;
@use './color.scss' as *;

// 修复滚动条样式被平台样式覆盖问题
html[lang] {
    scrollbar-color: unset;
}
html.default,
html.dark {
    background: var(--content-bg-color);
}
html body {
    /* stylelint-disable-next-line font-family-no-missing-generic-family-keyword */
    font-family: 'Helvetica Neue', 'PingFang SC', Tahoma, 'Microsoft Yahei';
    font-size: 14px;
    margin: 0;
    padding: 0;
    color: var(--default-color);
    &::-webkit-scrollbar,
    ::-webkit-scrollbar {
        background-color: transparent;
        width: 12px;
        height: 12px;
    }
    &::-webkit-scrollbar-track,
    ::-webkit-scrollbar-track {
        border-radius: 20px;
        box-shadow: none;
        border: 0;
    }
    &::-webkit-scrollbar-thumb,
    ::-webkit-scrollbar-thumb {
        border-radius: 20px;
        background-color: var(--scrollbar-thumb-color);
        box-shadow: none;
        border: 0;
    }
    ::-webkit-scrollbar-corner {
        background-color: transparent;
    }
    .el-table__body-wrapper::-webkit-scrollbar,
    .uedm-tree__wrap::-webkit-scrollbar,
    .uedm-scrollbar__width::-webkit-scrollbar,
    .uedm-selectionbox::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }
}
ul {
    padding: 0;
    margin: 0;
}
ul,
li {
    list-style: none;
}
ul.float li {
    float: left;
}
.clear::after,
ul.float::after,
.uedm-table__bar::after,
.uedm-breadcrumbs::after {
    clear: both;
    content: '';
    display: block;
    height: 0;
    visibility: hidden;
}
.float-right {
    float: right;
}
.border {
    border: solid 1px var(--border-color);
}
.border-color {
    border-color: var(--border-color);
}
p {
    margin: 0;
}
.p16 {
    padding: 16px !important;
}
.pl16 {
    padding-left: 16px !important;
}
.pr16 {
    padding-right: 16px !important;
}
.pt16 {
    padding-top: 16px !important;
}
.pb16 {
    padding-bottom: 16px !important;
}
.p0 {
    padding: 0 !important;
}
.pt0 {
    padding-top: 0 !important;
}
.pb0 {
    padding-bottom: 0 !important;
}
.pl0 {
    padding-left: 0 !important;
}
.pr0 {
    padding-right: 0 !important;
}
.m16 {
    margin: 16px !important;
}
.mt1 {
    margin-top: 1px !important;
}
.mt16 {
    margin-top: 16px !important;
}
.mb16 {
    margin-bottom: 16px !important;
}
.mb0 {
    margin-bottom: 0 !important;
}
.ml16 {
    margin-left: 16px !important;
}
.mr16 {
    margin-right: 16px !important;
}
.ml8 {
    margin-left: 8px !important;
}
.uedm-note {
    color: var(--disabled-color);
}
.uedm-nodata {
    font-size: 24px;
    text-align: center;
    font-weight: 400;
    color: var(--disabled-color);
}
.uedm-space {
    padding: 16px;
}
.uedm-title {
    font-size: 16px;
    font-weight: bold;
    padding: 0;
    margin: 0 0 16px;
    color: var(--title-color);
}
.uedm-block-title {
    font-size: 14px;
    font-weight: bold;
    padding: 0;
    margin: 0 0 16px;
    color: var(--title-color);
}

// breadcrumbs
.uedm-navigation {
    padding: 16px;
    min-height: 21px;
    background: var(--content-bg-color);
    border-bottom: solid 1px var(--border-td-color);
    .uedm-navigation__bar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .el-input {
            width: 300px;
        }
        .uedm-breadcrumbs-wrap {
            flex: 1;
        }
    }
    .uedm-navigation__op {
        text-align: right;
        margin-left: auto;
        white-space: nowrap;
        .el-radio-group {
            flex-wrap: nowrap;
        }
    }
    + .uedm-content-area {
        margin-top: 16px;
    }
}
.uedm-pagetitle {
    color: var(--title-color);
    font-size: 20px;
    font-weight: bold;
    .el-link {
        color: var(--title-color);
        margin-top: -2px;
        &:hover {
            color: var(--active-color);
        }
        + .uedm-pagetitle__text {
            width: calc(100% - 36px);
            margin-left: 8px;
        }
    }
    &.ellipsis {
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .uedm-pagetitle__text {
        display: inline-block;
        vertical-align: middle;
    }
}
.uedm-breadcrumbs {
    li {
        float: left;
        padding: 0 10px;
        position: relative;
        font-size: 16px;
        height: 21px;
        line-height: 21px;
        color: var(--info-color);
        .el-link {
            font-size: 16px;
            color: var(--active-color);
            &:hover {
                color: var(--active-border-color);
            }
        }
    }
    li:last-child {
        color: var(--title-color);
        font-size: 16px;
        font-weight: bold;
        .el-link {
            font-size: 16px;
            font-weight: bold;
        }
    }
    li::before {
        content: '/';
        position: absolute;
        left: 0;
        color: var(--info-color);
        font-weight: normal;
    }
    li:first-child {
        padding-left: 0;
    }
    li:first-child::before {
        content: '';
    }
    &.title {
        li {
            height: 26px;
            line-height: 26px;
        }
        li:first-child {
            color: var(--title-color);
            font-size: 16px;
            font-weight: bold;
            .el-link {
                color: var(--title-color);
                margin-top: -2px;
                &:hover {
                    color: var(--active-color);
                }
            }
        }
        li:nth-child(2)::before {
            content: '';
            width: 1px;
            top: 3px;
            bottom: 3px;
            background-color: var(--info-color);
        }
    }
    &.description {
        li {
            height: 26px;
            line-height: 26px;
        }
        li:first-child {
            color: var(--info-color);
            font-size: 16px;
        }
        li:nth-child(2)::before {
            content: '';
        }
    }
}
.uedm-navigation-bar {
    padding: 0 16px 8px;
    background: var(--content-bg-color);
    display: flex;
    justify-content: space-between;
    .el-input {
        width: 300px;
    }
}
.uedm-navigation-info {
    padding: 0 16px 16px;
    background: var(--content-bg-color);
    .el-descriptions .el-descriptions__body .el-descriptions__table:not(.is-bordered) tr:last-child td {
        padding-bottom: 0;
    }
}
.uedm-tabs__op {
    float: right;
    position: relative;
    z-index: 99;
    height: 43px;
    &::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        height: 1px;
        background-color: var(--border-disabled-color);
    }
}
.uedm-navigation-tabs {
    > .el-tabs__header {
        background-color: var(--content-bg-color);
        .el-tabs__nav-wrap {
            &.is-scrollable {
                padding: 0 20px;
            }
        }
    }
}
.uedm-tabs__contentspace {
    .el-tabs__content {
        padding: 0 16px 16px;
    }
}
.uedm-navigation + .uedm-navigation-tabs {
    >.el-tabs__header > .el-tabs__nav-wrap::after {
        background-color: transparent;
    }
}
.uedm-navigation + .uedm-tabs__op {
    &::after {
        background-color: transparent;
    }
    & + .uedm-navigation-tabs {
        >.el-tabs__header > .el-tabs__nav-wrap::after {
            background-color: transparent;
        }
    }
}
.uedm-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
span.uedm-ellipsis {
    display: inline-block;
}
.uedm-page-blank {
    display: flex;
    align-items: center;
    height: 100%;
    box-sizing: border-box;
    .uedm-page-blank-content {
        flex: 1;
        text-align: center;
        font-size: 24px;
        font-weight: 400;
        color: var(--disabled-color);
        .small-font {
            font-size: 14px;
        }
    }
    .uedm-page-blank-content > i {
        font-size: 48px;
        margin-bottom: 16px;
        color: #ff9852;
    }
    i.icon-blank {
        display: inline-block;
        background: url('../img/icon-empty.png') no-repeat center;
        width: 200px;
        height: 200px;
    }
}
.uedm-echart-tooltip {
    max-width: 500px;
    word-wrap: break-word;
    white-space: normal!important;
}
.uedm-split-border {
    border-color: var(--border-td-color) !important;
}
.uedm-form-single {
    margin-bottom: -16px;
}

/* === elementuiplus === */
:focus-visible {
    outline: none;
    box-shadow: none;
}
.el-popper:focus-visible {
    outline: none;
}
.el-popper {
    max-width: 1000px;
    &.el-popover {
        padding: 16px;
        word-wrap: break-word;
        word-break: normal;
    }
    .el-form {
        &:not(.el-form--inline) .el-form-item:last-child {
            .el-form-item__content {
                justify-content: flex-end;
            }
            .el-button {
                padding-left: 16px;
                padding-right: 16px;
            }
        }
    }
}
.el-popover {
    user-select: none; // 防止文字获得焦点，而导致进入右侧运算框时会扰乱顺序
}
.el-alert {
    display: inline-flex;
    width: auto;
    min-width: 380px;
    .el-alert__title {
        font-size: 12px;
        padding-top: 2px;
        color: var(--title-color);
    }
    .el-link {
        font-size: 12px;
    }
    &.el-alert--info.is-light {
        background-color: var(--active-bg-color);
        .el-alert__icon {
            color: var(--active-color);
        }
    }
    .el-alert__content {
        padding-right: 16px;
    }
}
.el-message {
    min-width: 380px;
    background-color: var(--pop-layer-bg-color);
    border-color: var(--pop-layer-bg-color);
    box-shadow: 0 0 12px rgba(0, 0, 0, 0.16);
    .el-message__content {
        color: var(--title-color);
        min-width: 285px;
    }
    .el-message-icon--info {
        color: var(--active-color);
    }
}
.el-notification {
    padding: 16px;
    min-width: 380px;
    background-color: var(--pop-layer-bg-color);
    .el-notification__group {
        min-width: 309px;
        margin-right: 0;
    }
    .el-notification__title {
        padding-right: 16px;
    }
    .el-notification--info {
        color: var(--active-color);
    }
    .el-notification__closeBtn {
        right: 16px;
    }
}
.el-link {
    & + .el-link,
    & + .el-dropdown {
        margin-left: 12px;
    }
}

// el-button
@mixin button-style($color) {
    color: #fff;
    &:focus:not(:active, :hover) {
        background-color: $color;
        border-color: $color;
    }
}
.el-button {
    padding: 6px 12px;
    &:focus-visible {
        outline: none;
    }
    & + .el-button {
        margin-left: 8px;
    }
    &.el-button--small {
        padding: 6px 12px;
    }
    &.uedm-button-icon {
        padding: 8px;
        font-size: 16px;
    }

    color: var(--active-color);
    &:focus:not(:active, :hover) {
        background-color: var(--content-bg-color);
        border-color: var(--border-color);
    }
    &.el-button--primary {
        @include button-style(var(--active-color));
    }
    &.el-button--success {
        @include button-style(var(--success-color));
    }
    &.el-button--warning {
        @include button-style(var(--warning-color));
    }
    &.el-button--danger {
        @include button-style(var(--danger-color));
    }
    &.el-button--info {
        @include button-style(var(--info-color));
    }
}
.el-button--red {
    background-color: transparent;
    border-color: var(--border-color);
    color: var(--danger-color);
    &:focus:not(:active, :hover) {
        background-color: transparent;
        border-color: var(--border-color);
        color: var(--danger-color);
    }
    &:hover {
        background-color: var(--danger-bg-color);
        border-color: var(--danger-border-color);
        color: var(--danger-color);
    }
    &:active {
        background-color: var(--danger-bg-color);
        color: var(--danger-color);
        border-color: var(--danger-color);
    }
    &.is-disabled,
    &.is-disabled:hover {
        color: var(--danger-border-color);
        background-color: var(--content-bg-color);
        border-color: var(--border-disabled-color);
    }
}

@mixin button-is-plain-style($color, $bgColor, $borderColor) {
    color: $color;
    &:focus:not(:active, :hover) {
        color: $color;
        background-color: $bgColor;
        border-color: $borderColor;
    }
    &:hover:not(.is-disabled) {
        color: #fff;
        border-color: $borderColor;
    }
}
.is-plain {
    color: var(--default-color);
    &.is-disabled {
        color: var(--disabled-color);
    }
    &:focus:not(:active, :hover) {
        color: var(--default-color);
        background-color: var(--content-bg-color);
        border-color: var(--border-color);
    }
    &:hover:not(.is-disabled) {
        color: var(--active-color);
        border-color: var(--active-border-color);
    }
    &:active:not(.is-disabled) {
        color: var(--active-color);
        border-color: var(--active-color);
    }
    &.el-button--primary {
        @include button-is-plain-style(var(--active-color), var(--active-bg-color), var(--active-border-color));
    }
    &.el-button--success {
        @include button-is-plain-style(var(--success-color), var(--success-bg-color), var(--success-border-color));
    }
    &.el-button--warning {
        @include button-is-plain-style(var(--warning-color), var(--warning-bg-color), var(--warning-border-color));
    }
    &.el-button--danger {
        @include button-is-plain-style(var(--danger-color), var(--danger-bg-color), var(--danger-border-color));
    }
    &.el-button--info {
        @include button-is-plain-style(var(--info-color), var(--info-bg-color), var(--info-border-color));
    }
}

@mixin button-is-text-style($color,$disabledColor) {
    color: $color;
    &:focus:not(:active, :hover) {
        color: $color;
        background-color: transparent;
        border-color: transparent;
    }
    &.is-disabled {
        color: $disabledColor;
    }
}
.is-link {
    @include button-is-text-style(var(--default-color),var(--disabled-color));
    &.el-button--primary {
        @include button-is-text-style(var(--active-color),var(--active-disabled-color));
    }
    &.el-button--success {
        @include button-is-text-style(var(--success-color), var(--success-disabled-color));
    }
    &.el-button--warning {
        @include button-is-text-style(var(--warning-color), var(--warning-disabled-color));
    }
    &.el-button--danger {
        @include button-is-text-style(var(--danger-color), var(--danger-disabled-color));
    }
    &.el-button--info {
        @include button-is-text-style(var(--info-color), #c8c9cc);
    }
}
.is-text {
    &.el-button {
        @include button-is-text-style(var(--default-color),var(--disabled-color));
    }
    &.el-button--primary {
        @include button-is-text-style(var(--active-color),var(--active-disabled-color));
    }
    &.el-button--success {
        @include button-is-text-style(var(--success-color), var(--success-disabled-color));
    }
    &.el-button--warning {
        @include button-is-text-style(var(--warning-color), var(--warning-disabled-color));
    }
    &.el-button--danger {
        @include button-is-text-style(var(--danger-color), var(--danger-disabled-color));
    }
    &.el-button--info {
        @include button-is-text-style(var(--info-color), var(--info-disabled-color));
    }
}
.el-button-group {
    .el-button {
        /* stylelint-disable-next-line selector-not-notation */
        &:not(.is-link, .is-text, .is-plain):not(.el-button--text, .el-button--primary, .el-button--success, .el-button--info, .el-button--warning, .el-button--danger, .el-button--red):not(.is-disabled) {
            color: var(--title-color);
            &:hover{
                color: var(--active-color);
                border-color: var(--border-color);
                background-color: var(--content-bg-color);
            }
            &.active, &.active:focus{
                color: #fff;
                background-color: var(--active-color);
                border-color: var(--active-color);
            }
        }
        & + .el-button {
            margin-left: 0;
        }
    }
}
.el-radio-group {
    .is-disabled:not(.is-active) .el-radio-button__inner {
        background-color: var(--disabled-bg-color);
        color: var(--disabled-color);
    }
    .is-active:not(.is-disabled) .el-radio-button__inner {
        background-color: var(--active-color);
        border-color: var(--active-color);
        color: #fff;
    }
    .is-active.is-disabled .el-radio-button__inner {
        background-color: var(--active-color);
        border-color: var(--active-color);
        color: #fff;
    }
}
.el-radio-button--small .el-radio-button__inner {
    padding: 5px 7px;
}
.icon-op {
    font-size: 16px;
    cursor: pointer;
    padding: 5px;
    color: var(--default-color);
    &:hover {
        color: var(--active-disabled-color);
    }
    & + .icon-op{
        margin-left: 8px;
    }
    &.op-red {
        color: var(--danger-color);
        &:hover {
            color: var(--danger-hover-color);
        }
    }
}

// tree
.uedm-tree__wrap{
    overflow: auto;
    & .el-tree .el-tree-node__children {
        overflow: visible !important;
    }
    & .el-tree-node__content > .el-tree-node__expand-icon {
        padding: 4px 6px;
        font-weight: normal;
    }
    .el-tree-node__expand-icon {
        color: var(--default-color);
    }
}
.el-tree__empty-text {
    font-size: 16px;
    color: var(--disabled-color);
}
.uedm-tree__search-wrap {
    width: 100%;
    box-sizing: border-box;
    margin-bottom: 8px;
    .el-input-group--append .el-input-group__append {
        padding: 0;
        cursor: pointer;
        & > .el-icon {
            margin-left: 0;
            height: 32px;
            width: 30px;
        }
    }
}
.uedm-tree__nopadding {
    .uedm-tree__search-wrap {
        padding: 16px 16px 0;
    }
    .uedm-tree__wrap .el-tree-node__content > .el-tree-node__expand-icon {
        margin-left: 10px;
    }
}
.uedm-tree-btn {
    .node-ellipsis {
        display: inline-block;
        max-width: 500px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;
    }
    .btn-bar {
        position: absolute;
        right: 0;
        top: -4px;
    }
    .tree-node {
        position: relative;
        padding-right: 115px;
    }
    .hover-to-show {
        display: none;
    }
    .el-tree-node__content:hover > .tree-node > .hover-to-show {
        display: inline-block;
    }
}

// table
.uedm-table__bar {
    margin-bottom: 8px;
    .uedm-title {
        display: inline-block;
        margin-bottom: 0;
        height: 32px;
        vertical-align: top;
    }
}
.el-table {
    .el-table__header .el-table__cell {
        background-color: var(--head-bg-color) !important;
        color: var(--label-color) !important;
    }
    .el-table__body {
        tr.current-row > td.el-table__cell {
            background-color: var(--current-row-bg-color);
        }
        .cell {
            color: var(--title-color);
            .el-button--text {
                padding: 0;
                height: 23px;
                line-height: 23px;
            }
            .el-link,
            .el-dropdown {
                vertical-align: middle;
            }
        }
        .el-radio {
            height: 24px;
        }
    }
    .el-table__empty-text {
        font-size: 16px;
        color: var(--disabled-color);
    }
    &:not(.el-table--border) {
        .el-table__inner-wrapper::before {
            background: none;
        }
    }
}
.uedm-table-border {
    border: solid 1px var(--border-td-color);
    .el-table .el-table__row:last-child .el-table__cell {
        border-bottom: none;
    }
}
.el-pagination {
    justify-content: flex-end;
    padding: 8px 0 0;
    text-align: right;
    .el-pagination__total {
        margin-right: 4px;
    }
    button {
        min-width: 32px;
        height: 32px;
        line-height: 32px;
        border-radius: 2px;
        background-color: transparent;
        border: solid 1px var(--border-color);
        &:not([disabled]):hover {
            color: var(--active-color);
        }
        &[disabled] {
            color: var(--disabled-color);
            background-color: var(--disabled-bg-color);
            border-color: var(--border-disabled-color);
        }
    }
    .btn-prev {
        padding-right: 6px;
    }
    .btn-next {
        padding-left: 6px;
    }
    .el-input__inner {
        height: 32px;
        line-height: 32px;
    }
    .el-select {
        .el-input {
            margin: 0 0 0 4px;
            width: 100px;
        }
    }
    .el-pager {
        margin: 0 2px;
        li {
            font-size: 14px;
            min-width: 32px;
            height: 32px;
            line-height: 32px;
            box-sizing: border-box;
            text-align: center;
            background-color: transparent;
            border: solid 1px var(--border-color);
            margin: 0 4px;
            border-radius: 2px;
            color: var(--title-color);
            font-weight: normal;
            &:not(.is-active):hover {
                color: var(--active-color);
            }
        }
        .is-active {
            color: var(--active-color);
            border-color: var(--active-color);
        }
    }
}

// form表单
.el-input {
    &.uedm-search {
        width: 232px;
        .el-input-group__append {
            padding: 0;
            cursor: pointer;
            & > .el-icon {
                margin-left: 0;
                height: 32px;
                width: 35px;
            }
        }
    }
}
.el-input-group__append,
.el-input-group__prepend {
    padding: 0 12px;
}
.el-input-number.append {
    .el-input__wrapper {
        border-radius: 4px 0 0 4px;
    }
}
.el-input-number-group__append {
    display: inline-block;
    padding: 0 16px;
    border: solid 1px var(--border-color);
    color: var(--info-color);
    border-left: none;
    height: 30px;
    line-height: 30px;
    border-radius: 0 4px 4px 0;
    background-color: var(--disabled-bg-color);
}
.el-select-dropdown__option-item {
    font-size: 14px;
}
.el-select-dropdown__empty {
    font-size: 14px;
}
.el-date-picker {
    .el-picker-panel__footer {
        .el-button.is-plain {
            color: var(--active-color);
        }
    }
}
.el-cascader__collapse-tags {
    max-height: 300px;
    overflow: auto;
    &::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }
}
.tree-select {
    .el-input {
        width: 100%;
    }
    .el-select-dropdown__item {
        padding: 10px 16px;
        .el-tree-node__label {
            font-weight: normal;
        }
    }
    .el-select-dropdown__item.hover,
    .el-select-dropdown__item.is-hovering,
    .el-select-dropdown__item:hover {
        background-color: transparent;
    }
    &.no-padding {
        .el-select-dropdown__list, .el-select-dropdown__item {
            padding: 0;
        }
        .uedm-tree__search-wrap {
            padding: 16px 16px 0;
        }
        .el-tree-node__content {
            height: 32px;
        }
    }
}
.el-radio__input {
    // & + .el-radio__label {
    //     color: var(--default-color);
    // }
    &.is-disabled + .el-radio__label {
        color: var(--disabled-color);
    }
}
.el-collapse {
    border: none;
    .el-collapse-item__header {
        position: relative;
        display: inline-flex;
        border: none;
        font-size: 16px;
        font-weight: bold;
        color: var(--title-color);
        height: 21px;
        line-height: 21px;
        width: auto;
        .el-icon {
            margin-left: 8px;
            font-weight: normal;
        }
    }
    .el-collapse-item__wrap {
        border: none;
    }
    .el-collapse-item__content {
        padding-bottom: 0;
        padding-top: 16px;
    }
}

// tabs
.el-tabs {
    --el-tabs-header-height: 43px;
    .el-tabs__header {
        margin-bottom: 16px;
    }
    .el-tabs__nav-wrap::after {
        height: 1px;
        background-color: var(--border-disabled-color);
    }
    .el-tab-pane > .uedm-space {
        padding-top: 0;
    }
    &:not(.el-tabs--card) > .el-tabs__header {
        .el-tabs__item {
            font-size: 14px;
            line-height: 19px;
            align-items: flex-start;
            padding-top: 12px;
            padding-left: 16px;
            padding-right: 16px;
            i {
                color: var(--label-color);
            }
            &.is-active {
                color: var(--active-color);
                border-bottom: solid 2px var(--active-color);
                i {
                    color: var(--active-color);
                }
            }
            &:hover {
                color: var(--active-color);
                i {
                    color: var(--active-color);
                }
            }
        }
        .el-tabs__active-bar {
            display: none;
        }
    }
    .el-tabs__nav-next, .el-tabs__nav-prev {
        line-height: 43px;
        color: var(--default-color);
        &.is-disabled {
            color: var(--disabled-color);
            cursor: not-allowed;
        }
    }
    .el-tabs__item:focus-visible {
        box-shadow: none;
    }
}
.el-tabs--card {
    & > .el-tabs__header .el-tabs__item.is-active {
        border-bottom-color: transparent;
    }
}

.el-dialog {
    padding: 20px;
    &.hidden .el-dialog__header {
        display: none;
    }
    .el-dialog__header {
        padding-bottom: 20px;
        .el-dialog__title {
            word-wrap: break-word;
            color: var(--title-color);
            font-size: 18px;
            height: 20px;
            line-height: 20px;
        }
        .el-dialog__close {
            font-size: 16px;
        }
        .el-dialog__headerbtn {
            top: 6px;
        }
    }
    .el-dialog__body {
        word-wrap: break-word;
        .bottom-bar {
            text-align: right;
            margin-bottom: 0;
            .el-button {
                padding-left: 24px;
                padding-right: 24px;
            }
        }
    }
    .el-dialog__footer {
        padding-top: 20px;
        .el-button {
            padding-left: 24px;
            padding-right: 24px;
        }
    }
}
.el-transfer {
    .el-transfer__buttons {
        padding: 0 25px;
        .el-transfer__button {
            width: 38px;
            height: 32px;
            padding: 0;
            display: block;
            border-radius: 4px;
            &:nth-child(2) {
                margin: 8px 0 0;
            }
        }
    }
    .el-transfer-panel__footer {
        text-align: right;
        padding-right: 16px;
    }
}

.bottom-bar {
    text-align: right;
    .el-button {
        padding-left: 24px;
        padding-right: 24px;
    }
}

.el-timeline {
    .uedm-timeline__title {
        font-size: 14px;
        font-weight: 700;
        margin-bottom: 6px;
        color: var(--title-color);
    }
    .uedm-timeline__description {
        font-size: 13px;
        font-weight: normal;
        color: var(--default-color);
    }
    .uedm-timeline__content {
        font-size: 14px;
        font-weight: normal;
        color: var(--title-color);
    }
    .uedm-timeline__note {
        color: var(--info-color);
        font-size: 13px;
    }
    .uedm-timeline__content + .uedm-timeline__note,
    .uedm-timeline__note + .uedm-timeline__content {
        margin-top: 6px;
    }
    .el-timeline-item__node--info {
        background-color: var(--border-disabled-color);
        border-color: var(--border-disabled-color);
    }
    .uedm-timeline__nobg .el-timeline-item__node {
        background-color: transparent;
        .el-timeline-item__icon {
            font-size: 16px;
        }
        &.el-timeline-item__node--primary .el-timeline-item__icon {
            color: var(--active-color);
        }
        &.el-timeline-item__node--success .el-timeline-item__icon {
            color: var(--success-color);
        }
        &.el-timeline-item__node--danger .el-timeline-item__icon {
            color: var(--danger-color);
        }
        &.el-timeline-item__node--warning .el-timeline-item__icon {
            color: var(--warning-color);
        }
        &.el-timeline-item__node--info .el-timeline-item__icon {
            color: var(--border-disabled-color);
        }
    }
    .uedm-timeline__flex {
        display: flex;
        align-items: flex-end;
    }
    .uedm-timeline__ellipsis {
        max-width: 350px;
        max-height: 38px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: normal;
        word-wrap: break-word;
        display: -webkit-box;
        -webkit-line-clamp: 2; /* 控制显示的行数 */
        -webkit-box-orient: vertical;
    }
    &.uedm-timeline__leftTime {
        padding-left: 146px;
        .el-timeline-item__timestamp {
            position: absolute;
            left: -146px;
            top: 4px;
            width: 130px;
            text-align: right;
            margin-top: 0;
        }
    }
}

.uedm-selectionbox {
    width: 300px;
    max-height: 88px;
    min-height: 26px;
    padding: 4px;
    line-height: 26px;
    box-sizing: border-box;
    border: solid 1px #dcdfe6;
    overflow: auto;
    .el-tag {
        height: 20px;
        font-size: 12px;
        margin: 0 4px;
        &.el-tag--info {
            background-color: #f0f2f5;
            border-color: #f0f2f5;
            color: #909399;
        }
        .el-tag__close {
            color: #909399;
            &:hover {
                background-color: #909399;
                color: #fff;
            }
        }
    }
}
.workDatePicker { // 工作台时间查询组件太靠右，打开时间选择框后超出页面，出现横向滚动条问题解决
    .el-date-range-picker.has-sidebar {
        width: 830px;
    }
}
.brandDatePicker {
    .el-date-range-picker {
        width: 720px;
    }
}

.navigation.el-steps {
    .el-step {
        cursor: pointer;
    }
    .el-step__head {
        &.is-wait {
            color: var(--info-color);
            border-color: var(--info-color);
        }
    }
    .el-step__title {
        &.is-wait {
            color: var(--info-color);
        }
    }
    .el-step__description {
        &.is-wait {
            color: var(--info-color);
        }
    }
    .is-success .el-step__line {
        background-color: var(--success-color);
    }
    .el-step__line {
        background-color: var(--border-disabled-color);
    }
    &.el-steps--vertical {
        .el-step__description {
            padding-right: 0;
        }
    }
    .is-process {
        .el-step__icon {
            background-color: var(--active-color);
            border-color: var(--active-color);
            color: #fff;
        }

        color: var(--active-color);
    }
}