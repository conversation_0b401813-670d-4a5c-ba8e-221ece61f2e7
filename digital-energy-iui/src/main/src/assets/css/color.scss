html {
    // 文字颜色
    --title-color: #303133; // 标题文字颜色(等级1)
    --default-color: #606266; // 默认文字颜色(次要等级2)
    --label-color: #606266; // 表单项标题,列表表头文字颜色
    --info-color: #909399; // 不常用第2次要(等级3)
    --disabled-color: #a8abb2; // 不可用文字颜色
    // 边框颜色
    --border-color: #dcdfe6; // 边框默认颜色
    --border-hover-color: #c0c4cc; // 边框鼠标移入颜色
    --border-disabled-color: #e4e7ed; // 边框不可用颜色
    --border-td-color: #ebeef5; // table的td边框不可用颜色
    // 背景颜色
    --default-bg-color: #f0f2f5;
    --content-bg-color: #fff; // 页面背景颜色
    --pop-layer-bg-color: #fff; // 弹出层背景色
    --current-row-bg-color: #f6f8fb; // 列表选中行的颜色
    --head-bg-color: #f8f8f8; // 表头背景色
    --popover-bg-color: #303133; // 气泡(信息展示)弹出层背景色
    --disabled-bg-color: #f5f7fa; // 不可用背景颜色
    --scrollbar-thumb-color: #ccc; // 滚动条颜色
    // active蓝色
    --active-color: #1993ff;
    --active-disabled-color: #a0cfff; // 蓝色不可用
    --active-hover-color: #79bbff; // 鼠标移入
    --active-border-color: #9eceff; // 蓝色边框色
    --active-bg-color: #ecf5ff; // 蓝色背景色
    // 信息
    --info-disabled-color: #c8c9cc; // 信息文字不可用
    --info-border-color: #c7c9cc; // 信息边框色
    --info-bg-color: #f4f4f5; // 信息背景色
    // danger红色
    --danger-color: #f56c6c;
    --danger-disabled-color: #fab6b6;
    --danger-hover-color: #f89898;
    --danger-border-color: #fab5b5;
    --danger-bg-color: #fef0f0; // 红色背景
    // warning橙色
    --warning-color: #e6a23c;
    --warning-disabled-color: #f3d19e;
    --warning-border-color: #f2d09d;
    --warning-bg-color: #fdf6ec; // 橙色背景
    // success绿色
    --success-color: #67c23a;
    --success-disabled-color: #b3e19d;
    --success-border-color: #b3e09c;
    --success-bg-color: #f0f9eb;
    &.dark {
        // 文字颜色
        --title-color: #e5eaf3; // 标题文字颜色(等级1)
        --default-font-color: #cfd3dc; // 默认文字颜色(等级2)
        --label-color: #cfd3dc; // 表单项标题,列表表头文字颜色
        --info-color: #a3a6ad; // 不常用(等级3)
        --disabled-color: #8d9095; // 不可用文字颜色
        // 边框颜色
        --border-color: #4c4d4f; // 边框默认颜色
        --border-hover-color: #6c6e72; // 边框鼠标移入颜色
        --border-disabled-color: #414243; // 边框不可用颜色
        --border-td-color: #363637; // table的td边框不可用颜色
        --border-dark-color: #636466; // 深一点的边框色
        // 背景颜色
        --default-bg-color: #1d1d1d;
        --content-bg-color: #141414; // 页面背景颜色
        --pop-layer-bg-color: #191919; // 弹出层背景色
        --current-row-bg-color: #202121; // 列表选中行的颜色
        --head-bg-color: #262727; // 表头背景色
        --popover-bg-color: #e5eaf3; // 气泡弹出层背景色
        --disabled-bg-color: #262727; // 不可用背景颜色
        --scrollbar-thumb-color: #434343; // 滚动条颜色
        // active蓝色
        --active-color: #1993ff;
        --active-disabled-color: #2a598a; // 蓝色不可用
        --active-hover-color: #3375b9; // 鼠标移入
        --active-border-color: #2a598a; // 蓝色边框色
        --active-bg-color: #18222c; // 蓝色背景色
        // 信息
        --info-disabled-color: #525457; // 信息文字不可用
        --info-border-color: #525457; // 信息边框色
        --info-bg-color: #202121; // 信息背景色
        // danger红色
        --danger-color: #f56c6c;
        --danger-disabled-color: #854040;
        --danger-hover-color: #f89898;
        --danger-border-color: #854040;
        --danger-bg-color: #2b1d1d; // 红色背景
        // warning橙色
        --warning-color: #e6a23c;
        --warning-disabled-color: #7d5b28;
        --warning-border-color: #7d5b28;
        --warning-bg-color: #292218; // 橙色背景
        // success绿色
        --success-color: #67c23a;
        --success-disabled-color: #3e6b27;
        --success-border-color: #3e6b27;
        --success-bg-color: #1c2518;
    }
}
