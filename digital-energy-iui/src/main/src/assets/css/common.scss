body {
    font-size: 14px;
}
.space {
    padding: 16px;
}
.add-document-dialog {
    .el-dialog__body {
        max-height: 480px;
        overflow-y: auto;
    }
}
.tab-item-content {
    margin-top: 16px;
}

.is-advanced-query {
    .search-input {
        width: 475px;
    }
    .el-input-group__append {
        cursor: pointer;
        padding: 0;
        .el-icon {
            width: 40px;
            height: 32px;
        }
    }
}
.query-advanced-bar {
    background-color: var(--head-bg-color);
    .is-advanced {
        padding: 21px 18px;
        text-align: center;
        .search-input {
            width: 475px;
        }
        .el-input-group__append {
            background-color: var(--active-color);
            cursor: pointer;
            padding: 0;
            .el-icon {
                width: 60px;
                height: 40px;
                font-size: 16px;
                color: #fff;
            }
        }
    }
    .is-common {
        padding: 16px;
    }
}
.uedm-query-form {
    .el-form-item {
        margin-right: 8px;
    }
}
.uedm-split-title {
    font-size: 14px;
    font-weight: bold;
    color: var(--title-color);
    position: relative;
    padding-left: 7px;
    margin-bottom: 16px;
    &::after {
        content: "";
        width: 3px;
        height: 15px;
        position: absolute;
        left: 0;
        top: 2px;
        background-color: #1993ff;
    }
}
.uedm-split-black {
    font-size: 14px;
    font-weight: bold;
    color: var(--title-color);
    position: relative;
    padding-left: 7px;
    margin-bottom: 16px;
    &::after {
        content: "";
        width: 1px;
        height: 15px;
        position: absolute;
        left: 0;
        top: 2px;
        background-color: #333;
    }
}
.blue-block-title {
    height: 15px;
    border-left: 3px solid #07f;
    padding-left: 4px;
    line-height: 15px;
    font-weight: 700;
    color: #333;
    font-size: 14px;
}
.menu {
    .icon-menu {
        display: inline-block;
        width: 16px;
        height: 16px;
        background-position: center;
        background-repeat: no-repeat;
        position: absolute;
        left: 20px;
        top: 17px;
    }
    i.icon-workbench {
        background-image: url('../img/menu_workbench.png');
    }
    i.icon-productManagement {
        background-image: url('../img/menu_productManagement.png');
    }
    i.icon-projectManagement {
        background-image: url('../img/menu_marketProject.png');
    }
    i.icon-knowledgeBase {
        background-image: url('../img/menu_knowledgeBase.png');
    }
    i.icon-billboard {
        background-image: url('../img/menu_board.png');
    }
    i.icon-LTCManagement{
        background-image: url('../img/menu_LTCManagement.png');
    }
}

.faq-detail-drawer-modal {
    .el-drawer {
        & > .el-drawer__body {
            padding: 0;
        }
        & > .el-drawer__header {
            margin-bottom: 0;
        }
    }
}

.blue-link {
    color: #1993ff;
    cursor: pointer;
    .ellipsis-wrap {
        vertical-align: middle;
    }
    &:hover {
        color: #79bbff;
    }
}
.red-link {
    color: #f56c6c;
    cursor: pointer;
    &:hover {
        color: #f89898;
    }
}
.bidding-drawer-modal {
    .el-drawer__body {
        padding-top: 28px;
    }
    .el-drawer__header {
        margin-bottom: 0;
    }
    .el-drawer__title {
        color: #606266;
        font-size: 18px;
    }
}
.el-popper {
    &.pre-wrap {
        white-space: pre-wrap;
    }
}
.footer-button {
    text-align: right;
    margin-right: 16px;
}
.tree-select {
    .el-input {
        width: 100%;
    }
    .el-select-dropdown__wrap {
        max-height: 600px;
    }
    .el-select-dropdown__item {
        padding: 0;
        .el-tree-node__label {
            font-weight: normal;
        }
        > div {
            padding: 10px 16px;
        }
    }
    .el-select-dropdown__item.hover,
    .el-select-dropdown__item.is-hovering,
    .el-select-dropdown__item:hover {
        background-color: transparent;
    }
}
.uedm-scrollbar__width::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}
.form-label {
    color: var(--label-color);
    margin-right: 12px;
}

.uedm-questiontip {
    font-size: 16px;
    color: var(--infoColor);
    cursor: pointer;
    vertical-align: middle;
    margin-left: 2px;
}

/* Started by AICoder, pid:h9321q84ed8cab614fdc0a09008a9b146193222b */
.user-selector-input-wrap .user-selector-input {
    box-sizing: border-box;
    li {
        height: auto!important;
        &.user-selector-editor {
            display: flex;
            align-items: center;
            padding: 0 6px !important;
        }
    }
}

/* Ended by AICoder, pid:h9321q84ed8cab614fdc0a09008a9b146193222b */
