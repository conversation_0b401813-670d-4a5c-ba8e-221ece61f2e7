import { defineStore } from 'pinia';
export const commonStore = defineStore('common', {
    state: () => {
        return {
            height: null,
            isDark: false,
            resize: false,
            userInfo: null,
            configInfo: null,
            jumpParams: null,
            isPageInit: false,
            commentOperationId: '',
            wangEditorMentionIsRegistered: false,
        };
    },
    getters: {
        getHeight: state => state.height,
        getUserInfo: state => state.userInfo,
        getConfigInfo: state => state.configInfo,
        getIsPageInit: state => state.isPageInit,
    },
    actions: {
        setHeight(height) {
            this.height = height;
        },
        setIsDark(isDark) {
            this.isDark = isDark;
        },
        setResize(isResize) {
            this.resize = isResize;
        },
        setUserInfo(info) {
            this.userInfo = info;
        },
        setConfigInfo(info) {
            this.configInfo = info;
        },
        setJumpParams(info) {
            this.jumpParams = info;
        },
        setPageInit() {
            this.isPageInit = !this.isPageInit;
        },
        setCommentOperationId(id) {
            this.commentOperationId = id || '';
        },
    }
});
export default commonStore;