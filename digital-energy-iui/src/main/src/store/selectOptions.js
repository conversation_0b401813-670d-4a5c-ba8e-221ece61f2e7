import { defineStore } from 'pinia';
import HTTP from '@/util/httpService';
const queryOptions = (queryUrl, type, callFn) => {
    HTTP.request(queryUrl, {
        method: type,
        complete: resp => {
            let data = [];
            if (resp.code === 0) {
                data = resp.data || [];
            } else {
                data = [];
            }
            if (callFn) {
                callFn(data);
            }
        },
        error: () => {
            if (callFn) {
                callFn([]);
            }
        },
    });
};
/* Started by AICoder, pid:jad8fue829q82ca14e5b0ae880bff6846bb2a26d */
export const selectOptionsStore = defineStore('selectOptions', {
    state: () => {
        return {
            purchaseModeOptions: [],
            materialStatusOptions: [],
            salesStatusOptions: [],
            productSubCategoryOptions: [],
            projectStageOptions: [],
            brandPurchaseOptions: []
        };
    },
    actions: {
        getPurchaseModeOptions() { // 采购模式
            return new Promise(resolve => {
                queryOptions('queryMaterialPurchaseMode', 'post', data => {
                    this.purchaseModeOptions = data || [];
                    resolve();
                });
            });
        },
        getMaterialStatusOptions() { // 物料状态
            return new Promise(resolve => {
                queryOptions('queryMaterialStatus', 'post', data => {
                    this.materialStatusOptions = data || [];
                    resolve();
                });
            });
        },
        getSalesStatusOptions() { // 销售状态
            return new Promise(resolve => {
                queryOptions('querySalesStatus', 'post', data => {
                    let val = [];
                    if (data && Array.isArray(data)) {
                        data.forEach((d) => {
                            let id = '';
                            let name = '';
                            if (typeof d === 'string') {
                                id = d;
                                name = d;
                            } else {
                                id = d.id;
                                name = d.name;
                            }
                            if (id) {
                                val.push({
                                    id: id,
                                    name: name,
                                });
                            }
                        });
                    }
                    this.salesStatusOptions = val;
                    resolve();
                });
            });
        },
        getProductSubCategory() { // 产品小类下拉
            return new Promise(resolve => {
                queryOptions('getProductCategory', 'get', data => {
                    this.productSubCategoryOptions = data || [];
                    resolve();
                });
            });
        },
        getProjectStageOptions() { // 项目阶段下拉
            return new Promise(resolve => {
                queryOptions('getProjectStage', 'get', data => {
                    this.projectStageOptions = data || [];
                    resolve();
                });
            });
        },
        /* Started by AICoder, pid:9a140fa1c7b894b14a8a0b34f08ca50e11c8adec */
        getBrandPurchaseOptions() { // 采购模式(产品-品牌，不包含生态采购)
            return new Promise(resolve => {
                queryOptions('getPurchasOptions', 'post', data => {
                    this.brandPurchaseOptions = data || [];
                    resolve();
                });
            });
        },
        /* Ended by AICoder, pid:9a140fa1c7b894b14a8a0b34f08ca50e11c8adec */
    }
});
/* Ended by AICoder, pid:jad8fue829q82ca14e5b0ae880bff6846bb2a26d */
export default selectOptionsStore;