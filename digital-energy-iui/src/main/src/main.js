import './util/initGlobal';
import { createApp } from 'vue';
import App from './App.vue';
import router from './router';
import i18n from './util/i18n.js';
import store from './store';
// 修复Echarts中的XSS漏洞
import echartsXSSPolyfill from './util/echartsXSSPolyfill.js';
echartsXSSPolyfill();
// 使用 v-dompurify-html 代替 v-html 解决 xss 问题
import VueDOMPurifyHTML from 'vue-dompurify-html';
import VueBus from './util/vueBus.js';
import ElementUI from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import './assets/plx-icons/style.css';
import './assets/css/theme-default.scss';
import './assets/css/common.scss';
import 'zte-user-selector-plus/index.css';
import './assets/fonts/font.css';
import UserSelector from 'zte-user-selector-plus';
window.i18n = i18n;
const app = createApp(App);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(`ElIcon${key}`, component);
}
app.config.globalProperties.routerAppend = (path, pathToAppend) => {
    return path + (path.endsWith('/') ? '' : '/') + pathToAppend;
};
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
dayjs.locale('zh-cn');
app.use(router);
app.use(i18n);
app.use(store);
app.use(ElementUI);
app.use(VueBus);
app.use(VueDOMPurifyHTML);
app.use(UserSelector, {
    PDMNo: '100000230077',
    isProd: !['developmentbuild', 'development'].includes(import.meta.env.MODE), // 非必填
    lang: 'zh_CN', // 非必填
});
router.isReady().then(() => {
    app.mount('#app');
});

