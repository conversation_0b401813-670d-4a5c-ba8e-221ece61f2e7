<template>
    <el-tooltip
        :disabled="!isOverflow"
        :content="tipContent || text"
        :placement="placement"
        :show-after="openDelay"
        :popper-class="isPreWrap?'pre-wrap':''"
    >
        <span
            ref="ellipsisWrapRef"
            class="ellipsis-wrap"
            :class="{'pre-style':isPreWrap}"
        >
            <span v-if="text" class="text-value">{{ text }}</span>
            <span v-if="text && isOverflow && isPreWrap" class="dot">...</span>
            <slot></slot>
        </span>
    </el-tooltip>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount } from 'vue';
const props = defineProps({
    tipContent: {
        type: String,
        default: ''
    },
    text: {
        type: String,
        default: '',
    },
    placement: {
        type: String,
        default: 'top',
    },
    refreshCounter: {
        type: Number,
        default: 0
    },
    openDelay: {
        type: Number,
        default: 0
    },
    isPreWrap: {
        type: Boolean,
        default: false
    }
});
const isOverflow = ref(false); // 面包屑路径是否有节点隐藏
const ellipsisWrapRef = ref();
const getEllipsis = () => {
    setTimeout(() => {
        if (!ellipsisWrapRef.value) {
            isOverflow.value = false;
        } else {
            if (ellipsisWrapRef.value.offsetWidth < ellipsisWrapRef.value.scrollWidth) {
                isOverflow.value = true;
            }
            if (props.isPreWrap && ellipsisWrapRef.value.offsetHeight < ellipsisWrapRef.value.scrollHeight) {
                isOverflow.value = true;
            }
        }
    }, 10);
};
const resize = function () {
    getEllipsis();
};
watch(
    () => [props.text, props.refreshCounter],
    () => {
        resize();
    }
);
const resizeObserver = new ResizeObserver(() => {
    getEllipsis();
});
onMounted(() => {
    if (ellipsisWrapRef.value && typeof ResizeObserver !== 'undefined') {
        resizeObserver.observe(ellipsisWrapRef.value);
    }
});
onBeforeUnmount(() => {
    if (ellipsisWrapRef.value && typeof ResizeObserver !== 'undefined') {
        resizeObserver.unobserve(ellipsisWrapRef.value);
    }
});
defineExpose({
    resize,
});
</script>
<style lang="scss" scoped>
.ellipsis-wrap {
    display: inline-block;
    width: 100%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.pre-style {
    height: 100%;
    padding-right: 10px;
    .text-value {
        max-width: calc(100% - 20px);
        display: inline-block;
        white-space: pre-line;
    }
    .dot {
        vertical-align: top;
    }
}
</style>
