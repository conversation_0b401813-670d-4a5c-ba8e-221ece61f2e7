<template>
    <div class="file-uploader">
        <el-upload
            ref="uploadRef"
            v-model:file-list="fileList"
            :limit="1"
            :accept="acceptFile"
            :auto-upload="false"
            :on-change="handleChange"
            :on-exceed="handleExceed"
            :show-file-list="false"
            :disabled="uploading"
        >
            <template #trigger>
                <el-button type="primary" :loading="uploading" :disabled="uploading">
                    {{ buttonText || $t('button.upload') }}
                </el-button>
            </template>
            <template #tip>
                <el-tooltip v-if="tip" effect="dark" placement="top">
                    <template #content>
                        <span>{{ tip }}</span>
                    </template>
                    <i class="plx-ico-help-tip-16 uedm-questiontip"></i>
                </el-tooltip>
            </template>
        </el-upload>
    </div>
</template>
<script setup>
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { genFileId } from 'element-plus';
import { showErrMsg } from '@/util/common.js';
const { t } = useI18n();
const emit = defineEmits(['upload']);
const props = defineProps({
    buttonText: {
        type: String,
        default: '',
    },
    fileType: {
        type: Array,
        default: () => ['xlsx', 'xls'],
    },
    tip: {
        type: String,
        default: '',
    },
    uploading: {
        type: Boolean,
        default: false
    }
});
const acceptFile = computed(() => {
    let text = '';
    for (let i = 0; i < props.fileType.length; i++) {
        if (i === 0) {
            text = `.${props.fileType[i]}`;
        } else {
            text += `,.${props.fileType[i]}`;
        }
    }
    return text;
});
const uploadRef = ref(null);
const fileList = ref([]);
const handleChange = (file) => {
    const this_fileType = file.raw.name?.toLowerCase().split('.').pop();
    if (!props.fileType.includes(this_fileType)) {
        showErrMsg(t('updownLoad.tipMessage.fileFormatTip', { type: acceptFile.value }));
        return;
    }
    // if (!file.raw.size > FILE_MAX_SIZE) {
    //     showErrMsg(t('updownLoad.tipMessage.fileSizeTip', { size: FILE_MAX_SIZE }));
    //     uploadRef.value.clearFiles();
    //     return;
    // }
    let formData = new FormData();
    formData.append('file', file.raw);
    emit('upload', formData);
};
const handleExceed = (files) => {
    uploadRef.value.clearFiles();
    const file = files[0];
    file.uid = genFileId();
    uploadRef.value.handleStart(file);
};
const popFileSelect = () => {
    const uploadInput = uploadRef.value?.$el.querySelector('.el-upload__input');
    if (uploadInput) {
        uploadInput.click();
    }
};
defineExpose({
    popFileSelect,
});
</script>