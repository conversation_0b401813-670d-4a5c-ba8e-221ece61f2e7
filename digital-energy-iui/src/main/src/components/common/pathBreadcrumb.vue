<template>
    <ul ref="breadcrumbsRef" class="uedm-breadcrumbs" :class="{ title: title }">
        <li v-if="title">
            <el-link v-if="isShowBack" type="primary" :underline="false" @click="handleBack">
                <el-icon :size="20">
                    <ElIconBack></ElIconBack>
                </el-icon>
            </el-link>
            {{ title }}
        </li>
        <li
            v-for="(item, index) in breadcrumbs"
            :ref="`breadcrumbsItemRef_${item.id}_${index}`"
            :key="`uedmBreadcrumbs_${item.id}_${index}`"
        >
            <el-link v-if="item.isLink" type="primary" :underline="false" @click="handleLinkClick(item)">
                {{ item.name }}
            </el-link>
            <span v-else>{{ item.name }}</span>
        </li>
    </ul>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { Back as ElIconBack } from '@element-plus/icons-vue';
const emit = defineEmits(['back', 'linkClick']);
const props = defineProps({
    title: {
        type: String,
        default: '',
    },
    isShowBack: {
        type: Boolean,
        default: false,
    },
    lists: {
        type: Array,
        default: () => {
            return [];
        },
    },
});
const breadcrumbsRef = ref();
const breadcrumbs = ref([]); // 面包屑路径值的数组
const handleBack = function () {
    emit('back');
};
const initBreadcrumbs = function () {
    let value = [];
    if (Array.isArray(props.lists)) {
        props.lists.forEach((d, index) => {
            let name = '';
            let id = '';
            if (d && d.constructor === Object) {
                name = d.name || '';
                id = d.id || '';
            } else {
                name = d;
            }
            value.push({
                id: id || index + name,
                name: name,
                isLink: d.isLink || false,
            });
        });
    }
    breadcrumbs.value = value;
};
initBreadcrumbs();
const pathNames = computed(() => {
    let names = [];
    breadcrumbs.value.forEach(d => {
        names.push(d.name);
    });
    return names.join(' / ');
});
watch(
    () => props.lists,
    () => {
        initBreadcrumbs();
    },
    { deep: true }
);
const handleLinkClick = item => {
    emit('linkClick', item);
};
</script>
