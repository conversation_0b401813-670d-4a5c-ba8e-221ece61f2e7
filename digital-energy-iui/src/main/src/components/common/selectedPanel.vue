<template>
    <div class="uedm-selected-panel">
        <div class="selected-title">
            <span class="uedm-split-title">
                {{ titleText }}
            </span>
            <div class="selected-title-right">
                <span class="selected-number">
                    {{ selectedText }}
                </span>
                <el-link class="cancelBtn" type="danger" :underline="false" @click="handleClearSelected">
                    {{ $t('button.deselect') }}
                </el-link>
            </div>
        </div>
        <div v-if="maxCount && selected && selected.length > maxCount" style="margin-bottom: 8px">
            <el-alert
                :title="$t('product.material.overSizeCountTips', { count: maxCount })"
                type="warning"
                show-icon
                :closable="false"
            />
        </div>
        <div class="uedm-selectionbox" :style="{ height: `${selectedBoxHeight}px` }">
            <el-tag v-for="item in selected" :key="item.id" type="info" closable @close="handleCloseTag(item)">
                <ellipsis-tooltip :text="item.name" :open-delay="600"></ellipsis-tooltip>
            </el-tag>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import EllipsisTooltip from '@/components/common/ellipsisTooltip.vue';
import { useI18n } from 'vue-i18n';

const props = defineProps({
    selected: {
        type: Array,
        default: () => [],
    },
    totalNumber: {
        type: Number,
        default: 0,
    },
    maxCount: {
        type: Number,
        default: 500,
    },
    height: {
        type: Number,
        default: 0,
    },
    title: {
        type: String,
        default: '',
    },
});

const { t } = useI18n();
const emit = defineEmits(['remove']);
const selectedBoxHeight = computed(() => {
    let h = 200;
    if (props.height) {
        const titleH = 35;
        const overTipH = props.maxCount && props.selected.length > props.maxCount ? 42 : 0;
        h = props.height - titleH - overTipH;
    }
    return h;
});
const titleText = computed(() => {
    let text = t('product.material.selectedMaterial');
    if (props.title) {
        text = props.title;
    }
    return text;
});
/* Started by AICoder, pid:pfafepda4ec0e031428e0834d03d261e35026386 */
const selectedText = computed(() => {
    let number = props.selected.length;
    let total = 0;
    if (props.maxCount) {
        total = props.maxCount;
    }
    if (total) {
        number += `/${total}`;
    }
    let text = t('product.material.selectedCount', { count: number });
    return text;
});
/* Ended by AICoder, pid:pfafepda4ec0e031428e0834d03d261e35026386 */
const handleCloseTag = item => {
    emit('remove', item);
};
const handleClearSelected = () => {
    emit('remove', null);
};
</script>

<style lang="scss" scoped>
.uedm-selected-panel {
    .uedm-split-title {
        margin-bottom: 0;
    }
    .selected-title {
        display: flex;
        margin-bottom: 8px;
        align-items: center;
        .selected-title-right {
            flex: 1;
            display: flex;
            justify-content: space-between;
            margin-left: 8px;
            background-color: var(--current-row-bg-color);
            padding: 4px;
        }
        .selected-number {
            color: var(--title-color);
        }
    }
    :deep(.el-tag__content) {
        max-width: 300px;
    }
}
:deep() .el-alert {
    width: 100%;
    min-width: 100px;
    box-sizing: border-box;
}
.uedm-selectionbox {
    width: 100%;
    max-height: none;
}
</style>