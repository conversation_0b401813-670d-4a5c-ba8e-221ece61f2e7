<template>
    <el-select
        v-model="userValue"
        :placeholder="placeholder || $t('placeholder.input')"
        value-key="accountId"
        filterable
        remote
        :remote-method="remoteMethod"
        :loading="loading"
        :reserve-keyword="reserveKeyword"
        :clearable="clearable"
        :teleported="teleported"
        @change="handleChange"
        @visible-change="visibleChange"
    >
        <el-option
            v-for="item in userOptions"
            :key="item.accountId"
            :label="item.name"
            :value="item"
            class="user-item"
        >
            <span>{{ `${item.name} ${item.accountId}`}}</span>
            <div v-if="item.orgNamePath" class="note">{{ item.orgNamePath }}</div>
        </el-option>
    </el-select>
</template>
<script setup>
import { ref, nextTick } from 'vue';
import debounce from 'lodash/debounce';
import HTTP from '@/util/httpService';
import { DEBOUNCE_TIME } from '@/util/constants.js';
const props = defineProps({
    defaultSelect: { // 不要watch该值重新赋值
        type: Object,
        default: () => {
            return null;
        },
    },
    clearable: {
        type: Boolean,
        default: true,
    },
    placeholder: {
        type: String,
        default: '',
    },
    teleported: {
        type: Boolean,
        default: false,
    },
    reserveKeyword: {
        type: Boolean,
        default: false,
    }
});
const emit = defineEmits(['change']);
const loading = ref(false);
const userValue = ref(null);
const userOptions = ref([]);
let queryCount = 0;
const getUserOptions = (searchText) => {
    const searchKey = searchText.trim();
    if (!searchKey) {
        userOptions.value = [];
        return;
    }
    loading.value = true;
    let count = ++queryCount;
    HTTP.request('queryUacUserInfo', {
        method: 'post',
        data: {
            key: searchKey
        },
        complete: resp => {
            if (count < queryCount) {
                return;
            }
            loading.value = false;
            if (resp.code === 0) {
                userOptions.value = resp.data || [];
            } else {
                userOptions.value = [];
            }
        },
        error: () => {
            if (count < queryCount) {
                return;
            }
            loading.value = false;
            userOptions.value = [];
        },
    });
};
const debounceQuery = debounce(getUserOptions, DEBOUNCE_TIME);
const remoteMethod = query => {
    userOptions.value = [];
    debounceQuery(query);
};
const handleChange = (val) => {
    emit('change', JSON.parse(JSON.stringify(val)));
};
const visibleChange = (val) => {
    if (!val) {
        userOptions.value = [];
    }
};
const clearData = () => {
    userValue.value = null;
};
const init = () => {
    if (props.defaultSelect) {
        const defaultValue = {
            accountId: props.defaultSelect.accountId || props.defaultSelect.employeeId || props.defaultSelect.id,
            name: props.defaultSelect.name
        };
        userValue.value = defaultValue;
        userOptions.value = [defaultValue]; // 不给下拉赋值，标签无法显示
        nextTick(() => {
            userOptions.value = [];
        });
    }
};
init();
defineExpose({
    init,
    clearData,
});
</script>
<style lang="scss" scoped>
    .note {
        color: var(--info-color);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 12px;
    }
    .user-item {
        height: auto;
        line-height: 18px;
        padding: 8px 20px;
    }
</style>
