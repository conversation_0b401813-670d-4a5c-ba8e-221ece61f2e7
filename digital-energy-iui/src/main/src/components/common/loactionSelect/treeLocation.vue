<template>
    <div>
        <div class="uedm-tree__search-wrap">
            <el-input
                v-model="filterText"
                :placeholder="$t('product.material.productClassification')"
                autocomplete="on"
                maxlength="100"
                clearable
                @keyup.enter="handleSearchTree"
                @clear="handleSearchTree"
            >
                <template #append>
                    <el-icon class="el-input__icon el-icon-search" @click="handleSearchTree">
                        <el-icon-search></el-icon-search>
                    </el-icon>
                </template>
            </el-input>
            <el-button
                plain
                class="uedm-button-icon"
                :class="{active: isPinning}"
                style="margin-left: 8px;"
                @click="handlePinning"
            >
                <i class="icon-button plx-ico-pin-f-16"></i>
            </el-button>
        </div>
        <div v-loading="loading" class="uedm-tree__wrap" :style="treeStle">
            <el-tree
                ref="treeRef"
                :data="treeData"
                node-key="id"
                :default-expanded-keys="expandedKeys"
                highlight-current
                :current-node-key="currentId"
                :props="defaultProps"
                :filter-node-method="filterNode"
                :expand-on-click-node="onlyChildrenClick"
                @node-click="handleNodeClick"
            >
                <template #default="{ node }">
                    <span class="tree-node">
                        <span class="el-tree-node__label" :class="{'level2': node.data.productLevel === '2' }">
                            <span :title="node.label" class="nodeEllipsis">{{ forMatLabel(node) }}</span>
                        </span>
                    </span>
                </template>
            </el-tree>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted } from 'vue';
import HTTP from '@/util/httpService.js';
import { useI18n } from 'vue-i18n';
import { showErrMsg, getCookie } from '@/util/common.js';
const emit = defineEmits(['nodeClick', 'updateExpandedNode', 'pinning']);
const { t } = useI18n();
const props = defineProps({
    lists: {
        type: Array,
        default: () => {
            return [];
        },
    },
    height: {
        type: Number,
        default: 0,
    },
    expandedKeys: {
        type: Array,
        default: () => {
            return [];
        },
    },
    hasDefaultKey: {
        type: Boolean,
        default: false,
    },
    defaultNodeId: {
        type: String,
        default: '',
    },
    defaultPinning: {
        type: Boolean,
        default: false,
    },
    cookieKey: {
        type: String,
        default: 'resourceId',
    },
    onlyChildrenClick: {
        type: Boolean,
        default: true,
    }
});
const defaultProps = {
    children: 'children',
    isLeaf: 'isLeaf',
    label: 'name',
};
const treeStle = computed(() => {
    let h = 'auto';
    if (props.height) {
        const paddingH = 12 * 2 + 2;
        const searchH = 40;
        h = `${props.height - paddingH - searchH}px`;
    }
    let item = {
        height: h,
    };
    return item;
});
const isPinning = ref(props.defaultPinning);
const handlePinning = () => {
    isPinning.value = !isPinning.value;
    emit('pinning', isPinning.value);
};
const filterText = ref('');
const treeRef = ref();
const treeData = ref([]);
const loading = ref(false);
const currentId = ref('');
const currentName = ref('');
const currentPathId = ref('');
const getFirstChildNode = (nodes) => {
    let currentPathName = [];
    // 递归函数，用于遍历树结构并找到第一个子节点
    function findFirstChild(node) {
        currentPathName.push(node.name);
        if (node.children && node.children.length > 0) {
            return findFirstChild(node.children[0]);
        } else {
            return {
                id: node.id,
                pathId: node.pathId,
                path: currentPathName,
            };
        }
    }
    if (nodes && Array.isArray(nodes) && nodes.length > 0) {
        return findFirstChild(nodes[0]);
    }
    return null;
};
const getPath = () => {
    // 获取选中节点的全路径名称数组
    let idArray = currentPathId.value.split('/');
    let pathArry = [];
    let getNodeName = (nodes) => {
        if (Array.isArray(nodes)) {
            nodes.forEach(node => {
                if (idArray.includes(node.id)) {
                    pathArry.push(node.name);
                    if (node.children && node.children.length > 0) {
                        getNodeName(node.children);
                    }
                }
            });
        }
    };
    getNodeName(treeData.value);
    return pathArry;
};
const forMatLabel = (node) => {
    const data = node.data;
    let productNo = data.productNo || '';
    return productNo ? `[${data.productNo}]${data.name}` : data.name;
};
/* 查询树数据 */
let queryCount = 0;
const queryTreeData = function () {
    loading.value = true;
    let count = ++queryCount;
    HTTP.request('getAllProductCategoryTree', {
        method: 'get',
        complete: resp => {
            if (count < queryCount) {
                return;
            }
            loading.value = false;
            if (resp.code === 0) {
                treeData.value = resp.data || [];
                if (!currentId.value) {
                    const defaultId = props.cookieKey ? getCookie(props.cookieKey) : '';
                    let pathArry = [];
                    treeRef.value.setCurrentKey(null);
                    nextTick(() => {
                        if (props.defaultNodeId) {
                            currentId.value = props.defaultNodeId;
                        } else if (props.hasDefaultKey && defaultId && defaultId !== 'undefined' && treeRef.value && treeRef.value.getNode(defaultId)) { // 如果有缓存，则设置缓存节点默认选中
                            currentId.value = defaultId;
                        } else { // 设置第一个子节点为默认选中节点
                            const firstNode = getFirstChildNode(resp.data);
                            currentId.value = firstNode?.id || '';
                        }
                        treeRef.value.setCurrentKey(currentId.value);
                        const currentNode = treeRef.value && treeRef.value.getNode(currentId.value) || null;
                        if (currentNode) {
                            currentPathId.value = currentNode.data.pathId;
                            currentName.value = currentNode.data.name;
                            pathArry = getPath();
                        }
                        emit('updateExpandedNode', currentPathId.value);
                        emit('nodeClick', currentId.value, currentName.value, currentPathId.value, pathArry, Boolean(currentNode.data.children?.length === 0));
                    });
                }
            } else {
                showErrMsg(resp);
                treeData.value = [];
            }
        },
        error: () => {
            if (count < queryCount) {
                return;
            }
            loading.value = false;
            treeData.value = [];
            showErrMsg(t('tipMessage.networkError'));
        },
    });
};
const handleSearchTree = () => {
    if (treeRef.value) {
        treeRef.value.filter(filterText.value);
    }
};
const filterNode = (value, data) => {
    if (!value) {
        return true;
    }
    const nodeName = data.name.toLowerCase();
    return nodeName.includes(value.trim().toLowerCase());
};
const handleNodeClick = (d) => {
    if (props.onlyChildrenClick && d.children && d.children.length > 0) { // 不是子节点不能点击选中
        treeRef.value.setCurrentKey(null);
        treeRef.value.setCurrentKey(currentId.value);
    } else {
        currentId.value = d.id;
        currentName.value = d.name;
        currentPathId.value = d.pathId;
        emit('nodeClick', currentId.value, currentName.value, currentPathId.value, getPath(), Boolean(d.children?.length === 0));
    }
};
const selectNode = (id) => {
    const currentNode = treeRef.value.getNode(id);
    if (currentNode) {
        handleNodeClick(currentNode.data);
    }
};
onMounted(() => {
    queryTreeData();
});
defineExpose({
    selectNode
});
</script>
<style lang="scss" scoped>
.uedm-tree__search-wrap {
    :deep(.el-input) {
        width: calc(100% - 43px)!important;
    }
}
.active .icon-button{
    color: var(--active-color);
}

.level2 {
    color: var(--info-color);
}
</style>
