<template>
    <div class="side-wrap" :class="{ 'hide-left': hideLeft }">
        <div ref="rightSide" class="side-right" :style="sideRightStyle">
            <slot name="right"></slot>
        </div>
        <div ref="leftSideRef" class="side-left" :style="sideLeftStyle">
            <div class="toggle-button" :class="{ 'is-active': show }" @click="toggle"></div>
            <div
                v-show="!show || isShowSplit"
                class="split"
                :class="{'hide': show}"
                @mousedown="handleMousedown"
            ></div>
            <div v-show="!show">
                <slot name="left"></slot>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, computed } from 'vue';
// eslint-disable-next-line no-undef
const props = defineProps({
    leftSideWidth: {
        type: Number,
        default: 226,
    },
    hideLeft: {
        type: Boolean,
        default: false,
    },
    viewH: {
        type: Number,
        default: 0,
    },
    paddingRight: { // 右侧区域右边距
        type: Number,
        default: 0,
    },
    leftPaddingRight: { // 左侧区域右边距
        type: Number,
        default: 0,
    },
    isShowSplit: { // 收折时是否显示分隔线
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(['styleControl']);
const leftSideRef = ref();
const show = ref(false);
let startX = 0;
let dragging = false;
let paddingLeft = props.leftPaddingRight + 1; // 1为中间分隔竖线占位需要空出来
const width = ref(props.leftSideWidth);
let startWidth = 0;
const sideLeftStyle = computed(() => {
    let height = props.viewH ? props.viewH : window.innerHeight;
    return {
        width: `${width.value}px`,
        height: `${height}px`,
        paddingRight: `${paddingLeft}px`,
    };
});
const sideRightStyle = computed(() => {
    let height = props.viewH ? props.viewH : window.innerHeight;
    return {
        marginLeft: `${width.value + paddingLeft}px`,
        marginRight: 0,
        paddingRight: `${props.paddingRight}px`,
        height: `${height}px`,
    };
});
const toggle = function () {
    show.value = !show.value;
    if (!show.value) {
        paddingLeft = props.leftPaddingRight + 1; // 1为中间分隔竖线占位需要空出来
        width.value = props.leftSideWidth;
    } else {
        width.value = 0;
        paddingLeft = 0;
    }
    emit('styleControl', show);
};
const onDragging = function (event) {
    if (dragging) {
        let diff = 0;
        let currentX = event.clientX;
        diff = currentX - startX;
        width.value = Math.min(Math.max(100, startWidth + diff), 500);
    }
};
const onDragStart = function (event) {
    dragging = true;
    startX = event.clientX;
    startWidth = leftSideRef.value.getBoundingClientRect().width - 15;
};
const onDragEnd = function () {
    dragging = false;
    window.removeEventListener('mousemove', onDragging);
    window.removeEventListener('mouseup', onDragEnd);
    emit('styleControl', true);
};
const handleMousedown = function (event) {
    event.preventDefault();
    onDragStart(event);
    window.addEventListener('mousemove', onDragging);
    window.addEventListener('mouseup', onDragEnd);
};
</script>

<style lang="scss" scoped>
.side-wrap {
    position: relative;
}
.toggle-button {
    background: url('../../assets/img/split_fold.png') no-repeat left center;
    position: absolute;
    display: inline-block;
    width: 12px;
    height: 62px;
    color: #fff;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
    text-align: center;
    top: 50%;
    transform: translateY(-50%);
    left: 100%;
    padding-top: 16px;
    cursor: pointer;
    pointer-events: auto;
    z-index: 99;
    &:hover {
        background-image: url('../../assets/img/split_fold_hover.png');
    }
    &:focus:not(:active, :hover) {
        background-image: url('../../assets/img/split_fold_click.png');
    }
}
.toggle-button.is-active {
    background-image: url('../../assets/img/split_unfold.png');
    &:hover {
        background-image: url('../../assets/img/split_unfold_hover.png');
    }
    &:focus:not(:active, :hover) {
        background-image: url('../../assets/img/split_unfold_click.png');
    }
}
.split {
    width: 3px;
    height: 100%;
    border-left: solid 1px #e4e7ed;
    position: absolute;
    right: -3px;
    top: 0;
    background: transparent;
    &.hide {
        right: -4px;
    }
}

.split:hover {
    cursor: col-resize;
}
.side-left {
    position: absolute;
    top: 0;
    z-index: 101;
    background-color: #fff;
}
.hide-left {
    .side-left {
        display: none;
        width: 0;
    }

    .side-right {
        margin-left: 0 !important;
    }

    .split {
        display: none;
    }
}
html.dark {
    .side-left {
        background-color: #141414;
    }
    .split {
        border-color: #414243;
        background: transparent;
    }
    .toggle-button {
        background-image: url('../../assets/img/split_fold_dark.png');
        &:hover {
            background-image: url('../../assets/img/split_fold_hover_dark.png');
        }
        &:focus:not(:active, :hover) {
            background-image: url('../../assets/img/split_fold_click_dark.png');
        }
    }
    .toggle-button.is-active {
        background-image: url('../../assets/img/split_unfold_dark.png');
        &:hover {
            background-image: url('../../assets/img/split_unfold_hover_dark.png');
        }
        &:focus:not(:active, :hover) {
            background-image: url('../../assets/img/split_unfold_click_dark.png');
        }
    }
}
</style>
