/* Started by AICoder, pid:v5c60a4202l261c14b2c08353245d53844d196b5 */
<template>
    <div ref="containerRef" class="ellipsis-text" :style="{ 'text-align': align }">
        <slot>{{ text }}</slot>
    </div>
</template>

<script setup>
import { ref, h, render, onMounted, onBeforeUnmount } from 'vue';
import { ElTooltip, ElScrollbar } from 'element-plus';
import { buildVueDompurifyHTMLDirective } from 'vue-dompurify-html';

let removePopper = null;

const props = defineProps({
    tooltipOptions: {
        type: Object,
        default: () => ({}),
    },
    placement: {
        type: String,
        default: 'top',
    },
    text: {
        type: String,
        default: '',
    },
    align: {
        type: String,
        default: 'left',
    },
    tipsWidth: {
        type: Number,
        default: 400,
    },
    tipsHeight: {
        type: Number,
        default: 100,
    },
    rawContent: {
        type: Boolean,
        default: false,
    },
    tipContent: {
        type: String,
        default: '',
    },
    tipDisable: {
        type: Boolean,
        default: null,
    },
});

const containerRef = ref(null);

const isGreaterThan = (a, b, epsilon = 0.01) => {
    return a - b > epsilon;
};

const getPadding = el => {
    const style = window.getComputedStyle(el, null);
    const paddingLeft = Number.parseInt(style.paddingLeft, 10) || 0;
    const paddingRight = Number.parseInt(style.paddingRight, 10) || 0;
    const paddingTop = Number.parseInt(style.paddingTop, 10) || 0;
    const paddingBottom = Number.parseInt(style.paddingBottom, 10) || 0;
    return {
        left: paddingLeft,
        right: paddingRight,
        top: paddingTop,
        bottom: paddingBottom,
    };
};

const isTextOverflowing = element => {
    const range = document.createRange();
    range.setStart(element, 0);
    range.setEnd(element, element.childNodes.length);

    let { width: rangeWidth, height: rangeHeight } = range.getBoundingClientRect();
    const offsetWidth = rangeWidth - Math.floor(rangeWidth);
    const epsilon = 0.001;
    if (offsetWidth < epsilon) {
        rangeWidth = Math.floor(rangeWidth);
    }
    const { width: elementWidth, height: elementHeight } = element.getBoundingClientRect();
    const offsetHeight = rangeHeight - Math.floor(rangeHeight);
    if (offsetHeight < epsilon) {
        rangeHeight = Math.floor(rangeHeight);
    }

    const { top, left, right, bottom } = getPadding(element);
    const horizontalPadding = left + right;
    const verticalPadding = top + bottom;

    return (
        isGreaterThan(rangeWidth + horizontalPadding, elementWidth) ||
        isGreaterThan(rangeHeight + verticalPadding, elementHeight) ||
        isGreaterThan(element.scrollWidth, elementWidth, 0.5)
    );
};

const createTooltip = (content, trigger, parent = document.body, options = {}) => {
    if (removePopper?.trigger === trigger) {
        return;
    }

    removePopper?.();

    const popperOptions = {
        strategy: 'fixed',
        ...options.popperOptions,
    };

    const vm = h(
        ElTooltip,
        {
            content,
            rawContent: props.rawContent,
            virtualTriggering: true,
            virtualRef: trigger,
            appendTo: parent,
            placement: props.placement,
            transition: 'none',
            offset: 0,
            hideAfter: 0,
            popperClass: 'ellipsis-text-tooltip-popper',
            ...options,
            popperOptions,
            onHide: () => {
                removePopper?.();
            },
        },
        {
            content: () => {
                if (props.rawContent) {
                    return h(
                        ElScrollbar,
                        {
                            wrapStyle: { padding: 0 },
                            maxHeight: `${props.tipsHeight}px`,
                        },
                        {
                            default: () => [
                                h(
                                    'div',
                                    {
                                        style: {
                                            maxWidth: `${props.tipsWidth}px`,
                                            margin: '5px 11px',
                                        },
                                    },
                                    {
                                        default: () => {
                                            const vdompurifyHtml = buildVueDompurifyHTMLDirective();
                                            return [withDirectives(h('span'), [[vdompurifyHtml, content]])];
                                        },
                                    }
                                ),
                            ],
                        }
                    );
                } else {
                    return h(
                        ElScrollbar,
                        {
                            wrapStyle: { padding: 0 },
                            maxHeight: `${props.tipsHeight}px`,
                        },
                        {
                            default: () => [
                                h(
                                    'div',
                                    {
                                        style: {
                                            maxWidth: `${props.tipsWidth}px`,
                                            margin: '5px 11px',
                                        },
                                    },
                                    content
                                ),
                            ],
                        }
                    );
                }
            },
        }
    );

    const container = document.createElement('div');
    render(vm, container);
    vm.component.exposed.onOpen();

    removePopper = () => {
        render(null, container);
        removePopper = null;
    };

    removePopper.trigger = trigger;
};

const handleMouseEnter = () => {
    const containerEl = containerRef.value;
    if (isTextOverflowing(containerEl)) {
        const tooltipContent = props.text || containerEl.innerText || containerEl.textContent || '';
        createTooltip(tooltipContent, containerEl, document.body, props.tooltipOptions);
    }
};

const closeTooltip = () => {
    removePopper?.();
};

onMounted(() => {
    const containerEl = containerRef.value;
    containerEl.addEventListener('mouseenter', handleMouseEnter);
});

onBeforeUnmount(() => {
    const containerEl = containerRef.value;
    containerEl.removeEventListener('mouseenter', handleMouseEnter);
});
defineExpose({ closeTooltip });
</script>

<style scoped>
.ellipsis-text {
    width: 100%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
</style>
<style>
.ellipsis-text-tooltip-popper {
    padding: 0 !important;
}
</style>

/* Ended by AICoder, pid:v5c60a4202l261c14b2c08353245d53844d196b5 */