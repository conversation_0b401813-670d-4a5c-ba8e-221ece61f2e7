/* Started by AICoder, pid:l4a3306139h117914f230813247d9a408f704f08 */
<template>
    <el-tooltip :content="pathNames" :placement="placement" :disabled="!isOverflow">
        <ul ref="breadcrumbsRef" class="uedm-breadcrumbs" :class="{ description: description }">
            <li v-if="description" ref="descriptionRef">
                {{ formatTitleName(description) }}:
            </li>
            <template v-for="(item, index) in breadcrumbs">
                <li
                    v-if="item.isShow"
                    :ref="`breadcrumbsItemRef_${item.id}_${index}`"
                    :key="`uedmBreadcrumbs_${item.id}_${index}`"
                    :class="{'last-child': index === breadcrumbs.length - 1}"
                    @click.stop="showPopover"
                >
                    <el-tooltip :disabled="isOverflow || !item.isEllipsis" :content="item.name" :placement="placement">
                        <span>{{ item.text }}</span>
                    </el-tooltip>
                </li>
            </template>
            <li class="arrow">
                <el-popover
                    ref="popoverRef"
                    placement="bottom"
                    :visible="popoverVisible"
                    width="auto"
                    popper-style="padding: 10px 0px; maxHeight: 450px; overflowY: auto"
                >
                    <div
                        v-for="path in lists"
                        :key="path.id"
                        class="select-item"
                        :class="{'active': path.id === selectedId}"
                        @click="handleSelect(path)"
                    >
                        {{ path.pathName }}
                    </div>
                    <template #reference>
                        <div
                            v-click-outside:[popperPanRef]="onClickOutside"
                            class="location-item"
                            @click.stop="showPopover"
                        >
                            <el-icon><ElIconCaretBottom /></el-icon>
                        </div>
                    </template>
                </el-popover>
            </li>
        </ul>
    </el-tooltip>
</template>

<script setup>
import { ref, watch, computed, onMounted, onBeforeUnmount, getCurrentInstance, nextTick } from 'vue';
import { ClickOutside as vClickOutside } from 'element-plus';
const emit = defineEmits(['linkClick']);
const props = defineProps({
    description: {
        type: String,
        default: '',
    },
    lists: {
        type: Array,
        default: () => {
            return [];
        },
    },
    ellipsisTitleNumber: {
        // 超出截取字符数
        type: Number,
        default: 20,
    },
    ellipsisNumber: {
        // 超出截取字符数
        type: Number,
        default: 20,
    },
    showPreviousNumber: {
        // 前面固定显示个数
        type: Number,
        default: 2,
    },
    hideType: {
        type: String,
        default: 'auto', // middle中间全省，auto只省略放不下的
    },
    width: {
        // 传入路径总宽度
        type: Number,
        default: 0,
    },
    minusWidth: { // 需要额外减去的宽度
        type: Number,
        default: 0,
    },
    placement: {
        type: String,
        default: 'top',
    },
    ellipsisSpace: { // 中间节点省略宽度
        type: Number,
        default: 33,
    }
});
const { proxy } = getCurrentInstance();
const breadcrumbsRef = ref();
const breadcrumbs = ref([]); // 面包屑路径值的数组
const isOverflow = ref(false); // 面包屑路径是否有节点隐藏
const breadcrumbsWidth = ref(0); // 面包屑路径的实际总宽度
const totalWidth = ref(0); // 放面包屑路径的外层宽度
const descriptionRef = ref();
const lastNodeShowAll = ref(true); // 最后一个节点是否完整显示
let hasUpdate = true; // 面包屑是否更新完成，防止因为高度变化重复resize
let resizeWidth = 0;
const formatTitleName = function (name) {
    let text = name;
    if (name.length > props.ellipsisTitleNumber) {
        text = `${name.substring(0, props.ellipsisTitleNumber)}...`;
    }
    return text;
};
const formatName = function (name, index) {
    let text = name;
    if (!(lastNodeShowAll.value && index === props.lists.length - 1) && name.length > props.ellipsisNumber) {
        text = `${name.substring(0, props.ellipsisNumber)}...`;
    }
    return text;
};
const selectedId = ref('');
const getCurrentPath = (path) => {
    let val = [];
    if (path && path.id) {
        selectedId.value = path.id;
        val = path.pathName.split(`/${path.productName}`);
        if (val.length) {
            val[val.length - 1] = path.productName;
        }
    }
    return val;
};
const pathNames = computed(() => {
    let names = [];
    breadcrumbs.value.forEach(d => {
        names.push(d.name);
    });
    return names.join(' / ');
});
const initBreadcrumbs = function (pathLists) {
    let value = [];
    lastNodeShowAll.value = true;
    isOverflow.value = false;
    if (Array.isArray(pathLists)) {
        pathLists.forEach((d, index) => {
            let name = '';
            let id = '';
            if (d && d.constructor === Object) {
                name = d.name || '';
                id = d.id || '';
            } else {
                name = d;
            }
            let isEllipsis = false;
            let text = formatName(name, index);
            if (index !== props.lists.length - 1 && name.length > props.ellipsisNumber) {
                isEllipsis = true;
            }
            value.push({
                id: id || index + name,
                text: text,
                name: name,
                isLink: d.isLink || false,
                isEllipsis: isEllipsis,
                width: 0,
                isShow: true,
            });
        });
    }
    breadcrumbs.value = value;
    emit('linkClick', selectedId.value, pathNames.value);
};
initBreadcrumbs(getCurrentPath(props.lists[0]));
const setWidth = function () {
    breadcrumbsWidth.value = 0;
    breadcrumbs.value.forEach((item, index) => {
        const domRef = proxy.$refs[`breadcrumbsItemRef_${item.id}_${index}`];
        if (domRef && domRef[0]) {
            let itemW = domRef[0].offsetWidth + 1;
            item.width = itemW;
            breadcrumbsWidth.value += itemW;
        }
    });
};
const updateLastTextStatus = function (isShowAll) {
    lastNodeShowAll.value = isShowAll;
    if (breadcrumbs.value.length === 0) {
        return;
    }
    let lastIndex = breadcrumbs.value.length - 1;
    breadcrumbs.value[lastIndex].text = formatName(breadcrumbs.value[lastIndex].name, lastIndex);
    breadcrumbs.value[lastIndex].isEllipsis = true;
    if (!isShowAll && breadcrumbs.value[lastIndex].name.length > props.ellipsisNumber) {
        breadcrumbs.value[lastIndex].isEllipsis = true;
    } else {
        breadcrumbs.value[lastIndex].isEllipsis = false;
    }
};
const updateTextStatus = function () {
    lastNodeShowAll.value = true;
    isOverflow.value = false;
    breadcrumbs.value.forEach((item, index) => {
        let isEllipsis = false;
        let text = formatName(item.name, index);
        if (index !== breadcrumbs.value.length - 1 && item.name.length > props.ellipsisNumber) {
            isEllipsis = true;
        }
        item.text = text;
        item.isEllipsis = isEllipsis;
        item.isShow = true;
    });
};
const showMiddleHidden = function () {
    let hasEllipsis = false;
    breadcrumbs.value.forEach((item, index) => {
        if (
            (props.showPreviousNumber > 0 && index <= props.showPreviousNumber - 1) ||
            index === breadcrumbs.value.length - 1
        ) {
            item.isShow = true;
            item.text = formatName(item.name, index);
        } else {
            if (!hasEllipsis) {
                item.isShow = true;
                item.text = '...';
                hasEllipsis = true;
            } else {
                item.isShow = false;
                item.text = '';
            }
        }
    });
};
const showMiddleAuto = function () {
    let hasEllipsis = false;
    let lastIndex = breadcrumbs.value.length - 1;
    let currentTotalLen = breadcrumbs.value[lastIndex].width + props.ellipsisSpace;
    lastIndex--;
    breadcrumbs.value.forEach((item, index) => {
        currentTotalLen += item.width;
        if ((props.showPreviousNumber > 0 && index <= props.showPreviousNumber - 1) || index > lastIndex) {
            item.isShow = true;
            item.text = formatName(item.name, index);
        } else if (currentTotalLen > totalWidth.value) {
            if (!hasEllipsis) {
                item.isShow = true;
                item.text = '...';
                hasEllipsis = true;
            } else {
                item.isShow = false;
            }
        } else {
            item.isShow = true;
            item.text = formatName(item.name, index);
            let lastItem = breadcrumbs.value[lastIndex];
            currentTotalLen += lastItem.width;
            if (currentTotalLen <= totalWidth.value) {
                lastIndex--;
            }
        }
    });
};
const updateShow = function (isSecond) {
    let w = breadcrumbsRef.value.offsetWidth;
    if (descriptionRef.value) {
        w = w - descriptionRef.value.offsetWidth;
    }
    if (props.width) {
        w = props.width;
    }
    if (props.minusWidth) {
        w = w - props.minusWidth;
    }
    const arrowW = 14; // 下拉按钮宽度
    totalWidth.value = w - arrowW;
    if (breadcrumbsWidth.value <= w) {
        hasUpdate = true;
        return;
    }
    let minW = props.ellipsisSpace;
    breadcrumbs.value.forEach((item, index) => {
        if (
            (props.showPreviousNumber > 0 && index <= props.showPreviousNumber - 1) ||
            index === breadcrumbs.value.length - 1
        ) {
            minW += item.width;
        }
    });
    if (!isSecond && minW > w) {
        updateLastTextStatus(false);
        updateBreadcrumbsSize(true);
        return;
    }
    isOverflow.value = true;
    if (props.hideType === 'middle') {
        showMiddleHidden();
    } else if (props.hideType === 'auto') {
        showMiddleAuto();
    }
    setTimeout(() => {
        hasUpdate = true;
    }, 10);
};
function updateBreadcrumbsSize(isSecond) {
    nextTick(() => {
        setWidth();
        updateShow(isSecond);
    });
}
watch(
    () => props.lists,
    val => {
        if (val.length) {
            initBreadcrumbs();
            updateBreadcrumbsSize();
        }
    }
);
const resize = function () {
    if (hasUpdate) {
        hasUpdate = false;
        updateTextStatus();
        updateBreadcrumbsSize();
    }
};
watch(
    () => [props.title, props.width, props.minusWidth],
    () => {
        resize();
    }
);
const resizeObserver = new ResizeObserver((entries) => {
    entries.forEach(entry => {
        const width = entry.borderBoxSize[0].inlineSize;
        if (width !== resizeWidth) {
            resize();
            resizeWidth = width;
        }
    });
});
onMounted(() => {
    if (breadcrumbsRef.value && typeof ResizeObserver !== 'undefined') {
        resizeObserver.observe(breadcrumbsRef.value);
    }
});
onBeforeUnmount(() => {
    if (breadcrumbsRef.value && typeof ResizeObserver !== 'undefined') {
        resizeObserver.unobserve(breadcrumbsRef.value);
    }
});
const popoverRef = ref();
const popperPanRef = computed(() => {
    return popoverRef.value?.popperRef?.contentRef;
});
const popoverVisible = ref(false);
const showPopover = () => {
    popoverVisible.value = true;
};
const onClickOutside = function () {
    popoverVisible.value = false;
};
const handleSelect = (item) => {
    initBreadcrumbs(getCurrentPath(item));
    resize();
    popoverVisible.value = false;
};
defineExpose({
    resize,
    handleSelect,
});
</script>
<style lang="scss" scoped>
    .uedm-breadcrumbs {
        &.description li:first-child {
            font-size: 14px;
            padding-right: 0;
        }
        li {
            font-size: 14px;
            cursor: pointer;
        }
        li.last-child {
            font-size: 14px;
            color: var(--title-color);
            font-weight: bold;
        }
        li.arrow {
            padding: 0;
            cursor: pointer;
            vertical-align: middle;
            &::before {
                display: none;
            }
        }
    }
    .select-item {
        line-height: 18px;
        padding: 8px 16px;
        cursor: pointer;
        &:hover {
            background-color: var(--disabled-bg-color);
        }
        &.active {
            color: var(--active-color);
        }
    }
</style>
/* Ended by AICoder, pid:l4a3306139h117914f230813247d9a408f704f08 */
