/* Started by AICoder, pid:s48dbq0f20s8725140ad0aa141d6e4019b58c78b */
<template>
    <div class="customize">
        <el-dialog v-model="dialogVisible" width="386" :before-close="handleClose" :show-close="false">
            <div class="headers">
                <span style="letter-spacing: -0.71px">{{ t('tipMessage.evaluation') }}</span>
                <img :src="icon_cai1_bgc" alt="" @click="handleDislike" style="margin-left: 12px" />
            </div>

            <el-input
                v-model="suggestion"
                maxlength="200"
                :placeholder="t('tipMessage.reasonDislike')"
                show-word-limit
                type="textarea"
            />
            <template #footer>
                <el-button style="margin-right: 8px" type="primary" @click="submitSuggestion">
                    {{ t('button.confirm') }}
                </el-button>
                <el-button @click="handleClose">{{ t('button.cancel') }}</el-button>
            </template>
        </el-dialog>
    </div>
</template>

<script setup>
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import HTTP from '@/util/httpService.js';
import { showErrMsg, showSuccessMsg } from '@/util/common.js';
import icon_cai1_bgc from '@/assets/img/icon_cai1_bgc.png';

const { t } = useI18n();
const emit = defineEmits(['update:visible']);

const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    functionId: {
        type: String,
        default: '',
    },
});

const suggestion = ref('');

const handleClose = () => {
    suggestion.value = '';
    emit('close');
};

const submitSuggestion = () => {
    HTTP.request('doEvaluationEval', {
        method: 'post',
        data: {
            functionId: props.functionId,
            operationType: 1,
            evalContent: suggestion.value,
        },
        complete: data => {
            if (data.code === 0) {
                emit('success');
                suggestion.value = '';
                showSuccessMsg(data.message);
            } else {
                showErrMsg(data.message);
            }
        },
        error: err => {
            showErrMsg(err.message);
        },
    });
};

const dialogVisible = computed({
    get: () => props.visible,
    set: value => {
        emit('update:visible', value);
    },
});
</script>

<style lang="scss" scoped>
.customize {
    .headers {
        display: flex;
        align-items: center;
        margin-bottom: 11px;
    }
    :deep(.el-dialog) {
        padding: 18px 16px 16px 32px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
        box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.15);
    }
    :deep(.el-dialog .el-dialog__header) {
        padding: 0;
    }
    :deep(.el-textarea__inner) {
        min-height: 93px !important;
    }
    :deep(.el-button) {
        padding: 6px 16px;
    }
}
</style>
/* Ended by AICoder, pid:s48dbq0f20s8725140ad0aa141d6e4019b58c78b */
