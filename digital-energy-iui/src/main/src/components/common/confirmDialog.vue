<template>
    <!-- Started by AICoder, pid:22ff189b9ck271a14d1b0a722043254299503363 -->
    <el-dialog
        v-model="dialogVisible"
        :title="dialogTitle"
        :show-close="isShowTitle"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :class="isShowTitle ? '' : 'hidden'"
        :width="width || `${420}px`"
        :append-to-body="appendBody"
    >
        <div class="confirm-content">
            <el-icon v-if="type === 'warning'" :size="21" color="#e6a23c" class="confirm-icon">
                <ElIconWarningFilled />
            </el-icon>
            <el-icon v-else-if="type === 'error'" :size="21" color="#f56c6c" class="confirm-icon">
                <ElIconCircleCloseFilled />
            </el-icon>
            <el-icon v-else-if="type === 'info'" :size="21" color="#409eff" class="confirm-icon">
                <ElIconInfoFilled />
            </el-icon>
            <p class="confirm-info">
                {{ content }}
            </p>
            <p v-if="description" class="confirm-des">
                {{ description }}
            </p>
            <slot name="content"></slot>
        </div>
        <template #footer>
            <span class="dialog-footer">
                <slot name="footerbutton"></slot>
                <el-button v-if="showConfirmButton" :type="confirmButtonType" @click="handleConfirm">
                    {{ dialogConfirmButtonText }}
                </el-button>
                <el-button v-if="showCancelButton" :type="cancelButtonType" @click="handleCancel">
                    {{ dialogCancelButtonText }}
                </el-button>
            </span>
        </template>
    </el-dialog>
    <!-- Ended by AICoder, pid:22ff189b9ck271a14d1b0a722043254299503363 -->
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { WarningFilled as ElIconWarningFilled, CircleCloseFilled as ElIconCircleCloseFilled, InfoFilled as ElIconInfoFilled } from '@element-plus/icons-vue';
// eslint-disable-next-line no-undef
const props = defineProps({
    width: {
        type: Number,
        default: 420,
    },
    visible: {
        type: Boolean,
        default: false,
    },
    appendBody: {
        type: Boolean,
        default: true,
    },
    isShowTitle: {
        type: Boolean,
        default: false,
    },
    type: {
        type: String,
        default: 'warning',
    },
    title: {
        type: String,
        default: '',
    },
    content: {
        type: String,
        default: '',
    },
    description: {
        type: String,
        default: '',
    },
    confirmButtonText: {
        type: String,
        default: '',
    },
    cancelButtonText: {
        type: String,
        default: '',
    },
    showConfirmButton: {
        type: Boolean,
        default: true,
    },
    showCancelButton: {
        type: Boolean,
        default: true,
    },
    confirmButtonType: {
        type: String,
        default: 'primary',
    },
    cancelButtonType: {
        type: String,
        default: 'default',
    },
});
const emit = defineEmits(['update:visible', 'confirm', 'cancel']);
const { t } = useI18n();
const dialogVisible = computed({
    get: () => {
        return props.visible;
    },
    set: value => {
        emit('update:visible', value);
    },
});
const dialogTitle = computed(() => {
    return props.title ? props.title : t('button.tooltip');
});
const dialogConfirmButtonText = computed(() => {
    return props.confirmButtonText ? props.confirmButtonText : t('button.confirm');
});
const dialogCancelButtonText = computed(() => {
    return props.cancelButtonText ? props.cancelButtonText : t('button.cancel');
});
const handleConfirm = function () {
    setTimeout(() => {
        emit('confirm');
    }, 100);
    dialogVisible.value = false;
};
const handleCancel = function () {
    emit('cancel');
    dialogVisible.value = false;
};
</script>
<style lang="scss" scoped>
.confirm-content {
    padding-left: 33px;
    position: relative;
    padding-top: 12px;
}
.confirm-icon {
    position: absolute;
    left: 0;
    top: 12px;
}
.confirm-info {
    font-size: 16px;
    font-weight: bold;
    color: var(--title-color);
}
.confirm-des {
    margin-top: 12px;
}
</style>
