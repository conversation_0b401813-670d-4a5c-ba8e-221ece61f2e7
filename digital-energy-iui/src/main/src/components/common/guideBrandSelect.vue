<template>
    <el-popover
        :visible="visibleGuideBrandSelect"
        placement="bottom"
        popper-style="padding: 0;"
        :width="popoverWidth"
        popper-class="guide-brand-select-popover"
    >
        <div class="guide-brand-select-list">
            <div v-for="item in guideBrandSelectList" :key="item.id" @click="handleGuideBrandSelect(item)">
                {{ item.label }}
            </div>
        </div>
        <template #reference>
            <input-tag
                ref="guideBrandInputRef"
                v-model="guidBrandValue"
                :max-label="guideBrandMaxLabel"
                :placeholder="$t('product.brand.dynamicTagsPlaceholder')"
                wrap-class="guide-brand-select-popover"
                :disabled="!productCategoryId"
                @input-change="guideBrandInputChange"
                @max-exceeded="maxExceeded"
                @change="handleChange"
            ></input-tag>
        </template>
    </el-popover>
</template>
<script setup>
import { ref, watch, computed } from 'vue';
import HTTP from '@/util/httpService.js';
import { useI18n } from 'vue-i18n';
import InputTag from '@/components/common/inputTag.vue';
import { showErrMsg } from '@/util/common.js';
const emit = defineEmits(['update:modelValue', 'change']);
const { t } = useI18n();
const props = defineProps({
    modelValue: {
        type: Array,
        default: () => {
            return [];
        },
    },
    maxLabel: {
        type: Number,
        default: 20,
    },
    productCategoryId: {
        type: String,
        default: '',
    },
    width: {
        type: Number,
        default: 0,
    }
});
const guidBrandValue = computed({
    get: () => {
        return props.modelValue;
    },
    set: value => {
        emit('update:modelValue', value);
    },
});
const popoverWidth = computed(() => {
    return props.width ? props.width : 'auto';
});
const guideBrandSelectList = ref([]); // 下拉选项列表
const guideBrandInputRef = ref(null);
const guidanceProductList = ref([]); // 品牌列表(下拉选项的原始总值)
const visibleGuideBrandSelect = computed(() => {
    return guideBrandSelectList.value.length > 0;
});
const handleGuideBrandSelect = (item) => {
    guideBrandInputRef.value && guideBrandInputRef.value.addTag(item.label);
};
const guideBrandInputChange = (val) => {
    if (val) {
        let lists = [];
        guidanceProductList.value.forEach(item => {
            let show = true;
            guidBrandValue.value.forEach(brand => { // 输入框标签中已存在同名时不显示
                if (item.label === brand) {
                    show = false;
                }
            });
            if (!item.brandName.toLowerCase().includes(val.toLowerCase())) { // 品牌名称如果和搜索内容不匹配时不显示
                show = false;
            }
            if (show) {
                lists.push({
                    id: item.id,
                    name: item.value,
                    label: item.label
                });
            }
        });
        guideBrandSelectList.value = lists;
    } else {
        guideBrandSelectList.value = [];
    }
};
const maxExceeded = () => {
    showErrMsg(t('product.brand.labelMax'));
};
const getBrandLabel = (item) => {
    let name = item.brandName;
    let tagInfo = [];
    if (Array.isArray(item.tagInfoList)) {
        item.tagInfoList.forEach(info => {
            tagInfo.push(info.tagName);
        });
    }
    if (tagInfo.length > 0) {
        name += `[${tagInfo.join('、')}]`;
    }
    return name;
};
const selectOne = ref(false);
const guideBrandMaxLabel = computed(() => {
    let num = props.maxLabel;
    if (selectOne.value) {
        num = 1;
    }
    return num;
});
const getGuidanceBrands = () => {
    if (!props.productCategoryId) {
        guidanceProductList.value = [];
        return;
    }
    HTTP.request('productBrandList', {
        method: 'post',
        data: {
            barndName: '',
            productCategoryId: props.productCategoryId
        },
        complete: data => {
            let lists = [];
            if (data.code === 0) {
                selectOne.value = data.data?.selectOne || false;
                data.data?.list?.forEach(item => {
                    lists.push(
                        {
                            id: item.id,
                            brandName: item.brandName,
                            label: getBrandLabel(item),
                            tagInfoList: item.tagInfoList || []
                        }
                    );
                });
            } else {
                selectOne.value = false;
                showErrMsg(data);
            }
            guidanceProductList.value = lists;
        },
        error: () => {
            selectOne.value = false;
            guidanceProductList.value = [];
        }
    });
};
watch(
    () => [props.productCategoryId],
    () => {
        getGuidanceBrands();
    }
);
const handleChange = (val) => {
    let guideBrandIds = [];
    val.forEach(guideBrand => {
        const curr = guidanceProductList.value.find(brand => brand.label === guideBrand);
        if (curr) {
            guideBrandIds.push(curr.id);
        } else {
            guideBrandIds.push('null');
        }
    });
    emit('change', val, guideBrandIds);
};
const init = () => {
    guideBrandInputRef.value && guideBrandInputRef.value.init();
};
defineExpose({
    init,
});
</script>
<style lang="scss" scoped>
.guide-brand-select-list {
    & > div {
        font-size: 14px;
        line-height: 34px;
        box-sizing: border-box;
        cursor: pointer;
        color: var(--label-color);
        padding: 0 32px 0 20px;
        &:hover {
            background-color: var(--el-fill-color-light);
        }
    }
}
</style>
