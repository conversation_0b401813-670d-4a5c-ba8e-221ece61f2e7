<template>
    <!-- Started by AICoder, pid:98001n0eaaf418a140d10988d0a9972ba6c4c214 -->
    <div
        v-loading="previewLoading"
        class="preview-wrap"
        :style="{ height: `${height}px`, overflow: 'auto' }"
    >
        <div v-if="!fileId" class="uedm-page-blank">
            <div class="uedm-page-blank-content">
                <i class="icon-blank"></i>
                <div>{{ $t('tipMessage.noData') }}</div>
            </div>
        </div>
        <div v-else-if="!previewSrc" class="uedm-page-blank">
            <div class="uedm-page-blank-content">
                <i class="icon-not-supported"></i>
                <div>{{ $t('tipMessage.previewNotSupported') }}</div>
            </div>
        </div>
        <iframe
            v-else
            :src="previewSrc"
            frameborder="0"
            :style="{ height: `${height}px`}"
        ></iframe>
    </div>
    <!-- Ended by AICoder, pid:98001n0eaaf418a140d10988d0a9972ba6c4c214 -->
</template>

<script setup>
import { computed, ref, watch, nextTick, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import HTTP from '@/util/httpService.js';
import { showErrMsg } from '@/util/common.js';
import { FilePreviewType } from '@/util/constants.js';
const props = defineProps({
    fileInfo: {
        type: Object,
        default: () => {
            return {};
        },
    },
    fileType: {
        type: Number,
        default: 1, // 1为文件id，2为文件url链接（规格说明书第三方文件）
    },
    height: {
        type: Number,
        default: 600,
    }
});

const previewSrc = ref('');
const fileId = computed(() => {
    return props.fileInfo?.fileId || '';
});
const fileName = computed(() => {
    return props.fileInfo?.fileName || '';
});
let queryCount = 0;
const previewLoading = ref(false);
const getPreviewSrc = () => {
    if (!fileId.value) {
        previewSrc.value = '';
        return;
    }
    const nameArr = fileName.value.split('.');
    let nameType = nameArr[nameArr.length - 1];
    if (!nameType || !FilePreviewType.includes(nameType.toLowerCase())) {
        previewSrc.value = '';
        return;
    }
    let count = ++queryCount;
    previewLoading.value = true;
    HTTP.request('getPreviewUrl', {
        method: 'post',
        urlParam: {
            fileAddr: encodeURIComponent(fileId.value),
            type: props.fileType,
            fileName: encodeURIComponent(fileName.value),
        },
        complete: resp => {
            if (count < queryCount) {
                return;
            }
            previewLoading.value = false;
            if (resp.code === 0) {
                previewSrc.value = resp.data || '';
            } else {
                previewSrc.value = '';
                showErrMsg(resp);
            }
        },
        error: () => {
            if (count < queryCount) {
                return;
            }
            previewLoading.value = false;
            previewSrc.value = '';
            showErrMsg(t('tipMessage.networkError'));
        },
    });
};
watch(
    () => fileId.value,
    () => {
        getPreviewSrc();
    }
);
onMounted(() => {
    if (props.fileInfo) {
        getPreviewSrc();
    }
});
</script>
<style lang="scss" scoped>
.preview-wrap {
    margin-top: 8px;
    .uedm-page-blank-content {
        font-size: 16px;
    }
    .icon-not-supported {
        display: inline-block;
        background: url('@/assets/img/undraw_folder.png') no-repeat;
        width: 240px;
        height: 214px;
    }
    iframe {
        width: 100%;
        height: 100%;
        display: block;
    }
}
</style>
