<template>
    <div
        :class="{
            'pagination-table': true,
            'col-resizable': colResizable,
            editable: editable,
        }"
    >
        <div v-show="$slots.title || $slots.toolbar" class="header">
            <div class="title">
                <slot name="title"></slot>
            </div>
            <div class="toolbar">
                <slot name="toolbar"></slot>
            </div>
        </div>
        <transition name="el-fade-in">
            <div v-show="_showSelected" class="uedm-table-selectioninfo">
                {{ $t('button.selected') }}
                <span class="number">{{ size(selectedRows) }}</span>
                {{ $t('button.selectedPost') }}
                <el-link class="cancelBtn" type="danger" :underline="false" @click="unselect">
                    {{ $t('button.unselect') }}
                </el-link>
            </div>
        </transition>
        <el-table
            ref="tableRef"
            class="el-table-sortable"
            v-bind="$attrs"
            :border="colResizable || $attrs.border"
            :data="data"
            :row-key="rowKey"
            @current-change="tableCurChange"
            @sort-change="sortChange"
            @row-click="rowClick"
            @row-dblclick="rowDblclick"
            @header-dragend="headerDragend"
        >
            <!-- eslint-disable-next-line vue/no-v-for-template-key -->
            <template v-for="column in _columns" :key="column.prop">
                <el-table-column v-if="column.type === 'selection'" v-bind="omit(column, ['children'])" />
                <column-item v-else v-bind="omit(column, ['children'])" :column="column">
                    <template #headerCell="{ column: col }">
                        <slot name="headerCell" :column="col"></slot>
                    </template>
                    <template #bodyCell="{ row, column: col, index }">
                        <slot name="bodyCell" :row="row" :column="col" :index="index"></slot>
                    </template>
                </column-item>
            </template>
        </el-table>
        <el-pagination
            v-if="pagination"
            ref="paginationRef"
            v-bind="$attrs"
            title=""
            :page-size="pagination.pageSize"
            :current-page="pagination.currentPage"
            :total="pagination.total"
            :page-sizes="pageSizes || pagination.pageSizes"
            :layout="pageLayout"
            :pager-count="pagerCount"
            background
            @current-change="pageCurChange"
            @size-change="sizeChange"
        />
    </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, shallowRef, nextTick, onUpdated } from 'vue';
import omit from 'lodash/omit';
import size from 'lodash/size';
import isEmpty from 'lodash/isEmpty';
import ColumnItem from './column-item.vue';

const SELECTION_COL = {
    type: 'selection',
    width: 55,
};

const props = defineProps({
    columns: {
        type: Array,
        required: true,
        default: () => [],
    },
    data: {
        type: Array,
        required: true,
        default: () => [],
    },
    pagination: {
        type: Object,
        default: null,
    },
    pageSizes: {
        type: Array,
        default: null,
    },
    pageLayout: {
        type: String,
        default: 'total, sizes, prev, pager, next, jumper',
    },
    // 行是否可多选
    selectable: {
        type: Boolean,
        default: false,
    },
    selectableFn: {
        type: Function,
        default: null
    },
    // 是否显示已选多少项
    showSelected: {
        type: Boolean,
        default: false,
    },
    // 已选行
    selectedRows: {
        type: [Array, Set],
        default: () => [],
    },
    // 使用行拖拽排序时，必须设置rowKey，且保证唯一
    rowKey: {
        type: [String, Function],
        default: null,
    },
    // 表格列是否可调整宽度，解决el-table必须border为true才能调整列宽的问题
    colResizable: {
        type: Boolean,
        default: false,
    },
    // 是否为可编辑表格，如果是，UX要求行高为42px
    editable: {
        type: Boolean,
        default: false,
    },
    // 错误页码，标红显示，如果是当前页，则不需要处理，还是保持蓝色
    errorPages: {
        type: Array,
        default: () => [],
    },
    pagerCount: {
        type: Number,
        default: 7,
    },
});

const emits = defineEmits([
    'pageChange',
    'tableCurrentChange',
    'sortChange',
    'sizeChange',
    'rowDragEnd',
    'rowClick',
    'rowDblclick',
    'updated',
    'unselect',
]);

const tableRef = ref(null);
const paginationRef = ref(null);
const _columns = computed(() => {
    let selectionCol = SELECTION_COL;
    if(props.selectableFn) {
        selectionCol.selectable = props.selectableFn;
    }
    return props.selectable ? [selectionCol].concat(props.columns) : props.columns;
});
const _showSelected = computed(() => props.selectable && props.showSelected && !isEmpty(props.selectedRows));

const tableCurChange = (currentRow, oldCurrentRow) => {
    emits('tableCurrentChange', currentRow, oldCurrentRow);
};
const pageCurChange = currentPage => {
    emits('pageChange', currentPage);
};
const sortChange = ({ column, prop, order }) => {
    emits('sortChange', { column, prop, order });
};
const sizeChange = currentPageSize => {
    emits('sizeChange', currentPageSize);
};
const rowClick = (row, column, event) => {
    emits('rowClick', { row, column, event });
};
const rowDblclick = (row, column, event) => {
    emits('rowDblclick', { row, column, event });
};
const rowDragEnd = evt => {
    emits('rowDragEnd', evt);
};
const unselect = () => {
    emits('unselect');
};
const headerDragend = (newWidth, oldWidth, column) => {
    // 限制列宽可拖动的最小值
    if (column.minWidth && newWidth < column.minWidth) {
        column.width = column.minWidth;
    }
};

const getLeftRightPageNo = element => ({
    left: parseInt(element.previousElementSibling?.textContent?.trim?.() || 0),
    right: parseInt(element.nextElementSibling?.textContent?.trim?.() || 0),
});

watch(
    () => [props.errorPages, props.pagination?.currentPage],
    () => {
        const paginationEl = paginationRef.value?.$el;
        nextTick(() => {
            if (paginationEl) {
                if (!isEmpty(props.errorPages)) {
                    let _errorPages = [...props.errorPages];
                    const pagerEl = paginationEl.querySelector('.el-pager');
                    if (pagerEl) {
                        const liEls = pagerEl.querySelectorAll('li') || [];
                        liEls.forEach(li => {
                            const pageNo = parseInt(li.textContent.trim());
                            if (props.errorPages.includes(pageNo)) {
                                li.classList.add('is-error');
                                _errorPages = _errorPages.filter(page => page !== pageNo);
                            } else {
                                li.classList.remove('is-error');
                            }
                            if (!isEmpty(_errorPages)) {
                                // 针对页码很多的情况，未展示出来的页码，需要给...页码标红
                                const quickPrevEl = pagerEl.querySelector('li.btn-quickprev');
                                const quickNextEl = pagerEl.querySelector('li.btn-quicknext');
                                _errorPages.forEach(page => {
                                    if (quickPrevEl) {
                                        const { left: prevLeftPageNo, right: prevRightPageNo } =
                                            getLeftRightPageNo(quickPrevEl);
                                        if (page > prevLeftPageNo && page < prevRightPageNo) {
                                            quickPrevEl.classList.add('is-error');
                                            return;
                                        }
                                    }
                                    if (quickNextEl) {
                                        const { left: nextLeftPageNo, right: nextRightPageNo } =
                                            getLeftRightPageNo(quickNextEl);
                                        if (page > nextLeftPageNo && page < nextRightPageNo) {
                                            quickNextEl.classList.add('is-error');
                                            return;
                                        }
                                    }
                                });
                            }
                        });
                    }
                } else {
                    const pagerEl = paginationEl.querySelector('.el-pager');
                    if (pagerEl) {
                        const liEls = pagerEl.querySelectorAll('li') || [];
                        liEls.forEach(li => {
                            li.classList.remove('is-error');
                        });
                    }
                }
            }
        });
    },
    {
        deep: true,
    }
);

onUpdated(() => {
    emits('updated');
});

// 表格exposes
const getSelectionRows = () => tableRef.value?.getSelectionRows();
const clearSelection = () => tableRef.value?.clearSelection();
const toggleRowSelection = (row, selected) => {
    return tableRef.value?.toggleRowSelection(row, selected);
};
const toggleRowExpansion = (row, expanded) => {
    return tableRef.value?.toggleRowExpansion(row, expanded);
};
const scrollTo = (options, yCoord) => {
    return tableRef.value?.scrollTo(options, yCoord);
};
const setCurrentRow = row => {
    return tableRef.value?.setCurrentRow(row);
};
const clearSort = () => {
    return tableRef.value?.clearSort();
};

defineExpose({
    getSelectionRows,
    clearSelection,
    toggleRowSelection,
    toggleRowExpansion,
    scrollTo,
    setCurrentRow,
    clearSort,
});
</script>

<style lang="scss" scoped>
.pagination-table {
    .header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
    }
    .el-pagination {
        display: flex;
        justify-content: flex-end;

        :deep(.el-pagination__editor.el-input) {
            width: 56px;
        }

        :deep(.el-pager) {
            .is-error:not(.is-active) {
                background-color: #fcd3d3;
            }
        }
    }
    &.editable {
        :deep(tbody) {
            .el-table__cell {
                padding: 5px 0;
                .cell {
                    .el-input {
                        height: 28px;
                        .el-input__inner {
                            height: 28px;
                        }
                    }
                    .el-select {
                        height: 28px;
                        .el-select__wrapper {
                            min-height: 28px;

                            .el-select__selected-item {
                                .el-tag {
                                    height: 20px;
                                }
                            }
                        }
                    }

                    .el-textarea__inner {
                        line-height: 1.3;
                    }
                }
            }
        }
    }

    &.col-resizable {
        &:deep(.el-table--border::before) {
            width: 0 !important;
        }

        :deep(.el-table--border::after) {
            width: 0 !important;
        }

        :deep(.el-table__cell) {
            border-right: none;
        }

        :deep(.el-table__inner-wrapper::after) {
            height: 0 !important;
        }

        :deep(.el-table__border-left-patch) {
            width: 0 !important;
        }

        :deep(thead) {
            .el-table__cell {
                .cell {
                    border-right: 1px solid var(--el-table-border-color);
                }
            }
        }
    }
}

html.dark {
    .pagination-table {
        .el-pagination {
            :deep(.el-pager) {
                .is-error:not(.is-active) {
                    background-color: #582e2e;
                }
            }
        }
    }
}
</style>
