<!-- eslint-disable max-len -->
/* Started by AICoder, pid:zb94bncf1a364b51463508dae0ed9b37aa55f4d9 */
<template>
    <el-table-column :label="column.label" :prop="column.prop" :sortable="column.sortable ? 'custom' : false">
        <template #header>
            <slot name="headerCell" :column="column">{{ column.label }}</slot>
        </template>
        <template #default="{ row, $index }">
            <template v-if="$index > -1">
                <template v-if="column.children">
                    <column-item v-for="col in column.children" :key="col.prop" :column="col">
                        <template #headerCell="{ column: c }">
                            <slot name="headerCell" :column="c"></slot>
                        </template>
                        <template #bodyCell="{ row: r, column: c }">
                            <slot name="bodyCell" :row="r" :column="c"></slot>
                        </template>
                    </column-item>
                </template>
                <template v-else>
                    <slot name="bodyCell" :row="row" :column="column" :index="$index">
                        <span>{{ column.formatter ? column.formatter(row, column, row[column.prop]) : row[column.prop] }}</span>
                    </slot>
                </template>
            </template>
        </template>
    </el-table-column>
</template>

<script setup>
defineProps({
    column: {
        type: Object,
        required: true,
    },
});
</script>

/* Ended by AICoder, pid:zb94bncf1a364b51463508dae0ed9b37aa55f4d9 */
