/* Started by AICoder, pid:zc24178fa4p98e4143b008576171d16114a8896f */
import { reactive, ref, computed, watch, nextTick } from 'vue';
import isFunction from 'lodash/isFunction';
import cloneDeep from 'lodash/cloneDeep';

const ORDER_MAP = {
    ascending: 'asc',
    descending: 'desc',
};

const slicePageData = (data = [], currentPage = 1, pageSize) => {
    const start = (currentPage - 1) * pageSize;
    const end = start + pageSize;
    return data.slice(start, end);
};

export const DEFAULT_PAGESIZE_LIST = [5, 10, 20, 30, 50];

// 静态数据：fetchData传入响应式表格数组数据
// 远程数据：fetchData传入异步请求函数，resolve返回{ data: 表格数组数据, total: 数据总条数 }格式数据
export default function usePaginationTable(
    fetchData = [],
    rowKey = 'key',
    pageSize = 10,
    pageSizes = DEFAULT_PAGESIZE_LIST,
    tableRef = null
) {
    const data = ref([]); // 非远程调用时的全部数据
    const dataOrigin = ref([]); // 非远程调用时的全部数据(用于恢复初始排序)
    const tableData = ref([]);
    const tableLoading = ref(false);
    const sortBy = ref({});
    const selectedRows = ref([]);
    const _selectedRows = computed({
        get: () => Array.from(selectedRows.value),
        set: val => (selectedRows.value = val || []),
    });
    const _rowKey = rowKey || 'key';
    let toggling = false;

    const pagination = reactive({
        currentPage: 1,
        total: 0,
        pageSize: pageSize,
        pageSizes: pageSizes,
    });

    const remote = isFunction(fetchData);

    if (!remote) {
        watch(
            fetchData,
            next => {
                const _next = cloneDeep(next);
                data.value = _next;
                dataOrigin.value = cloneDeep(next);
                setTableData(_next);
            },
            { immediate: true }
        );
    }

    async function fetchDataList() {
        if (remote) {
            tableLoading.value = true;
            // tableData.value = [];
            const { data, total } = await fetchData();
            tableData.value = data;
            pagination.total = total;
            tableLoading.value = false;
        } else {
            /* Started by AICoder, pid:pb6a08c053ycbc214db90a21a0bfa50f5899b957 */
            if (sortBy.value.order) {
                handleDataSort();
                setTableData(data.value);
                if (sortBy.value.order === 'default') {
                    sortBy.value = {};
                }
            } else {
                setTableData(data.value);
            }
            /* Ended by AICoder, pid:pb6a08c053ycbc214db90a21a0bfa50f5899b957 */
        }
    }
    /* Started by AICoder, pid:1d906kda95hd85414346086ef0ed161904425ec0 */
    function handleDataSort() {
        if (sortBy.value.order === 'default') {
            data.value = cloneDeep(dataOrigin.value);
        } else {
            const orderMultiplier = sortBy.value.order === 'asc' ? 1 : -1;
            data.value.sort((cur, next) => {
                const curValue = cur[sortBy.value.prop];
                const nextValue = next[sortBy.value.prop];
                return orderMultiplier * curValue.localeCompare(nextValue);
            });
        }
    }
    /* Ended by AICoder, pid:1d906kda95hd85414346086ef0ed161904425ec0 */

    function setTableData(_data = []) {
        pagination.total = _data.length;
        tableData.value = slicePageData(_data, pagination.currentPage, pagination.pageSize);
    }

    function reload() {
        pagination.currentPage = 1;
        pagination.total = 0;
        fetchDataList();
    }
    function resetPageNum() {
        pagination.currentPage = 1;
    }

    function pageChange(curPage) {
        pagination.currentPage = curPage;
        fetchDataList();
    }

    function sizeChange(pageSize) {
        pagination.pageSize = pageSize;
        reload();
    }

    function resetPage() {
        pagination.pageSize = pageSize;
        pagination.currentPage = 1;
        pagination.total = 0;
    }

    function sortChange({ prop, order }) {
        pagination.currentPage = 1;
        pagination.total = 0;
        sortBy.value = order ? { prop, order: ORDER_MAP[order] } : { prop, order: 'default' };
        fetchDataList();
    }

    function selectionChange(rows) {
        // syncSelection 会触发多次selectionChange，导致选中行异常，因此使用标志位限制触发
        if (toggling) {
            return;
        }

        rows.forEach(row => {
            const rowIndex = selectedRows.value.findIndex(item => item[_rowKey] === row[_rowKey]);
            if (rowIndex > -1) {
                selectedRows.value.splice(rowIndex, 1, row);
            } else {
                selectedRows.value.push(row);
            }
        });

        tableData.value
            .filter(row => !rows.includes(row))
            .forEach(row => {
                const rowIndex = selectedRows.value.findIndex(item => item[_rowKey] === row[_rowKey]);
                if (rowIndex > -1) {
                    selectedRows.value.splice(rowIndex, 1);
                }
            });
    }

    function selectAll(rows) {
        if (rows && rows.length) {
            tableData.value.forEach(row => {
                const rowIndex = selectedRows.value.findIndex(item => item[_rowKey] === row[_rowKey]);
                if (rowIndex > -1) {
                    selectedRows.value.splice(rowIndex, 1, row);
                } else {
                    selectedRows.value.push(row);
                }
            });
        } else {
            tableData.value.forEach(row => {
                const rowIndex = selectedRows.value.findIndex(item => item[_rowKey] === row[_rowKey]);
                if (rowIndex > -1) {
                    selectedRows.value.splice(rowIndex, 1);
                }
            });
        }
    }

    // 同步选中/未选中状态
    function syncSelection() {
        toggling = true;
        nextTick(() => {
            if (tableRef && tableRef.value) {
                tableData.value.forEach(row => {
                    const rowIndex = selectedRows.value.findIndex(item => item[_rowKey] === row[_rowKey]);
                    const rowSelected = rowIndex > -1;
                    tableRef.value.toggleRowSelection(row, rowSelected);
                });
            }
            toggling = false;
        });
    }

    // 跨页取消选择
    function unselect() {
        selectedRows.value = [];
        syncSelection();
    }

    // 清空排序条件
    function clearSort() {
        if (tableRef && tableRef.value) {
            sortBy.value = {};
            tableRef.value.clearSort();
        }
    }

    return {
        tableData,
        tableLoading,
        pagination,
        sortBy,
        selectedRows: _selectedRows,
        fetchDataList,
        reload,
        resetPageNum,
        pageChange,
        sizeChange,
        resetPage,
        sortChange,
        selectionChange,
        selectAll,
        syncSelection,
        unselect,
        clearSort,
    };
}

/* Ended by AICoder, pid:zc24178fa4p98e4143b008576171d16114a8896f */
