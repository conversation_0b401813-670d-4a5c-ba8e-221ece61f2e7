<template>
    <el-tooltip
        :content="text"
        :disabled="isTipDisabled"
        :placement="placement"
        :effect="effect"
        :trigger="trigger"
        popper-class="popper-tooltip-263974"
    >
        <span :id="uniqueId" class="param-text">
            <slot></slot>
        </span>
    </el-tooltip>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

defineExpose({ activeToolTip });
defineProps({
    placement: {
        type: String,
        default: 'top',
    },
    effect: {
        type: String,
        default: 'light',
    },
    trigger: {
        type: String,
        default: 'hover',
    },
});

const isTipDisabled = ref(true);
const uniqueId = ref(getUUID());
const text = ref('');

onMounted(() => {
    activeToolTip();
    // 窗口变化时激活ToolTip
    window.addEventListener('resize', activeToolTip);
});

// 当在弹窗等场景使用时，tooltip可能不生效，此时父组件可以使用ref调用该方法
function activeToolTip() {
    const el = document.getElementById(uniqueId.value);
    if (el) {
        isTipDisabled.value = el.scrollWidth <= el.offsetWidth;
        text.value = el.innerText;
    }
}

function getUUID() {
    if (typeof crypto === 'object' && typeof crypto.randomUUID === 'function') {
        return crypto.randomUUID();
    }

    // Math.random() 因安全问题会被扫描出来，使用 LCG
    // 线性同余伪随机数生成器（LCG），不涉及安全场景，可以使用
    const customRandom = (function () {
        const m = Math.pow(2, 32);
        const a = 1103515245;
        const c = 4258951256;
        let seed = Date.now();

        return function random() {
            seed = (a * seed + c) % m;
            return seed / m;
        };
    })();

    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = (customRandom() * 16) | 0;
        const v = c === 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
    });
}

onUnmounted(() => {
    window.removeEventListener('resize', activeToolTip);
});
</script>

<style scoped>
.param-text {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    width: 100%;
    display: flow-root;
}
</style>

<style>
.popper-tooltip-263974.is-light span {
    color: #606266;
}
</style>
