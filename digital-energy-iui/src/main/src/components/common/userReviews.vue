/* Started by AICoder, pid:l32acc1e6d16dbd147330aade190e65743a64d4a */
<template>
    <div>
        <div class="feedback-container">
            <img class="feedback-icon" :src="operationType === 0 ? icon_zan1_select : icon_zan1" @click="handleLike" />
            <span class="like-count truncate">{{ likeNumber }}</span>

            <img
                class="feedback-icon"
                :src="operationType === 1 ? icon_cai1_select : icon_cai1"
                @click="handleDislike"
            />
            <span class="dislike-count truncate">{{ stepNumber }}</span>
        </div>
        <dislike-dialog
            :visible="showDislikeDialog"
            :function-id="functionId"
            @success="successDialog"
            @close="showDislikeDialog = false"
        ></dislike-dialog>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import HTTP from '@/util/httpService.js';
import { showErrMsg, showSuccessMsg } from '@/util/common.js';
import DislikeDialog from '@/components/common/dislikeDialog.vue';
import icon_zan1 from '@/assets/img/icon_zan1.png';
import icon_zan1_select from '@/assets/img/icon_zan1_select.png';
import icon_cai1 from '@/assets/img/icon_cai1.png';
import icon_cai1_select from '@/assets/img/icon_cai1_select.png';

const likeNumber = ref(null);
const stepNumber = ref(null);
const operationType = ref(null);
const showDislikeDialog = ref(false);
const functionId = ref('');
// 获取评价信息
const getCurrentEval = () => {
    return new Promise((resolve, reject) => {
        HTTP.request('getCurrentEval', {
            method: 'post',
            data: { functionId: functionId.value },
            complete: data => {
                if (data.code !== 0) {
                    showErrMsg(data);
                    resolve({
                        type: null,
                        likeNum: null,
                        stepNum: null,
                    });
                    return;
                }

                // 0：赞，1：踩, 2：无状态
                resolve({
                    type: data.data.operationType,
                    likeNum: data.data.likeNumber,
                    stepNum: data.data.stepNumber,
                });
            },
            error: err => {
                showErrMsg(err);
                resolve({
                    type: null,
                    likeNum: null,
                    stepNum: null,
                });
            },
        });
    });
};
// 赞/踩
const doEvaluationEval = operationType => {
    HTTP.request('doEvaluationEval', {
        method: 'post',
        data: {
            functionId: functionId.value,
            operationType,
        },
        complete: data => {
            if (data.code === 0) {
                showSuccessMsg(data.message);
                transformEvaluationData();
                return;
            }
            showErrMsg(data);
        },
        error: err => {
            showErrMsg(err);
        },
    });
};
const transformEvaluationData = async () => {
    const { type, likeNum, stepNum } = await getCurrentEval();
    operationType.value = type;
    likeNumber.value = likeNum;
    stepNumber.value = stepNum;
};
const handleLike = () => {
    doEvaluationEval(operationType.value === 0 ? 2 : 0);
};
const handleDislike = async () => {
    // 踩
    if (operationType.value !== 1) {
        // 没踩过
        showDislikeDialog.value = true;
    } else {
        // 有踩过
        doEvaluationEval(2);
    }
};
const successDialog = () => {
    showDislikeDialog.value = false;
    transformEvaluationData();
};

const refreshData = id => {
    functionId.value = id;
    transformEvaluationData();
};

defineExpose({ refreshData });
</script>
<style lang="scss" scoped>
.feedback-container {
    position: absolute;
    right: 22px;
    top: 11px;
    display: flex;
    align-items: center;
    height: 19px;

    .feedback-icon {
        background: none;
        border: none;
        padding: 0;
        margin: 0;
        cursor: pointer;
    }

    .like-count {
        margin-right: 15px;
    }

    .truncate {
        max-width: 40px;
        margin-left: 4px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #404040;
        font-family: Microsoft YaHei;
    }
}
</style>
/* Ended by AICoder, pid:l32acc1e6d16dbd147330aade190e65743a64d4a */
