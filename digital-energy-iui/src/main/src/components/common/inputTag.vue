/* Started by AICoder, pid:q7f57hfc17jd305146960bda8220c66148b226a1 */
<template>
    <div
        ref="inputTagRef"
        v-clickOutside="handleClickOutSide"
        class="el-input-tag el-input-tag__wrapper input-tag"
        :class="{'is-focused': isFocused, 'is-hovering': isHover, 'is-disabled': disabled}"

        @mouseover="inputMouseOver"
        @mouseleave="inputMouseLeave"
    >
        <div class="el-input-tag__inner" :class="{'is-space': !hasTag}">
            <el-tag
                v-for="(tag, index) in taglist"
                :key="index"
                type="info"
                closable
                @close="removeTag(tag)"
            >
                {{ tag }}
            </el-tag>
            <div v-show="!disabled && (!maxLabel || taglist.length < maxLabel)" class="el-input-tag__input-wrapper">
                <el-input
                    ref="inputRef"
                    v-model="inputValue"
                    type="text"
                    :maxlength="maxLength"
                    :placeholder="placeholderTip"
                    @keydown="inputKeydown"
                    @keyup.enter="addTag()"
                    @input="inputChange"
                    @focus="inputFocus"
                    @blur="inputBlur"
                ></el-input>
            </div>
        </div>
        <div class="el-input-tag__suffix">
            <i
                v-show="showInputClearable && maxLabel!==1"
                class="el-icon el-input-tag__icon el-input-tag__clear"
                @click="handleClearable"
            >
                <ElIconCircleClose />
            </i>
        </div>
    </div>
</template>
<script setup>
import { ref, computed } from 'vue';
import { ClickOutside as vClickOutside } from 'element-plus';
const emit = defineEmits(['update:modelValue', 'change', 'inputChange', 'addTag', 'removeTag', 'clear', 'maxExceeded', 'focus', 'blur']);
const props = defineProps({
    modelValue: {
        type: Array,
        default: () => {
            return [];
        },
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: String,
        default: '',
    },
    clearable: {
        type: Boolean,
        default: true,
    },
    maxLabel: {
        type: Number,
        default: 0,
    },
    maxLength: {
        type: Number,
        default: 200,
    },
    wrapClass: {
        type: String,
        default: '',
    }
});
const inputValue = ref('');
const inputRef = ref(null);
const taglist = ref([]);
const hasTag = computed(() => {
    return taglist.value.length > 0;
});
const placeholderTip = computed(() => {
    let text = '';
    if (!hasTag.value && props.placeholder) {
        text = props.placeholder;
    }
    return text;
});
const showInputClearable = ref(false);
const isHover = ref(false);
const isFocused = ref(false);
const inputFocus = () => {
    isFocused.value = true;
    emit('focus');
};
const inputBlur = () => {
    isFocused.value = false;
    emit('blur');
};
const inputMouseOver = () => {
    isHover.value = true;
    if (props.clearable && (hasTag.value || inputValue.value)) {
        showInputClearable.value = true;
    } else {
        showInputClearable.value = false;
    }
};
const inputMouseLeave = () => {
    isHover.value = false;
    showInputClearable.value = false;
};
const inputChange = () => {
    emit('inputChange', inputValue.value);
    inputMouseOver();
};
const inputKeydown = e => {
    if (e.key === ',') {
        e.returnValue = false;
        return false;
    }
    return true;
};
const emitChange = () => {
    emit('update:modelValue', [...taglist.value]);
    emit('change', [...taglist.value]);
    emit('inputChange', inputValue.value);
};
// 添加标签
const addTag = (text) => {
    if (props.maxLabel && taglist.value.length >= props.maxLabel) {
        inputValue.value = '';
        emit('maxExceeded');
    } else {
        let addText = inputValue.value?.trim();
        if (text) {
            addText = text.trim();
        }
        if (addText && !taglist.value.find(d => d === addText)) {
            taglist.value.push(addText);
            inputValue.value = '';
        }
        emitChange();
        emit('addTag', addText);
    }

};
const inputTagRef = ref();
const handleClickOutSide = function (e) {
    e.stopPropagation();
    const className = props.wrapClass;
    try {
        if (!(className ? e.target.closest(`.${className}`) : false || e.target.closest('.input-tag')) && inputValue.value) {
            addTag();
        }
    } catch (err) {
        console.warn('input-tag event error:');
    }
};
// 删除标签
const removeTag = val => {
    const removeIndex = taglist.value.findIndex(item => item === val);
    taglist.value.splice(removeIndex, 1);
    emitChange();
    emit('removeTag', val);
    inputMouseOver();
    inputRef.value?.focus();
};
const handleClearable = () => {
    taglist.value = [];
    inputValue.value = '';
    inputMouseOver();
    inputRef.value?.focus();
    emitChange();
    emit('clear');
};
const focus = () => {
    inputRef.value?.focus();
};
const blur = () => {
    inputRef.value?.blur();
};
const init = () => {
    if (props.modelValue && Array.isArray(props.modelValue)) {
        taglist.value = [...props.modelValue];
    } else {
        taglist.value = [];
    }
    inputValue.value = '';
};
defineExpose({
    focus,
    blur,
    addTag,
    init,
});
</script>
<style lang="scss" scoped>
.el-input-tag {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    cursor: pointer;
    font-size: 14px;
    padding: 4px;
    width: 100%;
    min-height: 32px;
    line-height: 24px;
    border-radius: 4px;
    background-color: var(--content-bg-color);
    transition: .3s;
    transform: translateZ(0);
    box-shadow: 0 0 0 1px var(--el-border-color) inset;
    &.is-focused{
        box-shadow: 0 0 0 1px var(--el-color-primary) inset;
    }
    &.is-hovering:not(.is-focused,.is-disabled) {
        box-shadow: 0 0 0 1px var(--el-border-color-hover) inset;
    }
    &.is-disabled {
        cursor: not-allowed;
        background-color: var(--disabled-bg-color);
        box-shadow: 0 0 0 1px var(--border-disabled-color) inset;
    }
    .el-input-tag__inner {
        position: relative;
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        flex: 1;
        max-width: 100%;
        min-width: 0;
        gap: 6px;
        &.is-space {
            margin-left: 8px;
            margin-right: 8px;
        }
    }
    .el-input-tag__input-wrapper {
        flex: 1;
        height: 24px;
        :deep(.el-input) {
            min-width: 11px;
        }
        :deep(.el-input__wrapper) {
            box-shadow: none!important;
            padding: 0;
            background-color: transparent;
            .el-input__inner {
                height: 24px;
                line-height: 24px;
            }
        }
    }
    .el-input-tag__suffix {
        display: flex;
        align-items: center;
        flex-shrink: 0;
        gap: 8px;
        padding: 0 4px;
        min-width: 14px;
        cursor: pointer;
        color: var(--disabled-color);
    }
}
</style>
/* Ended by AICoder, pid:q7f57hfc17jd305146960bda8220c66148b226a1 */