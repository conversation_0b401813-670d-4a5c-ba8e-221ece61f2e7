<template>
    <div class="el-select">
        <div
            class="el-select__wrapper"
            :class="{'is-hovering': isHover, 'is-focus': isOpened}"
            tabindex="-1"
            @mouseover="inputMouseOver"
            @mouseleave="inputMouseLeave"
            @click="selectClick"
        >
            <div class="el-select__selection">
                <div
                    class="el-select__selected-item el-select__placeholder"
                    :class="{'is-transparent':!selectedValue.name}"
                >
                    <span>{{ selectTxt }}</span>
                </div>
            </div>
            <div class="el-select__suffix">
                <i
                    v-show="!inputClearable"
                    class="el-icon el-select__caret el-select__icon"
                >
                    <ElIconArrowDown />
                </i>
                <i
                    v-show="inputClearable && selectedValue.name"
                    class="el-icon el-select__caret el-select__icon"
                    @click.stop="handleClearable"
                >
                    <ElIconCircleClose />
                </i>
            </div>
        </div>
    </div>
</template>
<script setup>
import { ref, reactive, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const props = defineProps({
    value: {
        type: Object,
        default: () => {
            return {};
        },
    },
    clearable: {
        type: Boolean,
        default: false,
    }
});
const emit = defineEmits(['clear', 'selectClick']);
const selectedValue = reactive({
    id: '',
    name: '',
});
const init = () => {
    selectedValue.id = props.value?.id || '';
    selectedValue.name = props.value?.name || '';
};
init();
watch(
    () => props.value,
    () => {
        init();
    },
    { deep: true }
);
const selectTxt = computed(() => {
    let text = t('placeholder.select');
    if (selectedValue.name) {
        text = selectedValue.name;
    }
    return text;
});
const isHover = ref(false);
const inputClearable = ref(false);
const inputMouseOver = function () {
    isHover.value = true;
    if (props.clearable && selectedValue.name) {
        inputClearable.value = true;
    }
};
const inputMouseLeave = function () {
    isHover.value = false;
    inputClearable.value = false;
};
const isOpened = ref(false);
const selectClick = function () {
    isOpened.value = true;
    emit('selectClick');
};
const hide = function () {
    // 下拉框隐藏
    isOpened.value = false;
};
const handleClearable = function () {
    // 查询条件中位置选中值清空触发
    selectedValue.name = '';
    selectedValue.id = '';
    emit('clear');
};
defineExpose({
    hide,
});
</script>
