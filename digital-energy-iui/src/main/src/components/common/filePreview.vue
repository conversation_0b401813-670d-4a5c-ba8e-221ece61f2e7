<template>
    <el-drawer
        v-model="dialogVisible"
        :size="drawerWidth"
        :with-header="false"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :wrapper-closable="false"
        :append-to-body="appendTobody"
    >
        <div class="drawer__header">
            <div class="drawer__title">
                {{ $t('fields.documentPreview') }}
                <el-button
                    type="primary"
                    :disabled="selectedFileIds.length === 0 || loading"
                    style="margin-left: 8px"
                    :loading="exportLoading"
                    @click="handleBatchExport"
                >
                    {{ $t('button.batchOperation', { operation: $t('button.download') }) }}
                </el-button>
            </div>
            <el-icon class="drawer__close" @click="handleCancel"><ElIconClose /></el-icon>
        </div>
        <div class="drawer__content" style="margin-top: 16px">
            <div class="file-lists uedm-scrollbar__width" :style="{ height: `${contentHeight}px`, overflow: 'auto' }">
                <el-checkbox-group v-model="selectedFileIds">
                    <div
                        v-for="file in fileLists"
                        :key="file.fileId"
                        class="list-item"
                        :class="{ active: file.fileId === currentFileInfo?.fileId }"
                        @click="handleFileItemClick(file)"
                    >
                        <el-checkbox :label="file.fileId">{{ '' }}</el-checkbox>
                        <span class="label">{{ file.fileName }}</span>
                    </div>
                </el-checkbox-group>
            </div>
            <div class="file-detail">
                <div ref="detailTopRef" class="detail-top">
                    <div class="detail-name">{{ currentFileInfo?.fileName || '' }}</div>
                    <el-tooltip
                        class="item"
                        effect="dark"
                        :content="$t('button.download')"
                        placement="bottom"
                        :disabled="!currentFileInfo || !currentFileInfo.fileName"
                    >
                        <el-button
                            :disabled="!currentFileInfo || !currentFileInfo.fileName"
                            plain
                            class="uedm-button-icon ml8"
                            :loading="exportLoading"
                            @click="handleExport(currentFileInfo.fileId)"
                        >
                            <span class="plx-ico-download-16"></span>
                        </el-button>
                    </el-tooltip>
                </div>
                <div
                    v-loading="previewLoading"
                    class="preview-wrap"
                    :style="{ height: `${previewWrapHeight}px`, overflow: 'auto' }"
                >
                    <div v-if="!previewSrc" class="uedm-page-blank">
                        <div class="uedm-page-blank-content">
                            <i class="icon-not-supported"></i>
                            <div>{{ $t('tipMessage.previewNotSupported') }}</div>
                        </div>
                    </div>
                    <iframe
                        v-if="previewSrc"
                        :src="previewSrc"
                        frameborder="0"
                        :style="{ height: `${previewWrapHeight}px` }"
                    ></iframe>
                </div>
            </div>
        </div>
    </el-drawer>
</template>

<script setup>
import { computed, ref, watch, nextTick, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import commonStore from '@/store/commonStore.js';
const { t } = useI18n();
import exportFunc from '@/util/exportHandler.js';
import HTTP from '@/util/httpService.js';
import { showErrMsg, showSuccessMsg, apiDownloadType, getAddRecordDocDownLoad } from '@/util/common.js';
import { FilePreviewType } from '@/util/constants.js';
const emit = defineEmits(['update:visible', 'success']);
const store = commonStore();
const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    width: {
        type: Number,
        default: 0,
    },
    fileLists: {
        type: Array,
        default: () => {
            return [];
        },
    },
    selectFileId: {
        type: String,
        default: '',
    },
    appendTobody: {
        type: Boolean,
        default: true,
    },
    fileType: {
        type: Number,
        default: 1, // 1为文件id，2为文件url链接（规格说明书第三方文件）
    },
});
const dialogVisible = computed({
    get: () => {
        return props.visible;
    },
    set: val => {
        emit('update:visible', val);
    },
});
const handleCancel = function () {
    dialogVisible.value = false;
};
const isResize = computed(() => {
    return store.resize;
});
const contentHeight = ref(796);
const drawerWidth = ref('1240px');
const updateDrawerWidth = () => {
    // 抽屉最大宽度设为屏幕宽度减20
    let maxWidth = props.width || window.innerWidth * 0.86;
    let minWidth = 1240;
    drawerWidth.value = `${Math.max(minWidth, maxWidth)}px`;
};
const updateContentHeight = () => {
    const totalH = window.innerHeight;
    const headH = 32;
    const paddingH = 20 * 2 + 16;
    const h = totalH - headH - paddingH;
    contentHeight.value = h;
};
const detailTopRef = ref(null);
const previewWrapHeight = ref(756);
const updatepreviewWrapHeight = () => {
    const totalH = contentHeight.value;
    const topH = detailTopRef.value?.clientHeight || 32;
    const h = totalH - topH - 8;
    previewWrapHeight.value = h;
};
watch(
    () => isResize.value,
    () => {
        updateDrawerWidth();
        updateContentHeight();
        updatepreviewWrapHeight();
    }
);
const loading = ref(false);
const selectedFileIds = ref([]);
selectedFileIds.value = props.fileLists.map(item => {
    return item.fileId;
});
const currentFileInfo = ref({});
const previewSrc = ref('');
let queryCount = 0;
const previewLoading = ref(false);
/* Started by AICoder, pid:s5476k19ccg4d2a14b250b7f008c2d4f77e26883 */
const getPreviewSrc = () => {
    if (!currentFileInfo.value?.fileId) {
        previewSrc.value = '';
        return;
    }
    const nameArr = currentFileInfo.value.fileName.split('.');
    let nameType = nameArr[nameArr.length - 1];
    if (!nameType || !FilePreviewType.includes(nameType.toLowerCase())) {
        previewSrc.value = '';
        return;
    }
    let count = ++queryCount;
    previewLoading.value = true;
    HTTP.request('getPreviewUrl', {
        method: 'post',
        urlParam: {
            fileAddr: encodeURIComponent(currentFileInfo.value.fileId),
            type: props.fileType,
            fileName: encodeURIComponent(currentFileInfo.value.fileName),
        },
        complete: resp => {
            if (count < queryCount) {
                return;
            }
            previewLoading.value = false;
            if (resp.code === 0) {
                previewSrc.value = resp.data || '';
            } else {
                previewSrc.value = '';
                showErrMsg(resp);
            }
        },
        error: () => {
            if (count < queryCount) {
                return;
            }
            previewLoading.value = false;
            previewSrc.value = '';
            showErrMsg(t('tipMessage.networkError'));
        },
    });
};
/* Ended by AICoder, pid:s5476k19ccg4d2a14b250b7f008c2d4f77e26883 */
const handleFileItemClick = info => {
    currentFileInfo.value = info;
    getPreviewSrc();
    nextTick(() => {
        updatepreviewWrapHeight();
    });
};
const exportLoading = ref(false);
const toExport = id => {
    return new Promise(resolve => {
        const languageOption = window.languageOptionDash;
        const successHandler = res => {
            if (res.type !== 'application/json') {
                resolve(true);
                return;
            }
            const { code, message: msg } = res.data;
            if (code === 0) {
                showSuccessMsg(msg);
            } else {
                showErrMsg(res);
            }
            resolve(true);
        };
        const errorHandler = () => {
            resolve(true);
        };
        const config = {
            headers: {
                'accept-language': languageOption,
            },
        };
        let method = 'post';
        let fileNameHandler = null;
        let url = `/api/document-manager/v1/uportal/file/download?fileId=${id}`;
        if (props.fileType === 2) {
            const fileInfo = props.fileLists.find(d => d.fileId === id);
            method = 'get';
            fileNameHandler = () => {
                return fileInfo.fileName;
            };
            url = `/api/product-manager/v1/uportal/material/downloadSpecification?fileUrl=${encodeURIComponent(
                fileInfo.fileId
            )}&fileName=${encodeURIComponent(fileInfo.fileName)}`;
        }
        /* Started by AICoder, pid:5ef36tccf8qfb9614a010acfd078ac156db0b2f5 */
        setTimeout(() => {
            // 延时为了保障浏览器下载不阻塞，不然下载下来的文件数量不对
            exportFunc({
                url: url,
                method: method,
                fileNameHandler: fileNameHandler,
                errorHandler,
                successHandler,
                config,
            });
        }, 500);
        /* Ended by AICoder, pid:5ef36tccf8qfb9614a010acfd078ac156db0b2f5 */
    });
};
const handleBatchExport = async () => {
    exportLoading.value = true;
    for (const id of selectedFileIds.value) {
        await toExport(id);
        await getAddRecordDocDownLoad(id, apiDownloadType.param2);
    }
    exportLoading.value = false;
};
const handleExport = async id => {
    exportLoading.value = true;
    await toExport(id);
    exportLoading.value = false;
    getAddRecordDocDownLoad(id, apiDownloadType.param2);
};
onMounted(() => {
    if (props.fileLists && props.fileLists.length > 0) {
        let selectFile = null;
        if (props.selectFileId) {
            selectFile = props.fileLists.find(d => d.fileId === props.selectFileId);
        }
        currentFileInfo.value = selectFile || props.fileLists[0];
        getPreviewSrc();
    }
    setTimeout(() => {
        updateDrawerWidth();
        updateContentHeight();
        updatepreviewWrapHeight();
    }, 0);
});
</script>
<style lang="scss" scoped>
.drawer__header {
    display: flex;
    .drawer__title {
        color: var(--title-color);
        font-size: 18px;
        flex: 1;
    }
    .drawer__close {
        font-size: 16px;
        cursor: pointer;
        &:hover {
            color: var(--active-color);
        }
    }
}
.drawer__content {
    display: flex;
    .file-lists {
        width: 350px;
        box-shadow: 0 0 12px rgba(0, 0, 0, 0.12);
        box-sizing: border-box;
        padding: 8px;
        :deep(.el-checkbox) {
            height: 20px;
            vertical-align: middle;
        }
        .list-item {
            cursor: pointer;
            padding: 8px;
            border-bottom: solid 1px var(--border-td-color);
            display: flex;
            &:hover {
                background-color: var(--current-row-bg-color);
            }
            &.active {
                background-color: var(--el-color-primary-light-9);
            }
        }
        .label {
            font-size: 14px;
            display: inline-block;
            height: auto;
            min-height: 20px;
            line-height: 20px;
            vertical-align: middle;
            flex: 1;
            word-wrap: break-word;
            word-break: normal;
            overflow: hidden;
        }
    }
    .file-detail {
        flex: 1;
        padding: 0 0 0 16px;
    }
    .detail-top {
        display: flex;
        align-items: flex-end;
        .detail-name {
            flex: 1;
            color: var(--title-color);
        }
    }
    .preview-wrap {
        // background-color: var(--default-bg-color);
        margin-top: 8px;
        .uedm-page-blank-content {
            font-size: 16px;
        }
    }
}
.icon-not-supported {
    display: inline-block;
    background: url('@/assets/img/undraw_folder.png') no-repeat;
    width: 240px;
    height: 214px;
}
iframe {
    width: 100%;
    height: 100%;
    display: block;
}
</style>
