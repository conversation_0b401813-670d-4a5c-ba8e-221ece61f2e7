<!-- Started by AICoder, pid:227503691bge0a914e6508d0101fea231dc9bb4e -->
<template>
    <div :style="{ height: `${height}px`, overflow: 'auto' }">
        <div class="uedm-page-blank">
            <div class="uedm-page-blank-content">
                <i class="icon-image"></i>
                <div class="text">{{ $t('tipMessage.development') }}</div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { computed } from 'vue';
import commonStore from '@/store/commonStore.js';
const store = commonStore();
const height = computed(() => {
    return store.getHeight;
});
</script>
<style lang="scss" scoped>
.icon-image {
    background: url('../assets/img/icon-development.png') no-repeat center;
    width: 303px;
    height: 209px;
    display: inline-block;
}
.text {
    font-size: 14px;
}
</style>
<!-- Ended by AICoder, pid:227503691bge0a914e6508d0101fea231dc9bb4e -->
