<template>
    <div :style="{ height: `${height}px`, overflow: 'auto' }">
        <div class="uedm-page-blank">
            <div class="uedm-page-blank-content">
                <img :src="noPermissionImg" alt="" />
                <div class="text">{{ $t('tipMessage.noRight') }}</div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { computed } from 'vue';
import commonStore from '@/store/commonStore.js';
import noPermissionImg from '@/assets/img/no-permission.png';
const store = commonStore();
const height = computed(() => {
    return store.getHeight;
});
</script>
<style lang="scss" scoped>
.icon-image {
    background: url('../assets/img/icon-noRight.png') no-repeat center;
    width: 303px;
    height: 209px;
    display: inline-block;
}
.text {
    margin-top: 32.95px;
    color: rgba(0, 0, 0, 0.85);
    font-size: 16px;
}
</style>
