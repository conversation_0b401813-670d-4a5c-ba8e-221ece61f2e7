<template>
    <el-dialog
        v-model="dialogVisible"
        :title="$t('fields.notificationList')"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        width="800px"
        :append-to-body="true"
        @close="handleCancel"
    >
        <template #header>
            <div class="dialog__header">
                <span class="el-dialog__title">{{ $t('fields.notificationList') }}</span>
                <el-tooltip :content="$t('fields.setNoteRead')" placement="top">
                    <i v-if="tableData.length > 0" class="icon-clear" @click="debounceClear"></i>
                </el-tooltip>
            </div>
        </template>
        <el-table
            ref="tableRef"
            v-loading="tableLoading"
            :data="tableData"
            :show-header="false"
            style="width: 100%;"
            height="400px"
            @row-click="handleRowClick"
        >
            <el-table-column label="">
                <template #default="scope">
                    <!-- 项目名称 -->
                    <div class="msg-row" :class="{ 'is-link': scope.row.link }">
                        <div v-if="!scope.row.msgStatus" class="msg-dot"><i class="icon-dot"></i></div>
                        <div class="msg-content">
                            <ellipsis-tooltip :open-delay="500" :text="scope.row.msgName"></ellipsis-tooltip>
                        </div>
                        <el-tag>{{ scope.row.msgTypeName }}</el-tag>
                    </div>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            v-show="tableData.length > 0"
            v-model:current-page="pageInfo.pageNo"
            v-model:page-size="pageInfo.pageSize"
            :page-sizes="[5, 10, 20, 50]"
            layout="total, prev, pager, next, sizes"
            :total="pageInfo.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
        ></el-pagination>
    </el-dialog>
    <confirm-dialog
        v-model:visible="confirm.show"
        :content="$t('tipMessage.confirmNotificationListRead')"
        @confirm="toClearAll"
    ></confirm-dialog>
</template>
<script setup>
import { ref, reactive, computed } from 'vue';
import HTTP from '@/util/httpService';
import debounce from 'lodash/debounce';
import { DEBOUNCE_TIME } from '@/util/constants.js';
import { useI18n } from 'vue-i18n';
import { showErrMsg } from '@/util/common.js';
import commonStore from '@/store/commonStore.js';
import EllipsisTooltip from '@/components/common/ellipsisTooltip.vue';
import { useRouter } from 'vue-router';
import ConfirmDialog from '@/components/common/confirmDialog.vue';
const router = useRouter();
const emit = defineEmits(['update:visible', 'close', 'refresh']);
const { t } = useI18n();
const store = commonStore();
const props = defineProps({
    visible: {
        type: Boolean,
        default: false,
    },
    infos: {
        type: Object,
        default: () => {
            return {};
        },
    },
});
const hasRead = 1; // 1为已读, 0为未读
/* 弹出窗口显示隐藏 */
const dialogVisible = computed({
    get: () => {
        return props.visible;
    },
    set: value => {
        emit('update:visible', value);
    },
});
const handleCancel = function () {
    emit('close');
    dialogVisible.value = false;
};
/* 列表查询 */
const tableLoading = ref(false);
const tableData = ref([]);
const pageInfo = reactive({
    pageNo: 1, // 当前页码
    pageSize: 10, // 每页显示记录数
    total: 0, // 当前页总记录数
});
let queryCount = 0;
const getTableData = function () {
    tableLoading.value = true;
    let count = ++queryCount;
    HTTP.request('getMsgList', {
        method: 'get',
        urlParam: {
            pageNum: pageInfo.pageNo,
            pageSize: pageInfo.pageSize,
        },
        complete: resp => {
            if (count < queryCount) {
                return;
            }
            tableLoading.value = false;
            if (resp.code === 0) {
                tableData.value = resp.data?.list || [];
                pageInfo.total = resp.data?.total || 0;
            } else {
                tableData.value = [];
                pageInfo.total = 0;
                showErrMsg(resp);
            }
        },
        error: () => {
            if (count < queryCount) {
                return;
            }
            tableLoading.value = false;
            tableData.value = [];
            pageInfo.total = 0;
            showErrMsg(t('tipMessage.networkError'));
        },
    });
};
const handleSizeChange = function () {
    pageInfo.pageNo = 1;
    getTableData();
};
const handleCurrentChange = function () {
    getTableData();
};
const toJump = (row) => {
    if (row.link) {
        router.push(`/${row.link}`);
        store.setPageInit();
        handleCancel();
    }
};
const handleRead = (id, callFn) => {
    HTTP.request('msgClickRead', {
        method: 'post',
        urlParam: {
            deliveryId: id || '',
        },
        complete: resp => {
            if (resp.code !== 0) {
                showErrMsg(resp);
            }
            emit('refresh');
            if (callFn) {
                callFn(resp.code);
            }
        },
        error: () => {
            if (callFn) {
                callFn(-1);
            }
            showErrMsg(t('tipMessage.networkError'));
        },
    });
};
const handleRowClick = row => {
    tableData.value.forEach(item => {
        if (item.deliveryId === row.deliveryId) {
            if (!item.msgStatus) {
                handleRead(row.deliveryId, (code) => {
                    if (code === 0) {
                        item.msgStatus = hasRead;
                    }
                    toJump(row);
                });
            }
        }
    });
    const item = tableData.value.find(d => d.deliveryId === row.deliveryId);
    if (item && !item.msgStatus) {
        item.msgStatus = hasRead;
        handleRead(row.deliveryId);
    } else {
        toJump(row);
    }
};
const confirm = reactive({
    show: false,
});
const toClearAll = () => {
    tableData.value.forEach(item => {
        if (!item.msgStatus) {
            item.msgStatus = hasRead;
        }
    });
    handleRead();
};
const handleClearAll = () => {
    confirm.show = true;
};
const debounceClear = debounce(handleClearAll, DEBOUNCE_TIME);
getTableData();
</script>
<style lang="scss" scoped>
.dialog__header {
    position: relative;
}
.icon-clear {
    display: inline-block;
    width: 32px;
    height: 32px;
    background: url('../../assets/img/icon_qingkong.png') no-repeat center;
    position: absolute;
    right: 0;
    top: -8px;
    cursor: pointer;
}
:deep(.el-dialog__header) {
    padding-top: 10px;
}
.msg-row {
    display: flex;
    &.is-link {
        cursor: pointer;
    }
}
.msg-content {
    overflow: hidden;
    margin-right: 16px;
    margin-left: 8px;
    height: 23px;
}
.msg-dot {
    flex-shrink: 0;
}
i.icon-dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 100%;
    background-color: #f56c6c;
}
</style>
