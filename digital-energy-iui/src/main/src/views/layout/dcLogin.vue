<template>
    <div v-loading="loading" style="height: 100vh;">
        <!-- <el-empty v-if="!loading" :image-size="200" :description="$t('tipMessage.networkError')" /> -->
    </div>
</template>
<script setup>
import { useI18n } from 'vue-i18n';
import { showErrMsg } from '@/util/common.js';
import { ref } from 'vue';
const { t } = useI18n();
const loading = ref(false);
const getTokens = () => {
    const urlPath = window.location.href;
    const paramsArry = urlPath.split('?');
    const origin = window.location.origin;
    const redirectUrl = encodeURIComponent(`${origin}/#/dcLogin`);
    if (paramsArry.length > 1) {
        const urlParams = new URLSearchParams(paramsArry[1]);
        const code = urlParams.get('code');
        if (code) {
            loading.value = true;
            fetch('/api/security-manager/v1/uportal/auth/login/access-token', {
                method: 'POST',
                headers: {
                    code: code,
                    'X-redirect-uri': redirectUrl
                },
            })
                .then(response => response.json())
                .then(async resp => {
                    loading.value = false;
                    if (resp.code === 0) {
                        const origin = window.location.origin;
                        window.location.href = `${origin}/#/noPermission`;
                    } else {
                        showErrMsg(resp.message || t('tipMessage.requestError'));
                    }
                })
                .catch(() => {
                    loading.value = false;
                    showErrMsg(t('tipMessage.networkError'));
                });
        } else {
            showErrMsg(t('tipMessage.getCodeFail'));
        }
    }
};
getTokens();
</script>
