<template>
    <div class="menu" :class="{'is-fold': isFold}">
        <div
            v-for="item in menuLists"
            :key="item.id"
            class="menu-item"
        >
            <div
                class="menu-title"
                :class="{
                    'menu-sub-item': !item.children || item.children.length === 0,
                    active: currentPath.includes(item.id),
                }"
                @click="handleClick(item)"
            >
                <i class="icon-menu" :class="`icon-${item.id}`"></i>
                <span class="menu-title-content">
                    {{ item.name }}
                    <el-icon
                        v-if="item.children && item.children.length > 0"
                        class="icon-fold"
                        :class="{ expanded: item.childrenShow }"
                    >
                        <ElIconArrowDown />
                    </el-icon>
                </span>
            </div>
            <transition name="fade">
                <div v-show="item.childrenShow">
                    <div
                        v-for="subItem in item.children"
                        :key="subItem.id"
                        class="menu-sub-item"
                        :class="{ active: currentPath.includes(subItem.id) }"
                        @click="handleClick(subItem, item.id)"
                    >
                        <div class="menu-sub-title">
                            {{ subItem.name }}
                        </div>
                    </div>
                </div>
            </transition>
        </div>
    </div>
</template>
<script setup>
import { ref, computed, onMounted, inject } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
const router = useRouter();
const { t } = useI18n();
const rights = inject('rights', {});
const emit = defineEmits(['reload', 'menuClick']);
const props = defineProps({
    isFold: {
        type: Boolean,
        default: false
    }
});
const handleFold = item => {
    item.childrenShow = !item.childrenShow;
};
const currentPath = computed(() => {
    return router.currentRoute.value.path;
});
const handleClick = (item, path) => {
    emit('menuClick');
    if (item.children && item.children.length > 0) {
        handleFold(item);
    } else {
        if (item.id === router.currentRoute.value.name) {
            emit('reload');
        } else {
            let url = `/${item.id}`;
            if (path) {
                url = `/${path}/${item.id}`;
            }
            router.push(url);
        }
    }
};
const menuLists = computed(() => {
    if (rights && rights.menuListVos && rights.menuListVos.length) {
        return rights.menuListVos;
    } else {
        return [];
    }
});
const expandMenu = () => {
    const path = router.currentRoute.value.path;
    menuLists.value.forEach((d) => {
        if (path.includes(d.id)) {
            d.childrenShow = true;
        }
    });
};
onMounted(() => {
    expandMenu();
});
</script>
<style lang="scss" scoped>
.menu-sub-item {
    &:hover,
    &.active {
        background-color: #ebf8ff;
    }
}
.menu-title {
    font-size: 14px;
    line-height: 18px;
    padding: 16px 30px 16px 44px;
    cursor: pointer;
    position: relative;
    &:not(.menu-sub-item):hover {
        background-color: var(--current-row-bg-color);
    }
}
.menu-sub-title {
    font-size: 14px;
    line-height: 18px;
    padding: 16px 16px 16px 44px;
    cursor: pointer;
    position: relative;
}

/* Started by AICoder, pid:a784fx9a17ec989148fe0bbd40561235c4b134fd */
.icon-fold {
    position: absolute;
    right: 16px;
    &.expanded {
        transform: rotate(180deg);
    }
}
.menu.is-fold {
    .menu-sub-item {
        display: none;
    }
    .menu-title-content {
        display: none;
    }
    .icon-menu {
        left: 13px;
    }
    .menu-title {
        padding: 0;
        width: 42px;
        height: 50px;
        cursor: default;
        &:hover {
            background-color: transparent;
        }
        &.active {
            background-color: #ebf8ff;
        }
    }
}

/* Ended by AICoder, pid:a784fx9a17ec989148fe0bbd40561235c4b134fd */
</style>
