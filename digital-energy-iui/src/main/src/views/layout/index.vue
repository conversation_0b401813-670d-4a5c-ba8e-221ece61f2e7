/* Started by AICoder, pid:l0cf0k1e601f3fa1489f09cd7116c36099940b00 */
<template>
    <div class="layout-header">
        <div class="info">
            <!-- <span class="info-badge" @click="popNotice">
                <span v-if="noticeValue" class="info-badge-count">{{ formatNotice(noticeValue) }}</span>
                <i class="info-notification"></i>
            </span> -->
            <el-popover
                v-if="userName"
                ref="popoverRef"
                placement="bottom"
                :visible="popoverVisible"
                :width="156"
                popper-style="padding: 10px 0px;"
            >
                <div class="select-item" @click="handleLogout">
                    {{ $t('fields.logout') }}
                </div>
                <template #reference>
                    <span v-click-outside:[popperPanRef]="onClickOutside" class="user-name" @click.stop="showPopover">
                        {{ userName }}
                        <el-icon><ElIconArrowDown /></el-icon>
                    </span>
                </template>
            </el-popover>
            <span v-else>--</span>
        </div>
        <div class="title">
            <img :src="titleIdc" alt="" />
            <div class="title-name">{{ $t('fields.systemName') }}</div>
        </div>
    </div>
    <div class="layout-wrap" :class="{ 'is-fold-menu': isFoldMenu, 'layout-padding-left': rights && rights.menuListVos }">
        <div
            v-if="rights && rights.menuListVos"
            class="layout-left uedm-split-border"
            :style="{ height: `${height}px`, overflow: 'hidden' }"
        >
            <div :style="{ height: `${height - 48}px`, overflow: 'auto' }">
                <left-menu :is-fold="isFoldMenu" @reload="handleReload" @menu-click="menuClick"></left-menu>
            </div>
            <div class="fold-bar" @click="handleFold">
                <el-icon v-if="!isFoldMenu"><ElIconDArrowLeft /></el-icon>
                <el-icon v-if="isFoldMenu"><ElIconDArrowRight /></el-icon>
                <span v-if="!isFoldMenu">{{ $t('button.fold') }}</span>
            </div>
        </div>
        <div class="layout-right" :style="{ height: `${height}px`, overflow: 'auto' }">
            <router-view v-if="isRouterAlive"></router-view>
        </div>
    </div>
    <notice-list
        v-if="dialogNotice.show"
        v-model:visible="dialogNotice.show"
        @close="loopNotice"
        @refresh="queryNotice"
    ></notice-list>
</template>
<script setup>
import HTTP from '@/util/httpService';
import { ref, computed, nextTick, inject, reactive, onMounted, onBeforeUnmount } from 'vue';
import commonStore from '@/store/commonStore.js';
import LeftMenu from '@/views/layout/menu.vue';
import NoticeList from '@/views/layout/noticeList.vue';
import titleIdc from '@/assets/img/logo.png';
import { ClickOutside as vClickOutside } from 'element-plus';
import { Logout, showErrMsg } from '@/util/common.js';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const rights = inject('rights', {});
const store = commonStore();
const height = computed(() => {
    return store.height || window.innerHeight;
});
const popoverRef = ref();
const popperPanRef = computed(() => {
    return popoverRef.value?.popperRef?.contentRef;
});
const popoverVisible = ref(false);
const showPopover = () => {
    popoverVisible.value = true;
};
const onClickOutside = function () {
    popoverVisible.value = false;
};
const userName = computed(() => {
    return store.userInfo?.name || '--';
});

const handleLogout = () => {
    Logout();
};
const isRouterAlive = ref(true);
const handleReload = () => {
    isRouterAlive.value = false;
    nextTick(() => {
        isRouterAlive.value = true;
    });
};
const noticeMax = 99;
const noticeValue = ref(0);
const formatNotice = val => {
    let text = val;
    if (val > noticeMax) {
        text = `${noticeMax}+`;
    }
    return text;
};
const queryNotice = () => {
    HTTP.request('getMsgUnRead', {
        method: 'get',
        complete: resp => {
            if (resp.code === 0) {
                noticeValue.value = resp.data;
            } else {
                noticeValue.value = 0;
                showErrMsg(resp);
            }
        },
        error: () => {
            noticeValue.value = 0;
            showErrMsg(t('tipMessage.networkError'));
        },
    });
};
let noticeTimer = null;
const stopLoopNotice = () => {
    if (noticeTimer) {
        clearInterval(noticeTimer);
        noticeTimer = null;
    }
};
const loopNotice = () => {
    stopLoopNotice();
    noticeTimer = setInterval(() => {
        queryNotice();
    }, 5000);
};
const dialogNotice = reactive({
    show: false,
});
const popNotice = () => {
    dialogNotice.show = true;
    stopLoopNotice();
};

// 菜单折叠
const isFoldMenu = ref(false);
const handleFold = () => {
    isFoldMenu.value = !isFoldMenu.value;
};
const menuClick = () => {
    isFoldMenu.value = false;
};
// queryNotice();
onMounted(() => {
    // loopNotice();
});
onBeforeUnmount(() => {
    // stopLoopNotice();
});
</script>
/* Ended by AICoder, pid:l0cf0k1e601f3fa1489f09cd7116c36099940b00 */
<style lang="scss" scoped>
.layout-header {
    background: #090b1f center no-repeat;
    height: 48px;
    line-height: 48px;
    padding: 0 21px;
    position: relative;
    &::after {
        clear: both;
        content: '';
        display: block;
        height: 0;
        visibility: hidden;
    }
    .title {
        // position: absolute;
        // top: 9px;
        display: flex;
        align-items: center;
    }
    .title-name {
        margin-left: 8px;
        font-family: 'DingTalkSans';
        color: #ffffff;
        font-size: 16px;
    }
    .info {
        float: right;
        color: #fff;
    }
    .user-name {
        color: #ffffff;
        font-size: 12px;
        opacity: 85%;
        cursor: pointer;
        .el-icon {
            vertical-align: middle;
        }
    }
}
.select-item {
    line-height: 18px;
    padding: 8px 16px;
    cursor: pointer;
    &:hover {
        background-color: var(--disabled-bg-color);
    }
}
.layout-wrap {
    position: relative;
}
.layout-padding-left {
    padding-left: 200px;
}
.layout-left {
    position: absolute;
    left: 0;
    top: 0;
    width: 200px;
    box-sizing: border-box;
    border-right-style: solid;
    border-right-width: 1px;
}

.fold-bar {
    height: 47px;
    line-height: 47px;
    border-top: solid 1px #e4e4e4;
    text-align: right;
    padding-right: 13px;
    cursor: pointer;
    .el-icon {
        vertical-align: middle;
    }
}
.is-fold-menu {
    padding-left: 43px;
    .layout-left {
        width: 43px;
    }
}
.info-badge {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    height: 20px;
    line-height: 20px;
    margin-right: 25px;
    cursor: pointer;
    .info-badge-count {
        position: absolute;
        left: 12px;
        top: 2px;
        transform: translateY(-50%);
        padding: 0 6px;
        font-size: 12px;
        height: 14px;
        line-height: 12px;
        border-radius: 10px;
        background-color: #e6a23c;
        white-space: nowrap;
    }
    .info-notification {
        background: url('../../assets/img/icon_noise.png') no-repeat center;
        display: inline-block;
        width: 20px;
        height: 20px;
        line-height: 20px;
    }
}
</style>
