import i18n from '@/util/i18n.js';
const lang = localStorage.getItem('language-option');
import HTTP from '@/util/httpService.js';
// import store from '@/store/index.js';
import exportFunc from '@/util/exportHandler.js';
import { ElMessage } from 'element-plus';

export const handelMessage = message => {
    let msg = '';
    if (message) {
        let obj = {};
        try {
            obj = JSON.parse(message) || {};
            if (lang.includes('en')) {
                msg = obj['en_US'] || '';
            } else if (lang.includes('zh')) {
                msg = obj['zh_CN'] || '';
            }
        } catch (e) {
            msg = message;
        }
    }
    return msg;
};
export const showErrMsg = (message, tip, type = 'error') => {
    let msg = '';
    if (typeof message === 'string') {
        msg = handelMessage(message);
    } else {
        msg = handelMessage(message?.message) || handelMessage(message?.error);
    }
    ElMessage({
        showClose: true,
        duration: 5000,
        message: msg || tip || i18n.global.t('tipMessage.operationError'),
        type: type,
    });
};
export const showSuccessMsg = (message, tip) => {
    let msg = '';
    if (typeof message === 'string') {
        msg = handelMessage(message);
    } else {
        msg = handelMessage(message?.message) || handelMessage(message?.error);
    }
    ElMessage({
        showClose: true,
        duration: 5000,
        message: msg || tip || i18n.global.t('tipMessage.operationSuccess'),
        type: 'success',
    });
};
export const showWarningMsg = (message, tip) => {
    let msg = '';
    if (typeof message === 'string') {
        msg = handelMessage(message);
    } else {
        msg = handelMessage(message?.message) || handelMessage(message?.error);
    }
    ElMessage({
        showClose: true,
        duration: 5000,
        message: msg || tip,
        type: 'warning',
    });
};
export const formatNumber = (num, isNotReplace, occupancy) => {
    if (num || num === 0) {
        if (isNotReplace) {
            return num;
        }
        if (typeof num === 'number' && !isNaN(Number(num))) {
            const options = { useGrouping: true, minimumFractionDigits: 0, maximumFractionDigits: 2 };
            return num.toLocaleString('en-US', options);
        }
        return num;
    } else {
        return occupancy ? '--' : '';
    }
};
const StateNameLists = {
    productLevel: { // 产品梯队
        1: i18n.global.t('product.material.productLevel1'),
        2: i18n.global.t('product.material.productLevel2'),
    },
    productComponent: { // 产品组成
        1: i18n.global.t('product.material.singleProduct'),
        2: i18n.global.t('product.material.prefabricationScheme'),
        3: i18n.global.t('product.material.projectBasedSolution'),
        4: i18n.global.t('product.material.supportingMaterial'),
        5: i18n.global.t('product.material.singleAndSupporting'),
    },
};
export const filterStateName = (attr, val) => {
    let name = '';
    let value = val;
    if (typeof val === 'boolean') {
        value = val ? 'true' : 'false';
    }
    if (value || value === 0) {
        if (StateNameLists[attr] && StateNameLists[attr][value]) {
            name = StateNameLists[attr][value];
        }
    } else {
        name = '';
    }
    return name || value;
};

/** 跳转登录 */
/* Started by AICoder, pid:775f0e8d77l359714d53093aa0e33a308a41fc32 */
// const configInfo = store.getConfigInfo();
// let clientId = '************';
let clientId = '************';
const origin = window.location.origin;
const redirectUrl = encodeURIComponent(`${origin}/#/dcLogin`);
let uacAccount = 'uac.zte.com.cn';
// let uacAccount = 'uactest.zte.com.cn:5555';
// if (['developmentbuild', 'development'].includes(import.meta.env.MODE)) {
//     clientId = '************';
    uacAccount = 'uactest.zte.com.cn:5555';
// }

const getConfig = () => {
    return new Promise((resolve, reject) => {
        HTTP.request('/api/security-manager/v1/uac-config-inner/get-config', {
            method: 'get',
            complete: resp => {
                if (resp.code === 0) {
                    clientId = resp.data.appId;
                    // store.setConfigInfo(resp.data || null);
                } else {
                    showErrMsg(resp);
                }
                resolve();
            },
            error: () => {
                showErrMsg(t('tipMessage.networkError'));
                resolve();
            },
        });
    });
};

export const LogIn = async () => {
    // await getConfig();
    const loginUrl = `https://${uacAccount}/zte-sec-uac-iportalbff/auth/external/login/oauth/authorize.serv`;
    const junmpUrl = `${loginUrl}?client_id=${clientId}&response_type=code&redirect_uri=${redirectUrl}&scope=openid&state=123456`;
    window.location = junmpUrl;
};
/** 退出登录 */
export const Logout = () => {
    const loginUrl = `https://${uacAccount}/zte-sec-uac-iportal/iportal/#/auth/10001/logout`;
    const junmpUrl = `${loginUrl}?client_id=${clientId}&response_type=code&redirect_uri=${redirectUrl}&scope=openid&state=123456`;
    HTTP.request('/api/security-manager/v1/uportal/auth/logout', {
        method: 'post',
        complete: resp => {
            if (resp.code === 0) {
                window.location = junmpUrl;
            } else {
                showErrMsg(resp);
            }
        },
        error: () => {
            showErrMsg(i18n.global.t('tipMessage.networkError'));
        },
    });
};
/* Ended by AICoder, pid:775f0e8d77l359714d53093aa0e33a308a41fc32 */
/**
 * 从文档的cookie中获取指定名称的cookie值。
 */
export const getCookie = name => {
    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
        const [key, value] = cookie.split('=').map(item => item.trim());
        if (key === name) {
            return value;
        }
    }
    return '';
};

export const handleExportFile = id => {
    const languageOption = window.languageOption;
    const forgerydefense = window.forgerydefense;

    const successHandler = res => {
        if (res.type !== 'application/json') {
            return;
        }
        const { code, message: msg } = res.data;
        if (code === 0) {
            ElMessage.success(msg);
        } else {
            ElMessage.error(msg);
        }
    };

    const errorHandler = err => {
        ElMessage.error(err);
    };

    const config = {
        headers: {
            'accept-language': languageOption,
        },
    };

    exportFunc({
        url: `/api/document-manager/v1/uportal/file/download?fileId=${id}&forgerydefense=${forgerydefense}`,
        errorHandler,
        successHandler,
        config,
    });
};
export const getNodeUrl = (node) => {
    // 获取当前节点的全路径
    let nodeUrl = [];
    let nodeUrlIds = [];
    let getNodeName = function (node) {
        if (node.data) {
            if (node.data.id) {
                nodeUrl.unshift(node.data.name);
                nodeUrlIds.unshift(node.data.id);
                if (node.parent) {
                    getNodeName(node.parent);
                }
            }
        }
    };
    getNodeName(node);
    return {
        names: nodeUrl,
        ids: nodeUrlIds,
    };
};
export const openNewWindow = (url, param) => {
    const origin = window.location.origin;
    let openUrl = `${origin}/#/${url}`;
    if (param) {
        openUrl = `${origin}/#/${url}?${param}`;
    }
    window.open(openUrl, '_blank');
};

export const formatFiles = (fileInfos) => {
    let text = '';
    let textArray = [];
    if (Array.isArray(fileInfos)) {
        fileInfos.forEach((d) => {
            if (d.fileName) {
                textArray.push(d.fileName);
            }
        });
    }
    if (textArray.length) {
        text = textArray.join(', ');
    }
    return text;
};

// 加载图片时携带headers
export const loadImageWithHeaders = url => {
    return new Promise((resolve, reject) => {
        if (!url) {
            resolve('');
            return;
        }

        fetch(url, {
            method: 'GET',
            headers: new Headers({
                forgerydefense: window.forgerydefense || '',
            }),
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.blob();
            })
            .then(blob => URL.createObjectURL(blob))
            .then(resolve)
            .catch(error => {
                console.error('Error loading image:');
                resolve('');
            });
    });
};

export const checkPermission = (rightsPermissions, id, permissionCode) => {
    const permissions = rightsPermissions?.[id];
    return permissions?.some(item => item?.permissionCode === permissionCode) || false;
};
export const getTextWidth = (text, font = 14) => {
    const span = document.createElement('temporaryspan');
    span.style.fontSize = `${font}px`;
    span.style.visibility = 'hidden';
    span.style.position = 'absolute';
    span.style.whiteSpace = 'nowrap';
    span.style.fontWeight = 'bolder';
    if (typeof span.textContent !== 'undefined') {
        span.textContent = text;
    } else {
        span.innerText = text;
    }
    document.body.appendChild(span);
    const width = span.clientWidth;
    document.body.removeChild(span);
    return width + 50;
};
export const isJsonString = (str) => {
    try {
        if (typeof JSON.parse(str) === 'object') {
            return true;
        }
    } catch (e) {
        // ignore
    }
    return false;
};

/** 跳转页面查询是否有显示权限 */
export const queryPageShowRights = (id, type) => {
    return new Promise((resolve) => {
        HTTP.request('userVerify', {
            method: 'post',
            urlParam: {
                resourceId: id,
                type: type,
            },
            complete: resp => {
                if (resp.code === 0) {
                    resolve(resp.data || false);
                } else {
                    showErrMsg(resp);
                    resolve(false);
                }
            },
            error: () => {
                resolve(false);
            },
        });
    });
};

// 搜索接口埋点
export const apiPayload = {
    param1: 7, // 物料
    param2: 8, // 文档
    param3: 9, // FAQ
};
export const trackUserBehavior = (productCategoryId, operationType) => {
    HTTP.request('setBuryingPoint', {
        method: 'post',
        data: {
            productCategoryId,
            operationType,
        },
        complete: resp => {
            if (resp.code !== 0) {
                showErrMsg(resp);
            }
        },
        error: err => {
            showErrMsg(err);
        },
    });
};
// 页面访问埋点接口
export const apiPayloadType = {
    param1: 1, // 页面访问-品牌
    param2: 2, // 页面访问-物料
    param3: 3, // 页面访问-文档
    param4: 4, // 页面访问-FAQ
};
export const trackPageAccess = (resourceId, productCategoryId, type) => {
    HTTP.request('getBuryingPoint', {
        method: 'post',
        data: {
            resourceId,
            productCategoryId,
            type,
        },
        complete: resp => {
            if (resp.code !== 0) {
                showErrMsg(resp);
            }
        },
        error: err => {
            showErrMsg(err);
        },
    });
};
// 文档下载埋点接口
export const apiDownloadType = {
    param1: 5, // 文档预览
    param2: 6, // 文档下载
};
export const getAddRecordDocDownLoad = (resourceId, operationType) => {
    HTTP.request('getAddRecordDocDownLoad', {
        method: 'post',
        data: {
            resourceId,
            operationType,
        },
        complete: resp => {
            if (resp.code !== 0) {
                showErrMsg(resp);
            }
        },
        error: err => {
            showErrMsg(err);
        },
    });
};
export const getDocumentUpdate = (documentId, productCategoryId) => {
    HTTP.request('getAddRecord', {
        method: 'post',
        data: {
            documentId,
            productCategoryId,
        },
        complete: resp => {
            if (resp.code !== 0) {
                showErrMsg(resp);
            }
        },
        error: err => {
            showErrMsg(err);
        },
    });
};
// 物料更新埋点接口
export const getMaterialsUpdate = (resourceId, typeId) => {
    HTTP.request('getBuryingPointMaterials', {
        method: 'post',
        data: {
            resourceId,
            typeId,
            operationType: 1, // 1代表物料
        },
        complete: resp => {
            if (resp.code !== 0) {
                showErrMsg(resp);
            }
        },
        error: err => {
            showErrMsg(err);
        },
    });
};