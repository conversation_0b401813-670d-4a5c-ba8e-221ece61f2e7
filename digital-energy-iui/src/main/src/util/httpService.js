import axios from 'axios';
import { ElMessage as Message } from 'element-plus';
let encryptFailMessage = null;
import handelErrCode from '@/util/errCodeProcess'; // 注意路径
const ContentTypeJSON = 'application/json;charset=UTF-8';
axios.defaults.headers.post['Content-Type'] = ContentTypeJSON;
axios.defaults.headers.put['Content-Type'] = ContentTypeJSON;
axios.defaults.headers['delete']['Content-Type'] = ContentTypeJSON;
axios.defaults.headers['forgerydefense'] = window.forgerydefense;
let lang = localStorage['language-option'];
axios.defaults.headers['accept-language'] = (lang && lang.substr(1, lang.length - 2)) || navigator.language || 'zh-CN';
import { getCookie } from '@/util/common.js';
if (import.meta.env.MODE === 'development') {
    axios.defaults.headers['X-dc-access-token'] = getCookie('X-dc-access-token');
    axios.defaults.headers['X-dc-token'] = getCookie('X-dc-token');
    axios.defaults.headers['X-dc-uid'] = getCookie('X-dc-uid');
    axios.defaults.headers['refresh-token'] = getCookie('refresh-token');
}
let HttpService = {};
const api_document_manager_v1_uportal = '/api/document-manager/v1/uportal';
const api_product_manager_v1_uportal = '/api/product-manager/v1/uportal';
const api_system_manager_v1_uportal = '/api/security-manager/v1/uportal';
const api_process_manager_v1_uportal = '/api/process-manager/v1/uportal';
const api_project_manager_v1_uportal = '/api/project-manager/v1/uportal';
let urlMap = {
    getProductCategory: `${api_product_manager_v1_uportal}/product-category/get-by-user`,
    getAllProductCategory: `${api_product_manager_v1_uportal}/product-category/get-all`,
    getAllProductCategoryTree: `${api_product_manager_v1_uportal}/product-category/get-all-tree`,
    getOptLicense: '/api/security-manager/v1/uportal/auth/user/verify',
    getTokens: '/api/uportal/uac-api/get-uac-tokens',
    getLoginUser: '/api/uportal/uac-api/get-uac-login-user',
    getDocumentList: `${api_document_manager_v1_uportal}/document/query`,
    addDocument: `${api_document_manager_v1_uportal}/document/add`,
    updateDocument: `${api_document_manager_v1_uportal}/document/edit`,
    getDocumentDetail: `${api_document_manager_v1_uportal}/document/detail`,
    documentDelete: `${api_document_manager_v1_uportal}/document/delete`,
    documentItemDelete: `${api_document_manager_v1_uportal}/document/item-delete`,
    queryPdmList: `${api_document_manager_v1_uportal}/document/query`,
    getMaterialList: `${api_product_manager_v1_uportal}/material/query-by-conditions`,
    getFuzzyMaterialList: `${api_product_manager_v1_uportal}/material/fuzzy-condition-query`,
    submitImportMaterial: `${api_product_manager_v1_uportal}/material/batch-submit`,
    importMaterialTemplateDownload: `${api_product_manager_v1_uportal}/material/templateDownload`,
    queryMaterialPurchaseMode: `${api_product_manager_v1_uportal}/material/query-purchase-mode`,
    getPurchasOptions: `${api_product_manager_v1_uportal}/product-brand/get-purchas`,
    querySalesStatus: `${api_product_manager_v1_uportal}/material/query-sales-status`,
    getProductMaterialList: `${api_product_manager_v1_uportal}/material/query-by-conditions`,
    draftBatchOperation: `${api_product_manager_v1_uportal}/material/batch-operation`,
    queryUacUserInfo: `${api_system_manager_v1_uportal}/auth/query-uac-user-info`,
    updateProductCategory: `${api_product_manager_v1_uportal}/product-category/update-by-id`,
    getMsgUnRead: '/api/security-manager/v1/uportal/msg/unRead', // 右上角未读消息数量
    msgClickRead: '/api/security-manager/v1/uportal/msg/oneclickRead', // 消息一键已读
    getMsgList: '/api/security-manager/v1/uportal/msg/page', // 获取消息列表
    // 物料
    queryMaterialFuzzy: `${api_product_manager_v1_uportal}/material/fuzzy-query`,
    queryMaterialAccurate: `${api_product_manager_v1_uportal}/material/accurate-query`,
    queryMaterialDetail: `${api_product_manager_v1_uportal}/material/get-by-id`,
    queryMaterialDetailVersion: `${api_product_manager_v1_uportal}/material/query-version`,
    queryProductCategoryById: `${api_product_manager_v1_uportal}/product-category/get-by-id`,
    queryGroupPDMDetail: `${api_product_manager_v1_uportal}/product-group/detail`,
    queryMaterialVersion: `${api_product_manager_v1_uportal}/material/query-version`,
    queryMaterialStatus: `${api_product_manager_v1_uportal}/material/query-material-status`,
    queryProductGroup: `${api_product_manager_v1_uportal}/product-group/query`,
    sortProductGroup: `${api_product_manager_v1_uportal}/product-group/update-sort`,
    deleteProductGroup: `${api_product_manager_v1_uportal}/product-group/delete`,
    addProductGroup: `${api_product_manager_v1_uportal}/product-group/add`,
    editProductGroup: `${api_product_manager_v1_uportal}/product-group/edit`,
    addMaterial: `${api_product_manager_v1_uportal}/material/add`,
    deleteMaterial: `${api_product_manager_v1_uportal}/material/delete`,
    editMaterial: `${api_product_manager_v1_uportal}/material/edit`,
    operationMaterial: `${api_product_manager_v1_uportal}/material/process-operation`,
    chooseMaterialPDM: `${api_product_manager_v1_uportal}/pdm/material/product-specification-model`,
    chooseGroupPDM: `${api_product_manager_v1_uportal}/pdm/group/product-specification-model`,
    getPdmProductCategory: `${api_product_manager_v1_uportal}/pdm/product-large-category`,
    salesCodeCheck: `${api_product_manager_v1_uportal}/material/sales-code-check`,
    pdmSynchronization: `${api_product_manager_v1_uportal}/material/pdm-sync`,
    implementAssociatedMaterials: `${api_project_manager_v1_uportal}/project/material-association/do-associate`, // 执行关联物料
    queryAssociatedMaterialsId: `${api_project_manager_v1_uportal}/project/material-association/material/associated-simple`, // 查询已关联的物料（id+名称）
    queryAssociatedMaterials: `${api_project_manager_v1_uportal}/project/material-association/material/no-page-associated`, // 查询已关联物料列表
    maintainSpecification: `${api_product_manager_v1_uportal}/material/maintain-specification`, // 新增/修改物料规格书备注
    querySpecification: '/api/product-manager/v1/uportal/material/specification', // 查询物料详情支持规格书
    onceSubmit: '/api/product-manager/v1/uportal/demand/onceSubmit', // 一键上架
    batchDelMaterials: '/api/product-manager/v1/uportal/material/delMaterials', // 产品库-物料批量删除
    updMaterials: '/api/product-manager/v1/uportal/material/UpdMaterials', // 产品库-物料导入编辑-导入提交
    recommendedMaterials: '/api/product-manager/v1/uportal/intelligent-recommendation/recommended-materials', // 推荐物料
    // 产品库-FAQ
    frequentlyAskedQuestionsDetail: `${api_system_manager_v1_uportal}/faq`, // 详情
    frequentlyAskedQuestionsAdd: `${api_system_manager_v1_uportal}/faq/add`, // 新增
    frequentlyAskedQuestionsList: `${api_system_manager_v1_uportal}/faq/query`, // 列表
    frequentlyAskedQuestionsDelete: `${api_system_manager_v1_uportal}/faq/delete`, // 删除
    frequentlyAskedQuestionsDocumentUpdate: `${api_system_manager_v1_uportal}/faq/update`, // 编辑提交
    frequentlyAskedQuestionsQuery: `${api_document_manager_v1_uportal}/document-category/query`, // 上传文档-文档类型列表
    frequentlyAskedQuestionsDetailList: `${api_document_manager_v1_uportal}/document/query-by-resourceId`, // 详情-文档类型列表
    faqQueryProductCategory: `${api_product_manager_v1_uportal}/product-category/get-by-user`,
    // 产品库-品牌
    duplicateNameQuery: `${api_product_manager_v1_uportal}/product-brand/duplicate-name-query`, // 品牌名称是否存在
    neSelectScore: `${api_product_manager_v1_uportal}/product-brand/new-grade-standard`, // 选型评分上下限
    productBrandDetail: `${api_product_manager_v1_uportal}/product-brand/get-detail`, // 详情
    productBrandAdd: `${api_product_manager_v1_uportal}/product-brand/add`, // 新增
    productBrandList: `${api_product_manager_v1_uportal}/product-brand/reduced-fuzzy-search`, // 列表模糊查询
    queryProductBrandList: `${api_product_manager_v1_uportal}/product-brand/advanced-query`, // 列表精确查询
    productBrandDelete: `${api_product_manager_v1_uportal}/product-brand/delete`, // 删除
    productBrandUpdate: `${api_product_manager_v1_uportal}/product-brand/update`, // 编辑提交
    productBrandAssociatedList: `${api_document_manager_v1_uportal}/product-brand/associated-query`, // 关联文档列表
    productBrandAssociatedAdd: `${api_document_manager_v1_uportal}/product-brand/associated-add`, // 添加关联文档
    productBrandAssociatedDelete: `${api_document_manager_v1_uportal}/product-brand/associated-delete`, // 删除关联文档
    deleteByResourceId: ' /api/document-manager/v1/uportal/document/delete-by-resourceId',
    // 产品库-文档
    knowledgeBaseAdvancedList: `${api_document_manager_v1_uportal}/document/advanced-search`, // 高级查询列表
    knowledgeBaseCommonList: `${api_document_manager_v1_uportal}/document/common-search`, // 普通查询列表
    knowledgeBaseCommonByUserList: `${api_product_manager_v1_uportal}/product-category/get-by-user`, // 产品小类列表
    getPreviewUrl: '/api/document-manager/v1/uportal/doc-open-api/getPreviewUrl', // 文档预览
    // 我的工作台
    queryPendingProcessingList: `${api_process_manager_v1_uportal}/station/approval/pending`, // 待我处理
    queryInitiatedList: `${api_process_manager_v1_uportal}/station/approval/initiated`, // 我发起的
    queryProcessedList: `${api_process_manager_v1_uportal}/station/approval/completed`, // 我已处理
    queryApprovalDetail: `${api_process_manager_v1_uportal}/station/approval/detail`, // 审批详情
    queryApprovalMktDetail: `${api_process_manager_v1_uportal}/station/approval/mktDetail`, // 审批详情-投标
    queryApprovalMaterials: `${api_process_manager_v1_uportal}/station/approval/detail/materials`, // 审批详情-物料
    submitApproval: `${api_process_manager_v1_uportal}/station/approval/submit`, // 提交审批
    withdrawApproval: `${api_process_manager_v1_uportal}/station/approval/withdraw`, // 撤回审批
    queryMyProducts: `${api_process_manager_v1_uportal}/station/products`, // 我的产品
    queryProductCategory: `${api_product_manager_v1_uportal}/product-category/get-by-ids`, // 产品分类
    queryTenderApplicationData: `${api_process_manager_v1_uportal}/demand/detail`, // 招标申请详情
    submitTenderAppplication: `${api_process_manager_v1_uportal}/demand/submit`, // 提交招标申请
    getDemandManagementLectotypeById: `${api_product_manager_v1_uportal}/demand/getDemandManagementLectotypeById`, // 获取选型单跳转所需的数据
    // 项目管理
    getAreaTree: `${api_project_manager_v1_uportal}/project/area-query`, // 地区树
    getProjectAreaTree: `${api_project_manager_v1_uportal}/project/area-project`, // 地区项目树
    getProjectList: `${api_project_manager_v1_uportal}/project/query`, // 项目列表
    addProject: `${api_project_manager_v1_uportal}/project/add`, // 新增项目
    editProject: `${api_project_manager_v1_uportal}/project/edit`, // 编辑项目
    deleteProjectList: `${api_project_manager_v1_uportal}/project/delete`, // 删除项目
    projectListDetail: `${api_project_manager_v1_uportal}/project/detail`, // 项目详情
    getProjectStage: `${api_project_manager_v1_uportal}/project/stage`, // 项目阶段下拉选项
    getProjectStagesInfo: `${api_project_manager_v1_uportal}/brand-guide/get-project-phase`, // 品牌引导-项目阶段信息
    getStageBrandList: `${api_project_manager_v1_uportal}/brand-guide/list`, // 品牌引导-项目阶段对应品牌列表
    getBrandGuidanceRecord: `${api_project_manager_v1_uportal}/brand-guide/get-record`, // 品牌引导-品牌引导详情
    getProductSubCategoryList: `${api_project_manager_v1_uportal}/brand-guide/list-product-category`, // 品牌引导详情-新选产品小类（查询）
    submitBrandGuidance: `${api_project_manager_v1_uportal}/brand-guide/submit`, // 品牌引导-品牌引导详情提交
    getGuidanceHistory: `${api_project_manager_v1_uportal}/brand-guide/get-history`, // 品牌引导-引导历史左侧时间线
    getGuidanceHistoryDetail: `${api_project_manager_v1_uportal}/brand-guide/get-history-detail`, // 品牌引导-引导历史右侧表格
    customerSearch: `${api_project_manager_v1_uportal}/project/customer`, // 客户模糊查询
    projectFuzzyQuery: `${api_project_manager_v1_uportal}/item/query`, // 项目管理模糊匹配查询
    projectQueryId: `${api_project_manager_v1_uportal}/item/query-by-id`, // 项目详情-概览信息-基本信息查询
    projectQueryProjectProgress: `${api_project_manager_v1_uportal}/item/query-item-progress`, // 项目详情-概览信息-项目里程牌信息查询
    projectQueryHandpverHistory: `${api_project_manager_v1_uportal}/item/query-handover-history`, // 项目详情-概览信息-项目里程牌-交接历史查询
    projectQueryDeliveryHistory: `${api_project_manager_v1_uportal}/item/query-delivery-history`, // 项目详情-概览信息-项目里程牌-PAC(交付)历史查询
    projectQueryHandpverById: `${api_project_manager_v1_uportal}/item/query-handover-by-id`, // 项目详情-交接信息-基础信息查询
    projectQueryHandpverEdit: `${api_project_manager_v1_uportal}/item/handover-edit`, // 项目详情-交接信息-基础信息编辑
    projectAddLegacy: `${api_project_manager_v1_uportal}/item/add-legacy-issues`, // 项目详情-交接信息-遗留问题新增
    projectEditLegacy: `${api_project_manager_v1_uportal}/item/edit-legacy-issues`, // 项目详情-交接信息-遗留问题编辑
    projectDeleteLegacy: `${api_project_manager_v1_uportal}/item/delete-legacy-issues`, // 项目详情-交接信息-遗留问题删除
    projectQueryLegacy: `${api_project_manager_v1_uportal}/item/query-legacy-issues`, // 项目详情-交接信息-遗留问题查询
    projectQuerySubmissionInfo: `${api_project_manager_v1_uportal}/item/deepen-design/query-submission-information`, // 项目详情-工程深化设计-提资查询
    projectQueryDeepenImpl: `${api_project_manager_v1_uportal}/item/deepen-design/query-deepen-implementation`, // 项目详情-工程深化设计-深化实施查询
    projectEditDeepenImpl: `${api_project_manager_v1_uportal}/item/deepen-design/edit-implementation`, // 项目详情-工程深化设计-深化实施编辑
    projectQueryAllRecords: `${api_project_manager_v1_uportal}/item/deepen-design/query-all-record`, // 项目详情-工程深化设计-施工图会审查询
    projectQueryRecordById: `${api_project_manager_v1_uportal}/item/deepen-design/query-by-record-id`, // 项目详情-工程深化设计-施工图会审详情
    projectDeleteReviewRecord: `${api_project_manager_v1_uportal}/item/deepen-design/delete-review-record`, // 项目详情-工程深化设计-施工图会审删除
    projectEditReviewRecord: `${api_project_manager_v1_uportal}/item/deepen-design/edit-review-record`, // 项目详情-工程深化设计-施工图会审编辑
    projectAddReviewRecord: `${api_project_manager_v1_uportal}/item/deepen-design/add-review-record`, // 项目详情-工程深化设计-施工图会审新增
    projectQueryImplHistory: `${api_project_manager_v1_uportal}/item/deepen-design/query-implementation-history`, // 项目详情-工程深化设计-历史查询
    projectIssueSubmitTask: `${api_process_manager_v1_uportal}/item/deepen-design/product-upgrade/add`, // 提资任务下发
    projectQueryOverview: `${api_project_manager_v1_uportal}/item/deepen-design/query-overview`, // 工程深化设计信息-信息查询
    projectQueryCategoryByItemId: `${api_project_manager_v1_uportal}/item/deepen-design/query-category-by-item-id`, // 根据项目id查询产品小类
    projectDocumentAdd: `${api_document_manager_v1_uportal}/document/add-product-upgrade`, // 产品提资-附件保存
    projectQueryTaskDetailByItemId: `${api_process_manager_v1_uportal}/item/deepen-design/product-upgrade/process-detail`, // 任务处理-任务详情
    projectSubmitTaskByItemId: `${api_process_manager_v1_uportal}/item/deepen-design/product-upgrade/submit`, // 任务处理-提交
    projectQueryFlowDetailByItemId: `${api_process_manager_v1_uportal}/item/deepen-design/product-upgrade/flow-detail`, // 我已处理-详情
    projectQueryDelivery: `${api_project_manager_v1_uportal}/item/query-project-delivery`, // 项目详情-工程交付-查询
    projectEditDelivery: `${api_project_manager_v1_uportal}/item/edit-project-delivery`, // 项目详情-工程交付-编辑
    projectQueryHandoverStatus: `${api_project_manager_v1_uportal}/item/query-handover-status`, // 交接状态枚举查询
    projectQueryCurrentStage: `${api_project_manager_v1_uportal}/item/query-current-stage`, // 当前项目阶段枚举查询
    projectQueryOverAll: `${api_project_manager_v1_uportal}/item/query-over-all`, // 项目整体状态枚举查询
    projectAreaQuery: `${api_project_manager_v1_uportal}/item/area-query`, // 地区项目树查询
    projectDocumentQuery: `${api_document_manager_v1_uportal}/document/query-by-ascriptionId-type`, // 交接文档列表
    // 看板
    getBoardCategoryList: `${api_project_manager_v1_uportal}/notice-board/category/query`, // 品牌引导产品看板列表
    getBoardProjectList: `${api_project_manager_v1_uportal}/project/dashboard/projects-score`, // 品牌引导项目看板列表
    getBoardProjectDetail: `${api_project_manager_v1_uportal}/project/dashboard/guide-product-categories`, // 品牌引导项目看板详情
    getBoardCategoryChart: `${api_project_manager_v1_uportal}/project/dashboard/line-chart`, // 看板产品小类得分chart图
    getBoardOverView: '/api/security-manager/v1/uportal/stat/statisticOverView', // 看板-系统访问统计-总览
    // 投标
    getSpeDocumentType: `${api_document_manager_v1_uportal}/document-category/query-by-type`, // 查询指定文档小类
    getSpeDocumentList: `${api_document_manager_v1_uportal}/document/query-by-ascriptionId`, // 查询指定文档列表
    startBidding: `${api_project_manager_v1_uportal}/project/bidding-stage/start-bidding`, // 启动投标
    deleteTempDocument: `${api_document_manager_v1_uportal}/document/temp/delete-by-ascriptionId`, // 删除标书文档
    addBiddingDocument: `${api_document_manager_v1_uportal}/document/temp/add`, // 新增标书文档
    updateBiddingDocument: `${api_document_manager_v1_uportal}/document/temp/edit`, // 编辑标书文档
    updateBidding: `${api_project_manager_v1_uportal}/project/bidding-stage/update-bidding-information`, // 更新投标信息
    biddingDetail: `${api_project_manager_v1_uportal}/project/bidding-stage/query-by-id`, // 招标信息详情
    getSpeProductCategory: `${api_project_manager_v1_uportal}/bidding-document-clarification/query-productCategory`, // 根据项目ID查询指定产品小类下拉选项
    getSpeProductCategoryTree: '/api/project-manager/v1/uportal/bidding-document-clarification/query-productCategory-tree', // 根据项目ID查询指定产品小类下拉选项树结构
    getBidClarifyList: `${api_project_manager_v1_uportal}/bidding-document-clarification/query`, // 标书澄清列表
    addBidClarify: `${api_project_manager_v1_uportal}/bidding-document-clarification/add`, // 添加标书澄清
    editBidClarify: `${api_project_manager_v1_uportal}/bidding-document-clarification/edit`, // 编辑标书澄清
    deleteBidClarify: `${api_project_manager_v1_uportal}/bidding-document-clarification/delete`, // 删除标书澄清
    detailBidClarify: `${api_project_manager_v1_uportal}/bidding-document-clarification/detail`, // 查看标书澄清
    queryApprovalTaskDetail: `${api_process_manager_v1_uportal}/station/approval/task-detail`,
    queryApprovalManualTaskDetail: '/api/process-manager/v1/uportal/manual/approval/manual-task-detail',
    approvalManualAccept: '/api/process-manager/v1/uportal/manual/approval/manual-accept',
    bidApprovalAccept: `${api_process_manager_v1_uportal}/station/approval/accept`,
    approvalManualFlowDetail: '/api/process-manager/v1/uportal/manual/approval/manual-flow-detail',
    queryProjectTaskDetails: '/api/process-manager/v1/uportal/station/approval/project/task-details', // 商机信息投标支持信息中的任务列表
    // 标书分解
    addBidDecomposition: `${api_project_manager_v1_uportal}/project/bid-decomposition/bill-of-quantities/add`, // 新增工程量清单
    batchAddBidDecomposition: `${api_project_manager_v1_uportal}/project/bid-decomposition/bill-of-quantities/batch-add`, // 批量新增工程量清单
    editBidDecomposition: `${api_project_manager_v1_uportal}/project/bid-decomposition/bill-of-quantities/edit`, // 更新工程量清单
    batchEditBidDecomposition: `${api_project_manager_v1_uportal}/project/bid-decomposition/bill-of-quantities/batch-edit`, // 批量更新工程量清单
    deleteBidDecomposition: `${api_project_manager_v1_uportal}/project/bid-decomposition/bill-of-quantities/delete`, // 删除工程量清单
    batchDeleteBidDecomposition: `${api_project_manager_v1_uportal}/project/bid-decomposition/bill-of-quantities/batch-delete`, // 批量删除工程量清单
    queryBidDecomposition: `${api_project_manager_v1_uportal}/project/bid-decomposition/bill-of-quantities/query-by-project-id`, // 根据项目id查询所属工程量清单列表
    detailBidDecomposition: `${api_project_manager_v1_uportal}/project/bid-decomposition/bill-of-quantities/query-by-id`, // 根据工程量清单id查询工程量清单
    querySupplyBidDecomposition: `${api_project_manager_v1_uportal}/project/bid-decomposition/bill-of-quantities/query-supply`, // 获取所有供方枚举
    getProjTree: '/api/product-manager/v1/uportal/product-category/get-proj-tree', // 无产品小类树
    getInstallationSupervision: '/api/project-manager/v1/uportal/project/bid-decomposition/bill-of-quantities/installationSupervision', // 获取所有督导方式
    // 标书分析
    queryBidAnalysisDetailByProductId: '/api/project-manager/v1/uportal/bidAnalysis/detailByProductId',
    queryBidAnalysisDetail: '/api/project-manager/v1/uportal/bidAnalysis/detail',
    saveBidAnalysisEdit: '/api/project-manager/v1/uportal/bidAnalysis/updRes',
    queryBidAnalysisHistory: '/api/project-manager/v1/uportal/bidAnalysis/hisList',
    queryBidAnalysisResultByCategoryId: '/api/project-manager/v1/uportal/bidAnalysis/getResByCategoryId',
    bidAnalysisAnalysis: '/api/project-manager/v1/uportal/bidAnalysis/analysis',
    bidAnalysisTree: '/api/project-manager/v1/uportal/bidAnalysis/queryTreeById',
    // 投标结论确认
    queryDropDown: '/api/project-manager/v1/uportal/bidding-conclusion/query-drop-down', // 获取投标结论下拉框数据
    conclusionAddUpdate: '/api/project-manager/v1/uportal/bidding-conclusion/add-or-update', // 新增投标结论
    conclusionFirstFill: '/api/project-manager/v1/uportal/bidding-conclusion/first-fill', // 投标结论首次填写
    conclusionDetail: '/api/project-manager/v1/uportal/bidding-conclusion/detail', // 投标结论详情
    managementAddUpdate: '/api/project-manager/v1/uportal/project-handover/add-or-update', // 新增修改售前售后交接信息
    projectHandoverDetail: '/api/project-manager/v1/uportal/project-handover/detail', // 售前售后交接信息详情
    // 核心参数
    queryCoreParameter: `${api_product_manager_v1_uportal}/core-param/list`, // 查询列表
    addCoreParameter: `${api_product_manager_v1_uportal}/core-param/add`, // 新增
    detailCoreParameter: `${api_product_manager_v1_uportal}/core-param/detail`, // 详情
    updateCoreParameter: `${api_product_manager_v1_uportal}/core-param/update`, // 更新
    deleteCoreParameter: `${api_product_manager_v1_uportal}/core-param/delete`, // 删除
    // 项目详情
    queryMaterialAssociationDetails: `${api_project_manager_v1_uportal}/project/material-association/details`, // 配置清单详情查询
    queryTaskOverview: `${api_process_manager_v1_uportal}/station/approval/task-overview`, // 任务概览
    queryTaskDetails: `${api_process_manager_v1_uportal}/station/approval/task-details`, // 任务详情
    queryApprovalTask: `${api_process_manager_v1_uportal}/station/approval/tasks`, // 任务列表
    taskDistribution: `${api_process_manager_v1_uportal}/manual/approval/generat-task`, // 任务下发

    // 需求管理
    queryPageDemand: `${api_product_manager_v1_uportal}/demand/pageDemand`, // 分页查询需求列表
    queryDemandDetail: `${api_product_manager_v1_uportal}/demand/detailDemand`, // 需求详情
    queryDemandListLectotype: `${api_product_manager_v1_uportal}/demand/pageLectotype`, // 分页查询需求选型清单
    querDemandQuantity: `${api_product_manager_v1_uportal}/demand/pageQuantity`, // 分页查询需求工程量清单
    addLectotype: `${api_product_manager_v1_uportal}/demand/addLectotype`, // 新增选型
    updLectotype: `${api_product_manager_v1_uportal}/demand/updLectotype`, // 编辑选型
    delLectotype: `${api_product_manager_v1_uportal}/demand/delLectotype`, // 删除选型
    delLectotypes: `${api_product_manager_v1_uportal}/demand/delLectotypes`, // 批量删除选型
    addLectotypeMaterial: `${api_product_manager_v1_uportal}/demand/addLectotypeMaterial`, // 新增选型物料关联
    delLectotypeMaterial: `${api_product_manager_v1_uportal}/demand/delLectotypeMaterial`, // 批量删除选型物料关联
    detailDemandLectotypeMaterial: `${api_product_manager_v1_uportal}/demand/detailDemandLectotypeMaterial`, // 选型物料关联详情
    fuzzyQueryMaterial: `${api_product_manager_v1_uportal}/material/fuzzy-selection-query`, // 查询选型单对应物料列表- 模糊查询
    selectionAssociationConditions: `${api_product_manager_v1_uportal}/material/selection-association-conditions`, // 查询选型单对应物料列表-高级查询
    queryByLectotype: `${api_product_manager_v1_uportal}/cost-management/query-by-lectotype`, // 查询选型单 招采成本
    editProcurementCost: `${api_product_manager_v1_uportal}/cost-management/edit-procurement-cost`, // 编辑选型单招采成本
    // 成本管理
    selectionConditionQuery: `${api_product_manager_v1_uportal}/cost-management/selection-condition-query`, // 招采成本-选型单分页查询，路径待选型设计完后调整
    costBatchUpdate: `${api_product_manager_v1_uportal}/material/cost-batch-update`, // 成本拆分 物料成本更新
    queryHistory: `${api_product_manager_v1_uportal}/cost-management/query-history`, // 选型单招采成本历史查询
    getAllStatus: `${api_product_manager_v1_uportal}/demand/get-all-status`, // 获取选型单状态
    getCostById: `${api_product_manager_v1_uportal}/demand/get-by-id`, // 查询选型单详情
    queryBySelection: `${api_product_manager_v1_uportal}/material/query-by-selection`, // 分页查询选型单关联物料
    editCost: `${api_product_manager_v1_uportal}/material/submit-material-cost`, // 单个物料成本修改
    queryCost: '/api/product-manager/v1/uportal/material/query-material-cost', // 单个物料成本修改
    // 产品需求看板
    requirementBoardAdd: `${api_project_manager_v1_uportal}/brand-guide/add`, // 需求看板新增
    requirementBoardEdit: `${api_product_manager_v1_uportal}/requirement-board/edit`, // 需求看板编辑
    requirementBoardGetByCategoryId: `${api_product_manager_v1_uportal}/requirement-board/get-by-category-id`, // 根据产品小类及项目id查询看板
    productBrandQueryByCategory: `${api_product_manager_v1_uportal}/product-brand/query-by-category`, // 查询产品小类下的品牌
    queryByProjectId: `${api_product_manager_v1_uportal}/requirement-board/query-by-project-id`, // 根据商机（原项目）id分页查询需求看板
    queryAuthorizedSubcategory: `${api_product_manager_v1_uportal}/product-category/query-authorized-subcategory`, // 查询有权限填写看板的产品小类（品牌引导存在的产品小类中再次筛选
    getOptionalTree: `${api_product_manager_v1_uportal}/product-category/get-optional-tree`, // 过滤掉项目品牌引导过的产品小类
    getAllMode: `${api_product_manager_v1_uportal}/requirement-board/get-all-mode`, // 资源确认模式枚举查询
    getAllCode: `${api_product_manager_v1_uportal}/requirement-board/get-code-status`, // 代码状态枚举查询
    // 回音墙
    getEchoList: '/api/product-manager/v1/uportal/echo/list',
    addEcho: '/api/product-manager/v1/uportal/echo/add',
    editEcho: '/api/product-manager/v1/uportal/echo/upd',
    deleteEcho: '/api/product-manager/v1/uportal/echo/del',
    getEchoDetail: '/api/product-manager/v1/uportal/echo/treeComment',
    addComment: '/api/product-manager/v1/uportal/echo/addComment',
    editComment: '/api/product-manager/v1/uportal/echo/updComment',
    deleteComment: '/api/product-manager/v1/uportal/echo/delComment',
    getUserBySearchText: '/api/security-manager/v1/uportal/user/getUserBySearchText',
    getDeptTree: '/api/security-manager/v1/uportal/dept/list-dept-tree', // 部门树
    getStatisticLogin: '/api/security-manager/v1/uportal/stat/statisticLogin',
    // 页面访问-组织
    queryOrgTotal: '/api/security-manager/v1/uportal/access/total',
    getOverviewAll: '/api/security-manager/v1/uportal/access/overview-all', // 页面访问统计
    querySubClassTop: '/api/security-manager/v1/uportal/access/top20-cate', // 产品小类top
    getEchartsInfo: '/api/security-manager/v1/uportal/access/top20', // 品牌、物料、文档、FAQ TOP20
    getBoardAccessOverviewOne: '/api/security-manager/v1/uportal/access/overview-one',
    getBoardAccessOverviewDept: '/api/security-manager/v1/uportal/access/overview-dept',
    getBoardAccessOverviewUser: '/api/security-manager/v1/uportal/access/overview-user',
    getBoardDeptAll: '/api/security-manager/v1/uportal/stat/statisticPageDeptAll', // 页面访问按组织统计合并查询
    getStatisticPageDeptByType: '/api/security-manager/v1/uportal/stat/statisticPageDeptByType', // 页面访问按组织统计单独查询
    getPageUser: '/api/security-manager/v1/uportal/stat/statisticPageUser', // 用户访问统计列表查询
    // 页面访问-产品分类
    queryProTotal: '/api/product-manager/v1/uportal/page-product/product-all',
    queryProSmall: '/api/product-manager/v1/uportal/page-small/page-all', // 产品小类
    getBoardAccessProductSingle: '/api/product-manager/v1/uportal/page-small/page-single',
    getStatisticPageCategory: '/api/security-manager/v1/uportal/stat/statisticPageCategory', // 页面访问按产品分类统计
    getStatisticPageCategoryTop: '/api/security-manager/v1/uportal/stat/statisticPageCategoryTop', // 页面访问按产品分类统计TOP详情
    getStatisticPageCategoryDetail: '/api/security-manager/v1/uportal/stat/statisticPageCategoryDetail', // 产品分类top详情
    // 看板-搜索
    getStatisticSearch: '/api/security-manager/v1/uportal/stat/statisticSearch',
    getStatisticSearchTop: '/api/security-manager/v1/uportal/stat/statisticSearchTop',
    // 看板-智能体
    getStatisticIntelligence: '/api/security-manager/v1/uportal/stat/statisticIntelligence',
    // 看板-业务过程
    getBusinessStat: '/api/security-manager/v1/uportal/business/stat',
    // 埋点接口
    setBuryingPoint: '/api/security-manager/v1/uportal/stat/searchBuryingPoint', // 看板搜索
    setIntelligenceBuryingPoint: '/api/security-manager/v1//uportal/stat/intelligenceBuryingPoint', // 看板智能体
    getstatisticSearchTop: '/api/security-manager/v1/uportal/stat/statisticSearchTop',
    getBuryingPoint: '/api/security-manager/v1/uportal/stat/buryingPoint-add', // 看板-页面访问
    getAddRecordDocDownLoad: '/api/security-manager/v1/uportal/stat/addRecordDocDown', // 看板-文档下载
    // 文档下载
    getStatisticFile: '/api/security-manager/v1/uportal/stat/statisticFile',
    getPreviewTop: '/api/security-manager/v1/uportal/stat/previewTop', // 预览top50
    getDownloadTop: '/api/security-manager/v1/uportal/stat/downloadTop', // 文档下载 top50
    getUserStatisticFile: '/api/security-manager/v1/uportal/stat/userStatisticFile', // 用户预览
    getStatisticFileTop: '/api/security-manager/v1/uportal/stat/statisticFileTop', // top 详情
    // 文档数量，变化统计
    getStatDocument: '/api/security-manager/v1/uportal/asset/statDocument', // 文档数量，变化统计
    getStatJuniorStatistics: '/api/security-manager/v1/uportal/asset/statJuniorStatistics', // 文档下级统计
    getAddRecord: '/api/security-manager/v1/uportal/asset/addRecord', // 文档埋点
    // 物料统计 /uportal/asset/statMaterial
    getStatMaterial: '/api/security-manager/v1//uportal/asset/statMaterial', // 物料数量和变化统计
    getStatMaterialJunior: '/api/security-manager/v1/uportal/asset/statMaterialJunior', // 物料下级统计
    getBuryingPointMaterials: '/api/security-manager/v1/uportal/asset/buryingPoint', // 物料更新埋点
    // faq统计
    getStatFaq: '/api/security-manager/v1/uportal/asset/statFaq', // faq统计
    // 功能评价
    getCurrentEval: '/api/security-manager/v1/uportal/evaluation/current-eval', // 获取用户评价信息
    doEvaluationEval: '/api/security-manager/v1/uportal/evaluation/do-eval', // 赞/踩
    // 分享消息
    shareToMessage: '/api/product-manager/v1/uportal/page-share/message',
    shareToEmail: '/api/product-manager/v1/uportal/page-share/email',
    userVerify: '/api/product-manager/v1/uportal/user-verify/verify',
};
HttpService.callback = function (data) {
    if (data.data.errorCode === 0 || data.data.code === 0) {
        try {
            return JSON.parse(data.data.data);
        } catch (e) {
            return null;
        }
    }
};

function handleUrlParam(url, data) {
    let queryArr = [];
    for (let key in data) {
        if (data[key] === 0) {
            queryArr.push(`${key}=0`);
        } else {
            queryArr.push(`${key}=${data[key] || ''}`);
        }
    }

    if (queryArr.length) {
        return `${url}?${queryArr.join('&')}`;
    } else {
        return url;
    }
}

// params, okCallback, failCallback
HttpService.request = function (type, option) {
    let method = option.method || 'get';
    let url = urlMap[type] || type;
    if (option.urlParam) {
        if (typeof option.urlParam === 'string') {
            url = `${url}/${option.urlParam}`;
        } else {
            url = handleUrlParam(url, option.urlParam);
        }
    }
    let data;

    if (method === 'get') {
        data = {
            params: option.data || {},
        };
    } else {
        if (method === 'delete') {
            data = {
                data: option.data,
            };
        } else {
            data = option.isOriginData ? option.data : JSON.stringify(option.data);
        }
    }

    let config = option.config || {};

    return axios[method](url, data, config).then(
        data => {
            let msg = handelErrCode(data.data); // 统一错误码处理
            if (!encryptFailMessage && msg) {
                encryptFailMessage = Message({
                    message: msg,
                    type: 'error',
                    duration: 5000,
                    offset: 100,
                    showClose: true,
                    onClose: () => {
                        encryptFailMessage = null;
                    },
                });
            }
            option.complete && option.complete(data.data);
            if (data.data.errorCode === 0 || data.data.code === 0) {
                try {
                    let d = JSON.parse(data.data.data);
                    option.success && option.success(d);
                } catch (e) {
                    option.success && option.success(data.data.data);
                }
            }
        },
        err => {
            let msg = handelErrCode(err.response?.data); // 统一错误码处理
            if (!encryptFailMessage && msg) {
                encryptFailMessage = Message({
                    message: msg,
                    type: 'error',
                    duration: 5000,
                    offset: 100,
                    showClose: true,
                    onClose: () => {
                        encryptFailMessage = null;
                    },
                });
            }
            option.error && option.error(err);
        }
    );
};

export default HttpService;
