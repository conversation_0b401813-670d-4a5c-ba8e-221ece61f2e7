/* Started by AICoder, pid:u750au30b5kb514141170af180b6361c71c55aca */
export const antiShake = function (fn, time) {
    // 创建一个标记用来存放定时器的返回值
    let timeout = null;
    return function () {
        let that = this;
        let args = arguments;
        // 每当用户触发input事件  把前一个 setTimeout 清楚掉
        if (time) {
            clearTimeout(timeout);
        }
        // 然后又创建一个新的 setTimeout, 这样就能保证输入字符后等待的间隔内 还有字符输入的话，就不会执行 setTimeout里面的内容
        timeout = setTimeout(() => {
            // 这里进行防抖的内容
            fn.apply(that, args);
        }, time);
    };
};
/* Ended by AICoder, pid:u750au30b5kb514141170af180b6361c71c55aca */