/**
 * 注意：修改此文件时，所有代码库中的同名文件需要一同修改，避免不一致
 */
import * as echarts from 'echarts';
import xss from 'xss';

// 修改 echarts 默认行为，解决 XSS 问题
// 不同版本的 echarts 需要兼容
function echartsXSSPolyfill() {
    // 代码注入
    function polyfill(echarts) {
        let version = (/^4/i.test(echarts.version) && 'v4') || (/^5/i.test(echarts.version) && 'v5') || '';
        let dom = document.createElement('div');
        let myChart = echarts.init(dom, null, {
            renderer: 'canvas',
            useDirtyRect: false,
        });

        // 配置 myChart，从而可以在实例中获取到要修改的构造函数
        // 如果要处理其他组件问题，也需要在此处配置，然后才能在实例中获取到
        let option = {
            tooltip: {
                trigger: 'axis',
            },
        };

        if (option && typeof option === 'object') {
            myChart.setOption(option);
        }

        // xss 配置，生成 myxss 实例，多次使用时性能更好
        const xssOptions = {
            // 自定义匹配到不在白名单上的属性时的处理方法
            onIgnoreTagAttr: (tag, name, value, isWhiteAttr) => {
                // 如果返回一个字符串，则当前属性值将被替换为该字符串
                // 如果不返回任何值，则使用默认的处理方法（删除该属性）
                // 默认配置基础上，允许以下标签属性
                if (['class', 'style'].includes(name)) {
                    return `${name}="${value}"`;
                }
            },
        };
        const myxss = new xss.FilterXSS(xssOptions);

        Object.keys(myChart._componentsMap).forEach(key => {
            // 通过实例获取到 tooltip 的构造函数，修改函数定义，解决 XSS 问题
            if (/tooltip/i.test(key)) {
                let tooltip = myChart._componentsMap[key];
                let TooltipContent = Object.getPrototypeOf(tooltip._tooltipContent);
                window.TooltipContent = TooltipContent;
                // 修改默认的 setContent 函数。注意：如果升级了依赖，需要确认此函数是否有变化
                let originalSetContent = TooltipContent.setContent;
                if (version === 'v4') {
                    TooltipContent.setContent = function (content) {
                        // eslint-disable-next-line no-param-reassign
                        content = myxss.process(content);
                        originalSetContent.bind(this)(content);
                    };
                } else if (version === 'v5') {
                    TooltipContent.setContent = function (content, markers, tooltipModel, borderColor, arrowPosition) {
                        // eslint-disable-next-line no-param-reassign
                        content = myxss.process(content);
                        originalSetContent.bind(this)(content, markers, tooltipModel, borderColor, arrowPosition);
                    };
                }
            }
            // 其它组件的 XSS 问题，可以类似处理
        });
        myChart.dispose();
        dom = null;
    }

    if (typeof echarts !== 'undefined') {
        polyfill(echarts);
    }
}

export default echartsXSSPolyfill;
