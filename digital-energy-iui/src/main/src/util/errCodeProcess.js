import i18n from '@/util/i18n.js';
const lang = localStorage.getItem('language-option');
import { LogIn } from '@/util/common.js';
function handelErrCode(data) {
    let msg = ''; // 弹出报错信息
    if (data && data.code) {
        if (data.code === 900) {
            LogIn();
        } if (data.code === -300392) {
            msg = i18n.global.t('rules.specialCharacters');
        } else if (data.code === -300391) {
            msg = i18n.global.t('rules.lengthExceeds');
        } else if (data.code === -300701) {
            msg = i18n.global.t('rules.zipBomb');
        } else if (data.code === -701) {
            let msgTip = '';
            let obj = {};
            try {
                obj = JSON.parse(data.message) || {};
            } catch (e) {
                console.log('-701');
            }
            if (lang.includes('en')) {
                msgTip = obj['en_US'] || '';
            } else if (lang.includes('zh')) {
                msgTip = obj['zh_CN'] || '';
            }
            msg = msgTip;
        } else if (data.code === 3329003 || data.code === 3329001) {
            msg = data.message;
        }
    }
    return msg || '';
}

export default handelErrCode;
