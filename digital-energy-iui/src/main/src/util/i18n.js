import { createI18n } from 'vue-i18n';

import commonZhLocale from './../lang/common-zh-CN.json';
import commonEnLocale from './../lang/common-en-US.json';

let lang = parent.localStorage['language-option'];
let result = (lang && lang.substr(1, lang.length - 2)) || navigator.language || 'zh-CN';
const i18n = new createI18n({
    legacy: false,
    locale: result,
    globalInjection: true,
    messages: {
        'zh-CN': {
            ...commonZhLocale,
        },
        'en-US': {
            ...commonEnLocale,
        },
    },
    silentTranslationWarn: true,
});

// i18n文件单独拉出来，方便其他js文件import进行国际化处理
export default i18n;
