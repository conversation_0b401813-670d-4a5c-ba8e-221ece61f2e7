/**
 * 下载公共函数
 * @param {*} param
 */
export default function exportHandler({
    url,
    params,
    fileNameHandler,
    successHandler,
    errorHandler,
    method = 'post',
    config = {}
}) {
    const { headers = {}, othersConfig } = config;
    const newConfig = {
        responseType: 'blob',
        ...othersConfig,
        headers: {
            'Content-Type': 'application/json',
            ...headers
        }
    };

    let newUrl = url;
    if (method === 'get') {
        const queryParams = new URLSearchParams(params).toString();
        newUrl = url + (url.includes('?') ? '&' : '?') + queryParams;
    }

    fetch(newUrl, {
        method,
        body: method === 'post' ? JSON.stringify(params) : null,
        ...newConfig
    })
        .then(async res => {
            if (res.status !== 200) {
                throw Error(res.statusText);
            }

            let type = '';
            let data = null;
            const headers = Object.fromEntries(res.headers.entries());
            if (headers['content-type'].includes('application/json')) {
                type = 'application/json';
                data = await res.json();
            } else {
                data = await res.blob();
            }

            return new Promise(resolve => {
                resolve({
                    type,
                    data,
                    headers
                });
            });
        })
        .then(res => {
            if (res.type !== 'application/json') {
                // 导出成功，返回数据流
                let blob = new Blob([res.data]);
                let url = window.URL.createObjectURL(blob);
                let link = document.createElement('a');
                let fileName = '';

                // fileName 解析规则
                if (typeof fileNameHandler === 'function') {
                    fileName = fileNameHandler();
                } else if (res.headers['content-disposition']) {
                    let contentDisposition = res.headers['content-disposition'];
                    fileName = contentDisposition.split('filename=')[1] || '';
                    fileName = decodeURIComponent(fileName.replace(/\+/g, '%20'));
                }
                // 解析文件名失败抛错
                if (!fileName) {
                    throw Error('Parse file name error.');
                }

                link.style.display = 'none';
                link.href = url;
                link.download = `${fileName}`;
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);
            }
            successHandler(res);
        })
        .catch(res => {
            if (typeof errorHandler === 'function') {
                errorHandler(res.message);
            }
        });
}
