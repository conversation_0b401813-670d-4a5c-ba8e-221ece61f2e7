/**
 * 初始化全局变量
 * 必须在 main.js 的第一行，避免编译优化导致执行顺序变化
 */
function initGlobal() {
    // csrftoken
    window.forgerydefense = (localStorage['csrftoken'] && localStorage['csrftoken'].replace(/[^a-zA-Z0-9]/g, '')) || '';
    // 语言
    // 下划线连接，如 zh_CN，不同场景下注意区分使用哪种连接方式的
    window.languageOption = (
        (localStorage['language-option'] && localStorage['language-option'].replace(/[^a-zA-Z\-_]/g, '')) ||
        ''
    ).replace('-', '_');
    // 短横线连接，如 zh-CN
    window.languageOptionDash = window.languageOption.replace('_', '-');

    // username
    window.username =
        (top.ZteFrameWork_conf && top.ZteFrameWork_conf.userName) ||
        (localStorage['username'] || '').replace(/\"/g, '') ||
        '';
}

initGlobal();
