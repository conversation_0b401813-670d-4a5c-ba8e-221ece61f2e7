import i18n from './i18n.js';
const { t } = i18n.global;

export const DEBOUNCE_TIME = 300;
export const QUERY_INPUT_MAXLEN = 50;
export const FETCH_SUCCESS_CODE = 0;
export const CHART_HANDLEICON =
    'M10.7,11.9H9.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z';
export const CHART_COLORS = {
    color: ['#7cd180', '#109ebf', '#4bd3f3', '#ffc850', '#0171ab', '#a8e2cf', '#CCA4E3', '#4ee5c8'],
    default: {
        title: '#303133',
        total: '#606266',
        label: '#909399',
        noData: '#a8abb2',
        background: 'rgb(48, 49, 51, 0.75)',
        border: 'transparent',
        color: '#fff',
        defaultColor: '#606266',
        line: '#dcdfe6',
        splitLine: '#ebeef5',
    },
    dark: {
        title: '#e5eaf3',
        total: '#cfd3dc',
        label: '#a3a6ad',
        noData: '#8d9095',
        background: 'rgb(255, 255, 255, 1)',
        border: 'transparent',
        color: '#606266',
        defaultColor: '#cfd3dc',
        line: '#4c4d4f',
        splitLine: '#363637',
    },
};
export const getLineNodata = (colorType, text, fontSize) => {
    const defaultFontSize = '24px';
    const defaultText = t('tipMessage.noData');
    return {
        type: 'text',
        left: 'center',
        top: 'middle',
        silent: true,
        invisible: false,
        style: {
            fill: CHART_COLORS[colorType].noData,
            fontWeight: '400',
            text: text || defaultText,
            fontFamily: 'Microsoft Yahei',
            fontSize: fontSize || defaultFontSize,
        },
    };
};
/**
 * 产品库tab项的label和name
 */
export const PRODUCTTABSINFO = {
    document: {
        label: t('product.title.document'),
        name: 'document',
    },
    FAQ: {
        label: 'FAQ',
        name: 'frequentlyAskedQuestions',
    },
    coreParameter: {
        label: t('product.title.coreParameter'),
        name: 'coreParameter',
    },
    material: {
        label: t('product.title.material'),
        name: 'material',
    },
    brand: {
        label: t('product.title.brand'),
        name: 'brand',
    },
    echoWall: {
        label: t('product.document.echoWall'),
        name: 'echoWall',
    },
};
/**
 * 物料属性
 */
export const MaterialAttribute = [
    // {
    //     // 名称
    //     id: 'name',
    //     label: t('product.title.name'),
    // },
    {
        // 品牌
        id: 'brand',
        label: t('product.title.brand'),
    },
    {
        // 采购模式
        id: 'purchaseMode',
        label: t('product.material.purchaseMode'),
    },
    {
        // 规格型号
        id: 'specificationModel',
        label: t('product.material.modelSpec'),
    },
    {
        // 推荐等级
        id: 'recommendedLevel',
        label: t('product.material.recommendedLevel'),
    },
    {
        // 失效日期
        id: 'expirationDate',
        label: t('product.material.expirationDate'),
    },
    {
        // 物料状态
        id: 'materialStatusName',
        label: t('product.material.materialStatus'),
    },
    {
        // 销售状态
        id: 'salesStatus',
        label: t('product.material.salesStatus'),
    },
    {
        // 销售代码
        id: 'salesCode',
        label: t('product.material.salesCode'),
    },
    {
        // 生产代码
        id: 'productionCode',
        label: t('product.material.productionCode'),
    },
    {
        // 产品分类
        id: 'productCategory',
        label: t('product.material.productClassification'),
    },
    {
        // 产品分组
        id: 'productGroup',
        label: t('product.material.productGroup'),
    },
    {
        // 交期
        id: 'deliveryDays',
        label: t('product.material.deliveryDate'),
    },
    {
        // 供应商
        id: 'supplier',
        label: t('product.material.supplier'),
    },
    {
        // 单位
        id: 'unit',
        label: t('product.material.unit'),
    },
    {
        // 成本
        id: 'cost',
        label: t('product.material.cost'),
    },
    {
        // 服务
        id: 'service',
        label: t('product.material.service'),
    },
    {
        // 描述
        id: 'description',
        label: t('product.document.description'),
    },
    // {
    //     // 保质期
    //     id: 'warrantyPeriod',
    //     label: t('product.material.warrantyPeriodMonth'),
    // },
];
export const OPERATION_MAP = {
    1: t('product.material.batchSubmitChange'),
    4: t('product.material.batchListing'),
    5: t('product.material.batchDeList'),
    delete: t('button.multidelete'),
};
// 产品库-物料操作状态类型
export const MaterialOperationStatus = {
    submitChange: 1, // 变更
    cancelChange: 3, // 取消变更
    rack: '4', // 上架
    remove: '5', // 下架
};
// 物料单位
export const MaterialUnitOptions = [
    {
        id: '套KIT',
        nameZh: '套',
        nameEn: 'KIT',
    },
    {
        id: '块PCS',
        nameZh: '块',
        nameEn: 'PCS',
    },
    {
        id: '个PCS',
        nameZh: '个',
        nameEn: 'PCS',
    },
    {
        id: '根PCS',
        nameZh: '根',
        nameEn: 'PCS',
    },
    {
        id: '台SET',
        nameZh: '台',
        nameEn: 'SET',
    },
    {
        id: '件PCS',
        nameZh: '件',
        nameEn: 'PCS',
    },
    {
        id: '米m',
        nameZh: '米',
        nameEn: 'm',
    },
    {
        id: '双PR',
        nameZh: '双',
        nameEn: 'PR',
    },
    {
        id: '组GR',
        nameZh: '组',
        nameEn: 'GR',
    },
    {
        id: '千米km',
        nameZh: '千米',
        nameEn: 'km',
    },
    {
        id: '平方米m2',
        nameZh: '平方米',
        nameEn: 'm2',
    },
    {
        id: '批BAT',
        nameZh: '批',
        nameEn: 'BAT',
    },
];
/**
 * 标书分解
 */
export const BidDocumentBreakdown = {
    bid: {
        label: t('project.title.engineeringQuantityList'),
        name: 'bid',
    },
    document: {
        label: t('menu.document'),
        name: 'document',
    },
    bulletinBoard: {
        label: t('product.title.bulletinBoard'),
        name: 'bulletinBoard',
    },
};
/**
 * 选型属性下拉选项
 */
export const SelectionAttributeOptions = [
    {
        id: '0',
        name: t('product.brand.preference'),
    },
    {
        id: '1',
        name: t('product.brand.other'),
    },
];
/**
 * 需求状态下拉选项
 */
export const DemandTypeOptions = [
    {
        id: 1,
        name: t('product.demand.analyzing'),
    },
    {
        id: 2,
        name: t('product.demand.selecting'),
    },
    {
        id: 3,
        name: t('product.demand.completed'),
    },
];
// 物料状态和表格操作按钮映射关系
export const ButtonMaterialStatusRelation = {
    1: ['edit', 'costChange', 'rack', 'copy', 'delete'],
    2: ['withdrawRack', 'copy'],
    3: ['change', 'remove', 'PDMSynchronization', 'copy', 'costChange'],
    4: ['cancelChange', 'submitChange', 'edit', 'costChange', 'copy'],
    5: ['withdrawChange', 'copy'],
    6: ['withdrawRemove', 'copy'],
    7: ['delete', 'copy', 'edit', 'costChange', 'rack'],
    8: ['costChange', 'copy'],
};
// 商机管理-任务类型
export const ApprovalTypes = {
    1: t('workbench.fields.materialOnboard'),
    2: t('workbench.fields.materialChange'),
    3: t('workbench.fields.materialOffboard'),
    5: t('project.button.bidClarification'),
    6: t('project.button.materialSelection'),
    7: t('product.details.documentation'),
    8: t('product.demand.biddingApply'),
};
// 工作台-任务类型下拉选项
export const ApprovalTypesSelections = [
    {
        id: 0,
        label: t('workbench.fields.allType'),
    },
    {
        id: 1,
        label: t('workbench.fields.materialOnboard'),
    },
    {
        id: 2,
        label: t('workbench.fields.materialChange'),
    },
    {
        id: 3,
        label: t('workbench.fields.materialOffboard'),
    },
    // {
    //     id: 4,
    //     label: t('workbench.fields.marketBidSupport')
    // },
    {
        id: 5,
        label: t('project.button.bidClarification'),
    },
    {
        id: 6,
        label: t('project.button.materialSelection'),
    },
    {
        id: 7,
        label: t('product.details.documentation'),
    },
    {
        id: 8,
        label: t('product.demand.biddingApply'),
    },
    {
        id: 9,
        label: t('workbench.fields.productCapitalDeliveryTask'),
    },
];
// 工作台-处理结果下拉选项
export const ApprovalResultOptions = [
    {
        id: 0,
        label: t('workbench.fields.disagree'),
    },
    {
        id: 1,
        label: t('workbench.fields.agree'),
    },
    {
        id: 2,
        label: t('project.title.reback'),
    },
    {
        id: 3,
        label: t('button.transfer'),
    },
    {
        id: 4,
        label: t('project.button.completed'),
    },
    {
        id: 5,
        label: t('project.button.acceptancePassed'),
    },
    {
        id: 6,
        label: t('project.button.acceptanceFailed'),
    },
    {
        id: 10,
        label: t('button.receive'),
    },
    {
        id: 11,
        label: t('button.transfer'),
    },
    {
        id: 12,
        label: t('project.button.completed'),
    },
    {
        id: 13,
        label: t('project.button.acceptancePassed'),
    },
    {
        id: 14,
        label: t('project.button.acceptanceFailed'),
    },
    {
        id: 15,
        label: t('project.button.cancelled'),
    },
    {
        id: 16,
        label: t('button.reject'),
    },
    {
        id: 20,
        label: t('project.button.applyPassed'),
    },
    {
        id: 21,
        label: t('project.title.hasReady'),
    },
    {
        id: 22,
        label: t('project.title.applySubmit'),
    },
];
// 工作台-处理状态下拉选项
export const ApprovalStatusOptions = [
    {
        id: 0,
        label: t('workbench.fields.unApproved'),
    },
    {
        id: 1,
        label: t('workbench.fields.underApproval'),
    },
    {
        id: 2,
        label: t('workbench.fields.approved'),
    },
    {
        id: 5,
        label: t('workbench.fields.pendingProcess'),
    },
    {
        id: 6,
        label: t('workbench.fields.processing'),
    },
    {
        id: 7,
        label: t('workbench.fields.pendingVerified'),
    },
    {
        id: 8,
        label: t('workbench.fields.verified'),
    },
    {
        id: 11,
        label: t('project.title.materialReady'),
    },
    {
        id: 12,
        label: t('workbench.fields.unApplication'),
    },
    {
        id: 13,
        label: t('project.title.submitApply'),
    },
    {
        id: 14,
        label: t('workbench.fields.applicationPassed'),
    },
];
export const LectotypeType = {
    bidding: '1',
    specified: '2',
};
export const FilePreviewType = [ // 文件预览支持的类型
    'xlsx',
    'xls',
    'xlsm',
    'csv',
    'et',
    'docx',
    'doc',
    'wps',
    'docm',
    'pdf',
    'ppt',
    'pptx',
    'pptm',
    'pot',
    'potx',
    'potm',
    'pps',
    'ppsx',
    'dps',
    'txt',
    'json',
    'jpg',
    'jpeg',
    'gif',
    'png',
    'bmp',
    'tif',
    'tiff',
    'svg'
];