{"name": "digital-energy-iui", "version": "16.24.40", "private": true, "scripts": {"postinstall": "patch-package", "dev": "vite --force", "build": "vite build", "build:dev": "vite build --mode developmentbuild", "lint:quiet": "eslint --quiet", "stylelint:fix": "stylelint --fix", "stylelint:quiet": "stylelint --quiet", "prepare": "cd ../../.. && husky install user-iui/src/main/.husky", "lint-staged": "lint-staged"}, "lint-staged": {"**/*.{vue,js}": "npm run lint:quiet", "**/*.{vue,css,sass,scss}": "npm run stylelint:quiet"}, "dependencies": {"@element-plus/icons-vue": "2.1.0", "@wangeditor/editor-for-vue": "5.1.12", "axios": "1.7.8", "echarts": "5.5.1", "element-plus": "2.5.6", "lodash": "4.17.21", "mitt": "3.0.1", "moment": "2.29.4", "pinia": "2.1.7", "vue": "3.3.4", "vue-dompurify-html": "4.1.4", "vue-i18n": "9.2.2", "vue-router": "4.2.4", "xss": "1.0.14", "zte-user-selector-plus": "^1.0.0"}, "devDependencies": {"@babel/core": "7.23.2", "@babel/eslint-parser": "7.19.1", "@babel/preset-env": "7.23.2", "@stylistic/stylelint-plugin": "2.1.0", "@vitejs/plugin-vue": "5.0.4", "@vitejs/plugin-vue-jsx": "3.1.0", "autoprefixer": "10.4.13", "chai": "4.3.7", "crypto-js": "4.2.0", "eslint": "8.46.0", "eslint-config-prettier": "7.2.0", "eslint-plugin-prettier": "4.2.1", "eslint-plugin-vue": "9.17.0", "husky": "8.0.0", "lint-staged": "15.2.2", "patch-package": "8.0.0", "postcss": "8.4.31", "prettier": "3.2.5", "sass": "1.66.1", "stylelint": "16.2.1", "stylelint-config-recommended-vue": "1.5.0", "stylelint-config-standard-scss": "13.0.0", "vite": "5.1.3"}, "overrides": {"dompurify": "3.2.4"}}