/* eslint-disable no-undef */
/* Started by AICoder, pid:e2a4dedb6fsd56b14ef009575106d54739447186 */
import { shallowMount, createLocalVue, nextTick } from '@vue/test-utils';
import HTTP from '@/util/httpService.js';
import { ElMessage } from 'element-plus';
import { useI18n } from 'vue-i18n';

jest.mock('@/util/httpService.js', () => ({
    request: jest.fn()
}));

const localVue = createLocalVue();
localVue.use(useI18n);

describe('Frequently Asked Questions Component', () => {
    let wrapper;
    let httpMock;

    const mockData = {
        code: 0,
        data: {
            list: [
                { id: 1, question: 'Question 1', answer: 'Answer 1', updateBy: 'User 1', updateTime: '2023-01-01' },
                { id: 2, question: 'Question 2', answer: 'Answer 2', updateBy: 'User 2', updateTime: '2023-01-02' }
            ],
            total: 2
        }
    };

    const mockErrorData = {
        code: 1,
        message: 'Error message'
    };

    beforeEach(() => {
        httpMock = HTTP.request.mockImplementation((_, options) => {
            return new Promise(resolve => {
                if (options.url === 'frequentlyAskedQuestionsList') {
                    resolve(mockData);
                } else if (options.url === 'frequentlyAskedQuestionsDelete') {
                    resolve({ code: 0 });
                }
            });
        });

        wrapper = shallowMount({
            template: `<div><template v-for="item in tableData">{{ item.question }}</template></div>`,
            setup() {
                return {
                    tableData: ref([]),
                    getTableData: () => {
                        HTTP.request('frequentlyAskedQuestionsList', {
                            method: 'post',
                            data: {},
                            complete: data => {
                                if (data.code === 0) {
                                    tableData.value = data.data.list || [];
                                }
                            }
                        });
                    },
                    deleteConfirm: () => {
                        HTTP.request('frequentlyAskedQuestionsDelete', {
                            method: 'post',
                            urlParam: 1,
                            complete: data => {
                                if (data.code === 0) {
                                    ElMessage({
                                        type: 'success',
                                        message: 'Success'
                                    });
                                } else {
                                    ElMessage({
                                        type: 'error',
                                        message: 'Error'
                                    });
                                }
                            }
                        });
                    }
                };
            }
        }, {
            localVue,
            attachTo: document.body
        });

        wrapper.vm.getTableData();
        return nextTick();
    });

    afterEach(() => {
        HTTP.request.mockReset();
        wrapper.destroy();
    });

    it('fetches and displays data correctly', async () => {
        expect(HTTP.request).toHaveBeenCalledWith('frequentlyAskedQuestionsList', expect.any(Object));
        expect(wrapper.vm.tableData.length).toBe(2);
        expect(wrapper.vm.tableData[0].question).toBe('Question 1');
        expect(wrapper.vm.tableData[1].question).toBe('Question 2');
    });

    it('handles error response correctly', async () => {
        httpMock.mockReturnValueOnce(Promise.resolve(mockErrorData));
        await wrapper.vm.getTableData();
        expect(wrapper.vm.tableData.length).toBe(0);
    });

    it('displays success message on delete', async () => {
        await wrapper.vm.deleteConfirm();
        expect(ElMessage).toHaveBeenCalledWith({
            type: 'success',
            message: 'Success'
        });
    });

    it('displays error message on delete failure', async () => {
        httpMock.mockReturnValueOnce(Promise.resolve({ code: 1 }));
        await wrapper.vm.deleteConfirm();
        expect(ElMessage).toHaveBeenCalledWith({
            type: 'error',
            message: 'Error'
        });
    });

    it('sorts data correctly', async () => {
        wrapper.vm.sortChange({ prop: 'updateTime', order: 'descending' });
        expect(wrapper.vm.tableData[0].updateTime).toBe('2023-01-02');
        wrapper.vm.sortChange({ prop: 'updateTime', order: 'ascending' });
        expect(wrapper.vm.tableData[0].updateTime).toBe('2023-01-01');
    });

    it('handles pagination changes', async () => {
        wrapper.vm.handleSizeChange(20);
        expect(wrapper.vm.pageInfo.pageSize).toBe(20);
        wrapper.vm.handleCurrentChange(2);
        expect(wrapper.vm.pageInfo.pageNo).toBe(2);
    });

    it('searches data correctly', async () => {
        wrapper.vm.searchText = 'Question 1';
        await wrapper.vm.getTableData();
        expect(wrapper.vm.tableData.length).toBe(2); // Assuming search does not filter in this mock
    });
});
/* Ended by AICoder, pid:e2a4dedb6fsd56b14ef009575106d54739447186 */