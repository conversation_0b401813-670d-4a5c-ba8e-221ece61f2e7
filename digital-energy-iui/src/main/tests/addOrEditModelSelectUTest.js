/* Started by AICoder, pid:67f49t2d88cf222146420a0142d6d02247927139 */
// DrawerComponent.spec.js
import { shallowMount, createLocalVue } from '@vue/test-utils';
import DrawerComponent from '@/path-to-your-component/DrawerComponent.vue'; // Adjust the path as needed
import { useI18n } from 'vue-i18n';
import HTTP from '@/util/httpService.js';
import ProductDemandList from '@/path-to-your-component/ProductDemandList.vue'; // Adjust the path as needed
import { showErrMsg, openNewWindow, showSuccessMsg, checkPermission } from '@/util/common.js';

jest.mock('@/util/httpService.js');
jest.mock('@/util/common.js');

const localVue = createLocalVue();
localVue.use(useI18n);

describe('DrawerComponent.vue', () => {
    let wrapper;

    const mockT = (key) => key; // Mocking the translation function

    beforeEach(() => {
        wrapper = shallowMount(DrawerComponent, {
            localVue,
            propsData: {
                visible: true,
                drawerWidth: '1050px',
                drawerType: 'add',
                rowData: { id: '' },
                projectId: '123',
                batchEditList: []
            },
            mocks: {
                t: mockT
            }
        });
    });

    afterEach(() => {
        wrapper.unmount();
    });

    it('renders correctly', () => {
        expect(wrapper.exists()).toBe(true);
    });

    it('handles add button click', async () => {
        wrapper.setProps({ drawerType: 'add' });
        await wrapper.vm.$nextTick();
        expect(wrapper.vm.formState.name).toBe('');
    });

    it('handles edit button click', async () => {
        wrapper.setProps({ drawerType: 'edit', rowData: { id: 1, name: 'Test' } });
        await wrapper.vm.$nextTick();
        expect(wrapper.vm.formState.name).toBe('Test');
    });

    it('handles batch edit button click', async () => {
        wrapper.setProps({ drawerType: 'batchEdit' });
        await wrapper.vm.$nextTick();
        expect(wrapper.vm.isBatchEdit).toBe(false);
    });

    it('submits form successfully', async () => {
        wrapper.vm.formState.name = 'Test';
        wrapper.vm.formState.supply = '1';
        wrapper.vm.formState.productSubcategory = '1';
        HTTP.request.mockResolvedValue({ code: 0 });
        await wrapper.vm.submitForm(wrapper.vm.formStateRef);
        expect(showSuccessMsg).toHaveBeenCalledWith('productLibrary.tipMessage.addSuccess');
    });

    it('handles form validation error', async () => {
        wrapper.vm.formState.name = '';
        wrapper.vm.formState.supply = '1';
        wrapper.vm.formState.productSubcategory = '1';
        await wrapper.vm.submitForm(wrapper.vm.formStateRef);
        expect(showSuccessMsg).not.toHaveBeenCalled();
    });

    it('handles API error on submit', async () => {
        wrapper.vm.formState.name = 'Test';
        wrapper.vm.formState.supply = '1';
        wrapper.vm.formState.productSubcategory = '1';
        HTTP.request.mockRejectedValue({ message: 'API Error' });
        await wrapper.vm.submitForm(wrapper.vm.formStateRef);
        expect(showErrMsg).toHaveBeenCalledWith({ message: 'API Error' });
    });

    it('resets form data', async () => {
        wrapper.vm.formState.name = 'Test';
        wrapper.vm.resetForm(wrapper.vm.formStateRef);
        expect(wrapper.vm.formState.name).toBe('');
    });

    it('closes drawer on reset', async () => {
        wrapper.vm.resetForm(wrapper.vm.formStateRef);
        expect(wrapper.emitted().close).toBeTruthy();
    });

    it('fetches all enumerations on mount', async () => {
        HTTP.request.mockResolvedValue({ code: 0, data: [{ id: 1, name: 'Option 1' }] });
        await wrapper.vm.$nextTick();
        expect(HTTP.request).toHaveBeenCalledWith('querySupplyBidDecomposition', {
            method: 'get',
            complete: expect.any(Function),
            error: expect.any(Function)
        });
    });

    it('fetches product list on mount', async () => {
        HTTP.request.mockResolvedValue({ code: 0, data: [{ id: 1, pathName: 'Product 1' }] });
        await wrapper.vm.$nextTick();
        expect(HTTP.request).toHaveBeenCalledWith('getAllProductCategory', {
            method: 'get',
            complete: expect.any(Function),
            error: expect.any(Function)
        });
    });

    it('handles API error when fetching enumerations', async () => {
        HTTP.request.mockRejectedValue({ message: 'API Error' });
        await wrapper.vm.$nextTick();
        expect(showErrMsg).toHaveBeenCalledWith({ message: 'API Error' });
    });

    it('handles API error when fetching product list', async () => {
        HTTP.request.mockRejectedValue({ message: 'API Error' });
        await wrapper.vm.$nextTick();
        expect(showErrMsg).toHaveBeenCalledWith({ message: 'API Error' });
    });

    it('validates quantity correctly', async () => {
        const callback = jest.fn();
        await wrapper.vm.validateQuantity({}, '10.50', callback);
        expect(callback).toHaveBeenCalled();
    });

    it('validates negative quantity', async () => {
        const callback = jest.fn();
        await wrapper.vm.validateQuantity({}, '-1', callback);
        expect(callback).toHaveBeenCalledWith(expect.any(Error));
    });

    it('validates invalid quantity format', async () => {
        const callback = jest.fn();
        await wrapper.vm.validateQuantity({}, 'abc', callback);
        expect(callback).toHaveBeenCalledWith(expect.any(Error));
    });

    it('validates empty quantity', async () => {
        const callback = jest.fn();
        await wrapper.vm.validateQuantity({}, '', callback);
        expect(callback).toHaveBeenCalled();
    });

    it('handles drawer close event', async () => {
        wrapper.vm.handleClose();
        expect(wrapper.emitted().close).toBeTruthy();
    });

    it('sets correct rules for product subcategory', async () => {
        wrapper.vm.formState.supply = '3';
        wrapper.vm.formStateRef.validateField('productSubcategory');
        expect(wrapper.vm.rules.productSubcategory).toBeDefined();
    });

    it('clears product subcategory when supply is not 3', async () => {
        wrapper.vm.formState.supply = '1';
        wrapper.vm.formStateRef.validateField('productSubcategory');
        expect(wrapper.vm.formState.productSubcategory).toBe('');
    });

    it('emits update:visible event on drawerVisible change', async () => {
        wrapper.setProps({ visible: false });
        await wrapper.vm.$nextTick();
        expect(wrapper.emitted()['update:visible']).toBeTruthy();
    });

    it('watches visible prop and resets form', async () => {
        wrapper.setProps({ visible: false });
        await wrapper.vm.$nextTick();
        expect(wrapper.vm.formState.name).toBe('');
    });

    it('continues adding after successful submission', async () => {
        wrapper.setProps({ drawerType: 'add' });
        wrapper.vm.formState.name = 'Test';
        wrapper.vm.formState.supply = '1';
        wrapper.vm.formState.productSubcategory = '1';
        HTTP.request.mockResolvedValue({ code: 0 });
        await wrapper.vm.submitForm(wrapper.vm.formStateRef, true);
        expect(wrapper.emitted().close).toBeFalsy();
    });

    it('closes drawer after successful submission', async () => {
        wrapper.setProps({ drawerType: 'add' });
        wrapper.vm.formState.name = 'Test';
        wrapper.vm.formState.supply = '1';
        wrapper.vm.formState.productSubcategory = '1';
        HTTP.request.mockResolvedValue({ code: 0 });
        await wrapper.vm.submitForm(wrapper.vm.formStateRef);
        expect(wrapper.emitted().close).toBeTruthy();
    });

    it('handles batch edit submission', async () => {
        wrapper.setProps({ drawerType: 'batchEdit', batchEditList: [{ id: 1 }] });
        wrapper.vm.formState.supply = '1';
        wrapper.vm.formState.productSubcategory = '1';
        HTTP.request.mockResolvedValue({ code: 0 });
        await wrapper.vm.submitForm(wrapper.vm.formStateRef);
        expect(showSuccessMsg).toHaveBeenCalledWith('productLibrary.tipMessage.updatedSuccess');
    });

    it('handles edit submission', async () => {
        wrapper.setProps({ drawerType: 'edit', rowData: { id: 1 } });
        wrapper.vm.formState.name = 'Test';
        wrapper.vm.formState.supply = '1';
        wrapper.vm.formState.productSubcategory = '1';
        HTTP.request.mockResolvedValue({ code: 0 });
        await wrapper.vm.submitForm(wrapper.vm.formStateRef);
        expect(showSuccessMsg).toHaveBeenCalledWith('productLibrary.tipMessage.updatedSuccess');
    });
});
/* Ended by AICoder, pid:67f49t2d88cf222146420a0142d6d02247927139 */

/* Started by AICoder, pid:g7f2245e35m8e5014c2a098cc93d2c211f490453 */
// ProductDemandList.spec.js


describe('ProductDemandList.vue', () => {
    let wrapper;

    const mockT = (key) => key; // Mocking the translation function

    beforeEach(() => {
        wrapper = shallowMount(ProductDemandList, {
            localVue,
            propsData: {
                groupId: '123',
                productCategoryId: '456',
                pathName: ['Path1', 'Path2'],
                isLeafNode: true,
                height: 500,
            },
            mocks: {
                t: mockT,
                $t: mockT,
                rights: { productPermissions: {} }
            }
        });
    });

    afterEach(() => {
        wrapper.unmount();
    });

    it('renders correctly', () => {
        expect(wrapper.exists()).toBe(true);
    });

    it('handles detail button click', async () => {
        wrapper.vm.handleDetail({ id: 1 });
        expect(wrapper.emitted().toOtherPage).toBeTruthy();
    });

    it('handles convert button click', async () => {
        wrapper.vm.handleConvert({ id: 1 });
        expect(wrapper.emitted().toConvert).toBeTruthy();
    });

    it('fetches table data on mount', async () => {
        HTTP.request.mockResolvedValue({ code: 0, data: { list: [], total: 0 } });
        await wrapper.vm.$nextTick();
        expect(HTTP.request).toHaveBeenCalledWith('queryPageDemand', {
            method: 'post',
            data: { ...wrapper.vm.queryParam, direction: 'desc', orderField: 'create_time', productCategoryId: '456', page: 1, size: 10 },
            complete: expect.any(Function),
            error: expect.any(Function)
        });
    });

    it('handles pagination changes', async () => {
        wrapper.vm.pageInfo.pageSize = 20;
        wrapper.vm.handleSizeChange(20);
        expect(wrapper.vm.pageInfo.pageSize).toBe(20);

        wrapper.vm.pageInfo.pageNum = 2;
        wrapper.vm.handleCurrentChange(2);
        expect(wrapper.vm.pageInfo.pageNum).toBe(2);
    });

    it('updates query parameter', async () => {
        wrapper.vm.updateQueryParameter({ dataPicker: ['2023-01-01', '2023-01-31'] });
        expect(wrapper.vm.queryParam.expTimeCompletStart).toBe('2023-01-01');
        expect(wrapper.vm.queryParam.expTimeCompletEnd).toBe('2023-01-31');
    });

    it('sets query item width', async () => {
        wrapper.vm.setQueryItemWidth();
        expect(wrapper.vm.queryItemWidth.value).toBe(240);
    });

    it('watches productCategoryId change', async () => {
        wrapper.setProps({ productCategoryId: '789' });
        await wrapper.vm.$nextTick();
        expect(wrapper.vm.pageInfo.pageNum).toBe(1);
    });

    it('shows error message on API failure', async () => {
        HTTP.request.mockRejectedValue({ message: 'API Error' });
        await wrapper.vm.getTableData();
        expect(showErrMsg).toHaveBeenCalledWith({ message: 'API Error' });
    });

    it('shows success message on API success', async () => {
        HTTP.request.mockResolvedValue({ code: 0 });
        await wrapper.vm.getTableData();
        expect(wrapper.vm.tableLoading.value).toBe(false);
    });

    it('emits toOtherPage event', async () => {
        wrapper.vm.handleDetail({ id: 1 });
        expect(wrapper.emitted().toOtherPage).toBeTruthy();
    });

    it('emits toConvert event', async () => {
        wrapper.vm.handleConvert({ id: 1 });
        expect(wrapper.emitted().toConvert).toBeTruthy();
    });

    it('calculates text size correctly', async () => {
        const item = { id: 'name', name: 'Test Name' };
        const textSize = wrapper.vm.textSize(item);
        expect(textSize).toBeGreaterThan(0);
    });

    it('checks permission correctly', async () => {
        const hasPermission = wrapper.vm.checkPermission({}, '123', 'test.permission');
        expect(hasPermission).toBe(false);
    });

    it('opens new window correctly', async () => {
        const url = 'http://example.com';
        openNewWindow(url);
        expect(window.open).toHaveBeenCalledWith(url, '_blank');
    });

    it('shows error message when no productCategoryId', async () => {
        wrapper.setProps({ productCategoryId: '' });
        await wrapper.vm.getTableData();
        expect(HTTP.request).not.toHaveBeenCalled();
    });

    it('handles empty table data', async () => {
        HTTP.request.mockResolvedValue({ code: 0, data: { list: [], total: 0 } });
        await wrapper.vm.getTableData();
        expect(wrapper.vm.tableData.value.length).toBe(0);
    });

    it('shows error message on invalid API response', async () => {
        HTTP.request.mockResolvedValue({ code: 1, message: 'Invalid Response' });
        await wrapper.vm.getTableData();
        expect(showErrMsg).toHaveBeenCalledWith({ code: 1, message: 'Invalid Response' });
    });

    it('shows success message on valid API response', async () => {
        HTTP.request.mockResolvedValue({ code: 0, data: { list: [{ id: 1 }], total: 1 } });
        await wrapper.vm.getTableData();
        expect(showSuccessMsg).not.toHaveBeenCalled();
    });

    it('emits query event', async () => {
        wrapper.vm.query({ dataPicker: ['2023-01-01', '2023-01-31'] });
        expect(wrapper.vm.queryParam.expTimeCompletStart).toBe('2023-01-01');
        expect(wrapper.vm.queryParam.expTimeCompletEnd).toBe('2023-01-31');
    });

    it('reloads list correctly', async () => {
        wrapper.vm.reloadList();
        expect(wrapper.vm.pageInfo.pageNum).toBe(1);
    });
});
/* Ended by AICoder, pid:g7f2245e35m8e5014c2a098cc93d2c211f490453 */