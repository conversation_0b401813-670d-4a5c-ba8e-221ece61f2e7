module.exports = {
    root: true,
    env: {
        browser: true,
        node: true,
        es2020: true,
    },
    globals: {
        $: true,
    },
    extends: ['eslint:recommended', 'plugin:vue/vue3-recommended', 'prettier'],
    rules: {
        'no-console': 'warn',
        'no-debugger': 'error',
        'no-floating-decimal': 'error',
        // 禁止八进制自变量
        'no-octal': 'error',
        // 要求使用 isNaN() 检查 NaN
        'use-isnan': 'error',
        // 禁止对 String，Number 和 Boolean 使用 new 操作符
        'no-new-wrappers': 'error',
        // 禁止 Symbol和new 一起使用
        'no-new-symbol': 'error',
        // 禁止多次声明同一变量
        'no-redeclare': [
            'error',
            {
                builtinGlobals: true,
            },
        ],
        // 禁止自我赋值
        'no-self-assign': [
            'error',
            {
                props: true,
            },
        ],
        // 禁止变量连续赋值
        'no-multi-assign': 'error',
        //  禁止删除变量
        'no-delete-var': 'error',
        // 禁止出现未使用过的变量
        'no-unused-vars': [
            'warn',
            {
                vars: 'all',
                args: 'after-used',
                ignoreRestSiblings: false,
            },
        ],
        // 禁止修改类声明的变量
        'no-class-assign': 'error',
        // 禁止修改 const 声明的变量
        'no-const-assign': 'error',
        // 禁止将标识符定义为受限的名字
        'no-shadow-restricted-names': 'error',
        // 禁用未声明的变量，除非它们在 /*global */ 注释中被提到
        'no-undef': [
            'error',
            {
                typeof: true,
            },
        ],
        // 要求使用 let 或 const 而不是 var
        'no-var': 'error',
        // 禁止对原生对象或只读的全局对象进行赋值
        'no-global-assign': 'error',
        // 禁用使用Object 构造函数来构建对象
        'no-new-object': 'error',
        // 禁止对象字面量中出现重复的 key
        'no-dupe-keys': 'error',
        // 禁止对关系运算符的左操作数使用否定操作符
        'no-unsafe-negation': 'error',
        // 仅在非标识符的对象属性名称上使用单引号
        'quote-props': ['error', 'as-needed'],
        // 禁用稀疏数组
        'no-sparse-arrays': 'error',
        // 禁用 Array 构造函数
        'no-array-constructor': 'error',
        // 禁止使用空解构模式
        'no-empty-pattern': 'error',
        // 禁用 eval()
        'no-eval': [
            'error',
            {
                allowIndirect: true,
            },
        ],
        // 禁止使用多行字符串
        'no-multi-str': 'error',
        // 禁止隐式的 eval() 方法
        'no-implied-eval': 'error',
        // 禁用不必要的转义字符
        'no-useless-escape': 'warn',
        // 不允许在字符类语法中出现由多个代码点组成的字符
        'no-misleading-character-class': 'error',
        // 禁止在正则表达式中使用控制字符
        'no-control-regex': 'warn',
        // 禁止在正则表达式中使用空字符集
        'no-empty-character-class': 'error',
        // 字符串使用单引号，推荐使用反引号创建多行字符串
        quotes: [
            'error',
            'single',
            {
                avoidEscape: false,
                allowTemplateLiterals: true,
            },
        ],
        // 禁止对 function 的参数进行重新赋值
        'no-param-reassign': 'warn',
        // 禁止在 return 语句中使用赋值语句
        'no-return-assign': 'error',
        // 禁止对 function 声明重新赋值
        'no-func-assign': 'error',
        // 禁止 function 定义中出现重名参数
        'no-dupe-args': 'error',
        // 禁止把全局对象作为函数调用
        'no-obj-calls': 'error',
        // 禁止从setter返回值
        'no-setter-return': 'error',
        // 强制 getter 函数中出现 return 语句
        'getter-return': [
            'error',
            {
                allowImplicit: true,
            },
        ],
        // 禁止在 return、throw、continue 和 break 语句之后出现不可达代码
        'no-unreachable': 'error',
        // 在async function禁用不必要的 return await
        'no-return-await': 'error',
        // 要求 generator 函数内有 yield
        'require-yield': 'error',
        // 禁止使用async函数作为 Promise executor
        'no-async-promise-executor': 'error',
        // 强制隐式返回的箭头函数体的位置
        'implicit-arrow-linebreak': ['error', 'beside'],
        // 禁止在可能与比较操作符相混淆的地方使用箭头函数
        'no-confusing-arrow': [
            'error',
            {
                allowParens: true,
            },
        ],
        // 要求构造函数首字母大写
        'new-cap': 'warn',
        // 要求调用无参构造函数时带括号
        'new-parens': 'error',
        // 禁止使用 new 以避免产生副作用
        'no-new': 'error',
        // 禁止对 Function 对象使用 new 操作符
        'no-new-func': 'error',
        // 禁止 RegExp 构造函数中存在无效的正则表达式字符串
        'no-invalid-regexp': 'error',
        // 要求在构造函数中有 super() 的调用
        'constructor-super': 'error',
        // 禁止在构造函数中，在调用 super() 之前使用 this 或 super
        'no-this-before-super': 'error',
        // 禁止类成员中出现重复的名称
        'no-dupe-class-members': 'error',
        // 禁止向import绑定赋值
        'no-import-assign': 'error',
        // 对象与属性不在同一行时，要求点号与属性在同一行
        'dot-location': ['error', 'property'],
        // 禁止在对象中使用不必要的计算属性
        'no-useless-computed-key': 'error',
        // 要求使用 === 和 !== 代替 == 和 ！=
        eqeqeq: 'warn',
        // 禁止使用 == 或 ！= 与 null 进行比较
        'no-eq-null': 'warn',
        // 禁止变量与其自身比较
        'no-self-compare': 'error',
        // 禁止可以在有更简单的可替代的表达式时使用三元操作符
        'no-unneeded-ternary': [
            'error',
            {
                defaultAssignment: false,
            },
        ],
        // 禁止与 -0 进行比较
        'no-compare-neg-zero': 'error',
        // 强制 typeof 表达式与有效的字符串进行比较
        'valid-typeof': 'error',
        // 块区域前后要求遵循大括号约定
        curly: 'error',
        // 禁用标签语句
        'no-labels': 'error',
        // 禁用不必要的嵌套块
        'no-lone-blocks': 'error',
        // 禁用 with 语句
        'no-with': 'error',
        // 将大括号放在控制语句或声明语句同一行的位置
        'brace-style': 'error',
        // 禁止在嵌套的块中出现 function 声明
        'no-inner-declarations': 'error',
        // 禁止在 finally 语句块中出现控制流语句
        'no-unsafe-finally': 'error',
        // 禁止在if-else-if链中出现重复条件
        'no-dupe-else-if': 'error',
        // switch语句中，禁止case或default子句中不出现词法声明
        'no-case-declarations': 'error',
        // 禁止出现重复的 case 标签
        'no-duplicate-case': 'error',
        // 禁止对 catch 子句的参数重新赋值
        'no-ex-assign': 'error',
        // switch语句中，禁止 case 语句落空
        'no-fallthrough': 'error',
        // 禁止条件表达式中出现赋值操作符
        'no-cond-assign': 'error',
        // 禁止在条件中使用常量表达式
        'no-constant-condition': [
            'error',
            {
                checkLoops: false,
            },
        ],
        // 如果代码块中有语句与开/闭括号在同一行，开括号后和闭括号前必须有空格
        'block-spacing': 'error',
        // 强制在逗号后使用空格
        'comma-spacing': 'error',
        // 禁止在计算的属性的方括号中使用空格
        'computed-property-spacing': 'error',
        // 禁止在函数标识符和其调用之间有空格
        'func-call-spacing': 'error',
        // 强制在对象字面量的属性中键和冒号之间不使用空格，在冒号和值之间使用空格
        'key-spacing': 'error',
        // 不允许使用 tabs，包括在注释中
        'no-tabs': 'error',
        // 禁止空格和 tab 的混合缩进
        'no-mixed-spaces-and-tabs': 'error',
        // 禁止属性前有空白
        'no-whitespace-before-property': 'error',
        // 需要在对象的大括号内添加空格
        'object-curly-spacing': ['error', 'always'],
        // 强制在块之前使用空格
        'space-before-blocks': 'error',
        // 注释的 // 后跟至少一个空白
        'spaced-comment': ['error', 'always', { exceptions: ['-', '+'] }],
        // 要求操作符周围有空格
        'space-infix-ops': [
            'error',
            {
                int32Hint: false,
            },
        ],
        // 强制在单词类一元操作符之后使用空格，在非单词类一元操作符之前或之后不使用空格
        'space-unary-ops': 'error',
        // 强制在 switch 的冒号右边使用空格
        'switch-colon-spacing': 'error',
        // 强制箭头函数的箭头前后需要有空格
        'arrow-spacing': 'error',
        // 禁止不规则的空白
        'no-irregular-whitespace': ['error', { skipStrings: true }],
        // 禁止正则表达式字面量中出现多个空格
        'no-regex-spaces': 'error',
        // 禁止模板字符串中的嵌入表达式周围空格的使用
        'template-curly-spacing': 'error',
        // 强制剩余和扩展运算符及其表达式之间不允许有空格
        'rest-spread-spacing': 'error',
        // 强制 “for” 循环中更新子句的计数器朝着正确的方向移动
        'for-direction': 'error',
        // 禁用一成不变的循环条件
        'no-unmodified-loop-condition': 'error',
        // 要求在语句末尾使用分号
        semi: 'error',
        // 禁止不必要的分号
        'no-extra-semi': 'error',
        // 禁止出现令人困惑的多行表达式
        'no-unexpected-multiline': 'error',
        // 使用Error对象代替直接抛出异常
        'no-throw-literal': 'error',
        // 禁止在catch子句中，仅抛出原始异常
        'no-useless-catch': 'error',
        // 强制一致的缩进
        indent: ['error', 4, { SwitchCase: 1 }],
        'max-len': [
            'error',
            {
                code: 120,
                ignoreComments: true,
                ignoreRegExpLiterals: true,
                ignoreStrings: true,
                ignoreTemplateLiterals: true,
            },
        ],
        // 禁止使用行尾空白（空格、tab 和其它 Unicode 空白字符）。
        'no-trailing-spaces': 'error',
        // 禁止在逻辑表达式，条件表达式，声明，数组元素，对象属性，序列和函数参数周围使用多个空格
        'no-multi-spaces': 'error',
        // 规则旨在减少阅读代码时所需的滚动。它会在超过最大空行数量时发出警告, 选项:
        // "max"（默认2：）强制连续空行的最大数量;
        // "maxEOF" 在文件结尾处强制执行最大数量的连续空行;
        // "maxBOF" 在文件的开头强制执行最大数量的连续空行。
        'no-multiple-empty-lines': ['error', { max: 1, maxEOF: 1 }],
        // 禁止出现空语句块
        'no-empty': 'error',
        // 使用模板字符串
        'prefer-template': 'warn',
        // 禁止使用 undefined
        'no-undefined': 'error',
        // 禁止在定义前使用
        'no-use-before-define': ['error', 'nofunc'],
        // 在编写异步代码时，避免可能会产生的微妙的竞争条件错误
        'require-atomic-updates': 'error',

        // 针对 vue 的规则
        'vue/html-indent': [
            'warn',
            4,
            {
                attribute: 1,
                baseIndent: 1,
                closeBracket: 0,
                alignAttributesVertically: true,
                ignores: [],
            },
        ],
        'vue/html-self-closing': [
            'warn',
            {
                html: {
                    void: 'always',
                    normal: 'never',
                    component: 'any',
                },
                svg: 'any',
                math: 'any',
            },
        ],
        // 每行的属性数量
        'vue/max-attributes-per-line': [
            'warn',
            {
                singleline: {
                    max: 3,
                },
                multiline: {
                    max: 1,
                },
            },
        ],
        // vue 组件中 data、method 等属性的顺序，调整后代码变动太大，不利于跟踪，先关闭
        'vue/order-in-components': 'off',
        'vue/no-v-model-argument': 'off',
        'vue/no-multiple-template-root': 'off',
        'vue/multi-word-component-names': ['warn', { ignores: ['index'] }],
        'vue/prefer-template': 'warn',
        'vue/attribute-hyphenation': 'error',
        'vue/prop-name-casing': 'error',
    },
    parserOptions: {
        parser: '@babel/eslint-parser',
        babelOptions: {
            parserOpts: {
                plugins: ['jsx'],
            },
        },
    },
};
