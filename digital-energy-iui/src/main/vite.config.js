import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import vueJsx from '@vitejs/plugin-vue-jsx';
const path = require('path');

// 代理设置
const URL = '10.229.19.166:28001';
const COOKIE = '';

export default defineConfig({
    root: './',
    base: './',
    publicDir: '/iui/digital-energy/',
    build: {
        outDir: '../iui/digital-energy',
        emptyOutDir: true,
        chunkSizeWarningLimit: 3000,
    },
    plugins: [
        vue(),
        vueJsx({
            // options are passed on to @vue/babel-plugin-jsx
        }),
    ],
    resolve: {
        alias: {
            '@': path.resolve(__dirname, './src'),
            'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js',
        },
        extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue'],
    },
    define: { 'process.env': {} },

    server: {
        port: 5180,
        proxy: {
            '^/api/.*': {
                // 匹配以 '/api/' 开头的请求。
                target: `https://${URL}/`, // 请求转发到服务器
                changeOrigin: true,
                ws: true,
                secure: false,
                configure(proxy) {
                    proxy.on('proxyReq', proxyReq => {
                        proxyReq.setHeader('Host', URL);
                        proxyReq.setHeader('Cookie', COOKIE);
                        proxyReq.setHeader('Referer', `https://${URL}/iui/digital-energy/`);
                        proxyReq.setHeader('username', '');
                    });
                },
            },
        },
    },
});
