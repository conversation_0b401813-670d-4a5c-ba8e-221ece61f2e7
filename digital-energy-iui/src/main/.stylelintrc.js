module.exports = {
    extends: ['stylelint-config-standard-scss', 'stylelint-config-recommended-vue'],
    plugins: ['@stylistic/stylelint-plugin'],
    rules: {
        'alpha-value-notation': 'number', // 颜色 alpha 使用数字
        // 颜色表示时带逗号分隔，如 rgb(0,0,0)
        'color-function-notation': 'legacy',
        // 颜色简写
        'color-hex-length': 'short',
        'custom-property-pattern': null,
        // 规则缩写
        'declaration-block-no-redundant-longhand-properties': [true, { ignoreShorthands: ['inset'] }],
        'function-url-quotes': ['always', { severity: 'warning' }],
        // keyframe 期望为 kebab-case 格式
        'keyframes-name-pattern': [
            '^([a-z][a-z0-9]*)(-[a-z0-9]+)*$',
            { message: name => `Expected keyframe name "${name}" to be kebab-case`, severity: 'warning' },
        ],
        'no-descending-specificity': null, // 声明优先级
        'number-max-precision': null, // 不限制数字精度
        'rule-empty-line-before': null, // 规则前不需要空行
        // class 期望为 kebab-case 格式
        'selector-class-pattern': [
            '^([_a-z][a-z0-9]*)([-_]+[a-z0-9]+)*$',
            { message: selector => `Expected class selector "${selector}" to be kebab-case`, severity: 'warning' },
        ],
        // id 期望为 kebab-case 格式
        'selector-id-pattern': [
            '^([a-z][a-z0-9]*)(-[a-z0-9]+)*$',
            { message: selector => `Expected id selector "${selector}" to be kebab-case`, severity: 'warning' },
        ],
        // 伪类前用双冒号
        'selector-pseudo-element-colon-notation': ['double', { severity: 'warning' }],

        // stylistic rules from @stylistic/stylelint-plugin:
        // 颜色指定小写
        '@stylistic/color-hex-case': ['lower', { severity: 'error' }],
        // 分号结尾
        '@stylistic/declaration-block-trailing-semicolon': ['always', { severity: 'warning' }],
        // 样式声明中冒号前后空格
        '@stylistic/declaration-colon-space-after': 'always-single-line',
        '@stylistic/declaration-colon-space-before': 'never',
        // 缩进必须是4个空格
        '@stylistic/indentation': [4, { severity: 'error' }],
        '@stylistic/no-eol-whitespace': true, // 禁止行尾空格
        '@stylistic/number-no-trailing-zeros': true, // x.0 不需要结尾的0
        // 组合选择器前后空格 div > span
        '@stylistic/selector-combinator-space-after': 'always',
        '@stylistic/selector-combinator-space-before': 'always',
        // 多个选择器逗号后空格 a, b
        '@stylistic/selector-list-comma-space-before': 'never',
        '@stylistic/selector-list-comma-space-after': 'always-single-line',
        // 样式声明中逗号前后空格
        '@stylistic/value-list-comma-space-before': 'never',
        '@stylistic/value-list-comma-space-after': 'always-single-line',


        // 变量名格式不限制
        'scss/dollar-variable-pattern': null,
        // 变量声明前允许空行
        'scss/dollar-variable-empty-line-before': null,
    },
};
