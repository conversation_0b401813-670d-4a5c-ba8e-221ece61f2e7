#!/bin/sh

backup_host=$1
backup_port=$2
backup_user=$3
backup_password=$4
backup_db=$5
backup_table=$6
backup_file=$7

export PGPASSWORD=$backup_password

if [ $6 -eq "-" ] ; then
  /usr/bin/pg_dump -h $backup_host -p $backup_port -U $backup_user -d $backup_db -F c -c --if-exists -f $backup_file
else
  /usr/bin/pg_dump -h $backup_host -p $backup_port -U $backup_user -d $backup_db -t $backup_table -F c -c --if-exists -f $backup_file
fi

