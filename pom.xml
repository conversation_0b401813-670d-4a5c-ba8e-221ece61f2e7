<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.zte.uedm.digital.energy</groupId>
        <artifactId>digital-energy-parent</artifactId>
        <version>${digital.energy.service.version}</version>
        <relativePath>digital-energy-parent/pom.xml</relativePath>
    </parent>

    <artifactId>digitalEnergyTechPlatform</artifactId>
    <version>${digital.energy.service.version}</version>
    <packaging>pom</packaging>
    <name>数能技术平台</name>

    <properties>
        <service.name>digital-energy</service.name>
        <service.dir>${output.dir}/${service.name}</service.dir>
        <!-- 各个模块的artifactId -->
        <ms.manager.security.manager>digital-energy-security-manager</ms.manager.security.manager>
        <manager.security.manager.artifactId>${tenant.id}-${ms.manager.security.manager}</manager.security.manager.artifactId>
        <manager.security.manager.port>29124</manager.security.manager.port>

        <ms.manager.data.manager>digital-energy-data-manager</ms.manager.data.manager>
        <manager.data.manager.artifactId>${tenant.id}-${ms.manager.data.manager}</manager.data.manager.artifactId>
        <manager.data.manager.port>29125</manager.data.manager.port>

        <ms.manager.iui>digital-energy-iui</ms.manager.iui>
        <manager.iui.artifactId>${tenant.id}-${ms.manager.iui}</manager.iui.artifactId>
        <iui.port>29109</iui.port>
    </properties>

    <modules>
        <module>digital-energy-parent</module>
        <module>digital-energy-iui</module>
        <module>digital-energy-data-manager</module>
        <module>digital-energy-security-manager</module>
        <module>integration</module>
        <module>digital-energy-common</module>
    </modules>


</project>