package com.zte.uedm.digitalenergy.infrastructure.permission.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.digitalenergy.infrastructure.permission.po.RoleMenuPO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 角色菜单关联映射器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface RoleMenuMapper extends BaseMapper<RoleMenuPO> {
    
    /**
     * 根据角色ID查询菜单ID列表
     * 
     * @param roleId 角色ID
     * @return 菜单ID列表
     */
    @Select("SELECT menu_id FROM t_role_menu WHERE role_id = #{roleId}")
    List<Long> selectMenuIdsByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 根据菜单ID查询角色ID列表
     * 
     * @param menuId 菜单ID
     * @return 角色ID列表
     */
    @Select("SELECT role_id FROM t_role_menu WHERE menu_id = #{menuId}")
    List<Long> selectRoleIdsByMenuId(@Param("menuId") Long menuId);
    
    /**
     * 根据角色ID删除角色菜单关联
     * 
     * @param roleId 角色ID
     * @return 删除数量
     */
    @Delete("DELETE FROM t_role_menu WHERE role_id = #{roleId}")
    int deleteByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 根据菜单ID删除角色菜单关联
     * 
     * @param menuId 菜单ID
     * @return 删除数量
     */
    @Delete("DELETE FROM t_role_menu WHERE menu_id = #{menuId}")
    int deleteByMenuId(@Param("menuId") Long menuId);
    
    /**
     * 根据角色ID和菜单ID删除关联
     * 
     * @param roleId 角色ID
     * @param menuId 菜单ID
     * @return 删除数量
     */
    @Delete("DELETE FROM t_role_menu WHERE role_id = #{roleId} AND menu_id = #{menuId}")
    int deleteByRoleIdAndMenuId(@Param("roleId") Long roleId, @Param("menuId") Long menuId);
    
    /**
     * 批量插入角色菜单关联
     * 
     * @param roleMenuList 角色菜单关联列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<RoleMenuPO> roleMenuList);
    
    /**
     * 检查角色菜单关联是否存在
     * 
     * @param roleId 角色ID
     * @param menuId 菜单ID
     * @return 存在数量
     */
    @Select("SELECT COUNT(*) FROM t_role_menu WHERE role_id = #{roleId} AND menu_id = #{menuId}")
    int countByRoleIdAndMenuId(@Param("roleId") Long roleId, @Param("menuId") Long menuId);
}
