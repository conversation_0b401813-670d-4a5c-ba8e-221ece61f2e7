package com.zte.uedm.digitalenergy.domain.permission.service;

import com.zte.uedm.digitalenergy.domain.permission.entity.Menu;
import com.zte.uedm.digitalenergy.domain.permission.entity.Role;
import com.zte.uedm.digitalenergy.domain.permission.entity.User;
import com.zte.uedm.digitalenergy.domain.permission.repository.MenuRepository;
import com.zte.uedm.digitalenergy.domain.permission.repository.RoleRepository;
import com.zte.uedm.digitalenergy.domain.permission.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 权限领域服务
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class PermissionDomainService {
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private RoleRepository roleRepository;
    
    @Autowired
    private MenuRepository menuRepository;
    
    /**
     * 计算用户的所有权限菜单（包括直接分配和用户组继承）
     * 
     * @param userId 用户ID
     * @return 用户权限菜单列表
     */
    public List<Menu> calculateUserPermissions(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }
        
        // 获取用户所有角色ID（直接分配 + 用户组继承）
        List<Long> allRoleIds = userRepository.findAllRoleIdsByUserId(userId);
        
        if (allRoleIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 根据角色ID列表获取菜单权限
        return menuRepository.findMenusByRoleIds(allRoleIds);
    }
    
    /**
     * 构建用户菜单树
     * 
     * @param userId 用户ID
     * @return 用户菜单树
     */
    public List<Menu> buildUserMenuTree(Long userId) {
        return menuRepository.buildUserMenuTree(userId);
    }
    
    /**
     * 检查用户是否有指定菜单权限
     * 
     * @param userId 用户ID
     * @param menuCode 菜单编码
     * @return true-有权限，false-无权限
     */
    public boolean hasMenuPermission(Long userId, String menuCode) {
        if (userId == null || menuCode == null || menuCode.trim().isEmpty()) {
            return false;
        }
        
        List<Menu> userMenus = calculateUserPermissions(userId);
        return userMenus.stream()
                .anyMatch(menu -> menuCode.equals(menu.getMenuCode()));
    }
    
    /**
     * 检查用户是否有指定权限编码
     * 
     * @param userId 用户ID
     * @param permissionCode 权限编码
     * @return true-有权限，false-无权限
     */
    public boolean hasPermission(Long userId, String permissionCode) {
        if (userId == null || permissionCode == null || permissionCode.trim().isEmpty()) {
            return false;
        }
        
        List<Menu> userMenus = calculateUserPermissions(userId);
        return userMenus.stream()
                .anyMatch(menu -> permissionCode.equals(menu.getPermissionCode()));
    }
    
    /**
     * 获取用户的权限编码列表
     * 
     * @param userId 用户ID
     * @return 权限编码列表
     */
    public List<String> getUserPermissionCodes(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }
        
        List<Menu> userMenus = calculateUserPermissions(userId);
        return userMenus.stream()
                .map(Menu::getPermissionCode)
                .filter(code -> code != null && !code.trim().isEmpty())
                .distinct()
                .collect(Collectors.toList());
    }
    
    /**
     * 获取用户的菜单编码列表
     * 
     * @param userId 用户ID
     * @return 菜单编码列表
     */
    public List<String> getUserMenuCodes(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }
        
        List<Menu> userMenus = calculateUserPermissions(userId);
        return userMenus.stream()
                .map(Menu::getMenuCode)
                .distinct()
                .collect(Collectors.toList());
    }
    
    /**
     * 计算角色的权限菜单
     * 
     * @param roleId 角色ID
     * @return 角色权限菜单列表
     */
    public List<Menu> calculateRolePermissions(Long roleId) {
        if (roleId == null) {
            return new ArrayList<>();
        }
        
        return menuRepository.findMenusByRoleId(roleId);
    }
    
    /**
     * 验证角色是否可以删除
     * 
     * @param roleId 角色ID
     * @return true-可以删除，false-不可删除
     */
    public boolean canDeleteRole(Long roleId) {
        if (roleId == null) {
            return false;
        }
        
        // 检查是否有用户关联
        long userCount = roleRepository.countUsersByRoleId(roleId);
        if (userCount > 0) {
            return false;
        }
        
        // 检查是否有用户组关联
        long userGroupCount = roleRepository.countUserGroupsByRoleId(roleId);
        return userGroupCount == 0;
    }
    
    /**
     * 验证用户是否可以删除
     * 
     * @param userId 用户ID
     * @return true-可以删除，false-不可删除
     */
    public boolean canDeleteUser(Long userId) {
        if (userId == null) {
            return false;
        }
        
        // 这里可以添加业务规则，比如检查用户是否有重要数据等
        // 目前简单返回true，表示用户可以删除
        return true;
    }
    
    /**
     * 获取用户权限继承路径
     * 
     * @param userId 用户ID
     * @return 权限继承路径信息
     */
    public UserPermissionInheritance getUserPermissionInheritance(Long userId) {
        if (userId == null) {
            return new UserPermissionInheritance();
        }
        
        // 获取直接分配的角色
        List<Long> directRoleIds = userRepository.findDirectRoleIdsByUserId(userId);
        
        // 获取通过用户组继承的角色
        List<Long> groupRoleIds = userRepository.findGroupRoleIdsByUserId(userId);
        
        // 获取用户所属的用户组
        List<Long> userGroupIds = userRepository.findUserGroupIdsByUserId(userId);
        
        UserPermissionInheritance inheritance = new UserPermissionInheritance();
        inheritance.setUserId(userId);
        inheritance.setDirectRoleIds(directRoleIds);
        inheritance.setGroupRoleIds(groupRoleIds);
        inheritance.setUserGroupIds(userGroupIds);
        
        return inheritance;
    }
    
    /**
     * 合并多个角色的菜单权限
     * 
     * @param roleIds 角色ID列表
     * @return 合并后的菜单列表
     */
    public List<Menu> mergeRolePermissions(List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        Set<Menu> menuSet = new HashSet<>();
        for (Long roleId : roleIds) {
            List<Menu> roleMenus = menuRepository.findMenusByRoleId(roleId);
            menuSet.addAll(roleMenus);
        }
        
        return new ArrayList<>(menuSet);
    }
    
    /**
     * 用户权限继承信息
     */
    public static class UserPermissionInheritance {
        private Long userId;
        private List<Long> directRoleIds = new ArrayList<>();
        private List<Long> groupRoleIds = new ArrayList<>();
        private List<Long> userGroupIds = new ArrayList<>();
        
        // Getters and Setters
        public Long getUserId() {
            return userId;
        }
        
        public void setUserId(Long userId) {
            this.userId = userId;
        }
        
        public List<Long> getDirectRoleIds() {
            return directRoleIds;
        }
        
        public void setDirectRoleIds(List<Long> directRoleIds) {
            this.directRoleIds = directRoleIds != null ? directRoleIds : new ArrayList<>();
        }
        
        public List<Long> getGroupRoleIds() {
            return groupRoleIds;
        }
        
        public void setGroupRoleIds(List<Long> groupRoleIds) {
            this.groupRoleIds = groupRoleIds != null ? groupRoleIds : new ArrayList<>();
        }
        
        public List<Long> getUserGroupIds() {
            return userGroupIds;
        }
        
        public void setUserGroupIds(List<Long> userGroupIds) {
            this.userGroupIds = userGroupIds != null ? userGroupIds : new ArrayList<>();
        }
        
        /**
         * 获取所有角色ID（直接 + 继承）
         * 
         * @return 所有角色ID列表
         */
        public List<Long> getAllRoleIds() {
            Set<Long> allRoleIds = new HashSet<>();
            allRoleIds.addAll(directRoleIds);
            allRoleIds.addAll(groupRoleIds);
            return new ArrayList<>(allRoleIds);
        }
    }
}
