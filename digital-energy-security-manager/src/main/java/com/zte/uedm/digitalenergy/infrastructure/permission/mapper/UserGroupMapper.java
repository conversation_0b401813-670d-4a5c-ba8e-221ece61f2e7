package com.zte.uedm.digitalenergy.infrastructure.permission.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.digitalenergy.infrastructure.permission.po.UserGroupPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户组数据访问接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface UserGroupMapper extends BaseMapper<UserGroupPO> {
    
    /**
     * 根据用户组编码查询用户组
     * 
     * @param groupCode 用户组编码
     * @return 用户组PO
     */
    UserGroupPO selectByGroupCode(@Param("groupCode") String groupCode);
    
    /**
     * 根据用户组名称查询用户组
     * 
     * @param groupName 用户组名称
     * @return 用户组PO
     */
    UserGroupPO selectByGroupName(@Param("groupName") String groupName);
    
    /**
     * 分页查询用户组列表
     * 
     * @param offset 偏移量
     * @param limit 限制数量
     * @param groupName 用户组名称（可选）
     * @return 用户组PO列表
     */
    List<UserGroupPO> selectByPage(@Param("offset") int offset, 
                                   @Param("limit") int limit, 
                                   @Param("groupName") String groupName);
    
    /**
     * 统计用户组总数
     * 
     * @param groupName 用户组名称（可选）
     * @return 用户组总数
     */
    long countUserGroups(@Param("groupName") String groupName);
    
    /**
     * 根据用户组编码统计数量（排除指定ID）
     * 
     * @param groupCode 用户组编码
     * @param excludeId 排除的用户组ID
     * @return 数量
     */
    long countByGroupCodeAndIdNot(@Param("groupCode") String groupCode, 
                                  @Param("excludeId") Long excludeId);
    
    /**
     * 根据用户组名称统计数量（排除指定ID）
     * 
     * @param groupName 用户组名称
     * @param excludeId 排除的用户组ID
     * @return 数量
     */
    long countByGroupNameAndIdNot(@Param("groupName") String groupName, 
                                  @Param("excludeId") Long excludeId);
    
    /**
     * 根据角色ID查询用户组列表
     * 
     * @param roleId 角色ID
     * @return 用户组PO列表
     */
    List<UserGroupPO> selectByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 根据用户ID查询用户组列表
     * 
     * @param userId 用户ID
     * @return 用户组PO列表
     */
    List<UserGroupPO> selectByUserId(@Param("userId") Long userId);
}
