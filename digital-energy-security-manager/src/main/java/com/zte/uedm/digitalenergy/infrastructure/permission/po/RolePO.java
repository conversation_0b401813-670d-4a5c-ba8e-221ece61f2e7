package com.zte.uedm.digitalenergy.infrastructure.permission.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 角色持久化对象
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@TableName("t_role")
public class RolePO {
    
    /**
     * 角色ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 角色名称
     */
    @TableField("role_name")
    private String roleName;
    
    /**
     * 角色编码
     */
    @TableField("role_code")
    private String roleCode;
    
    /**
     * 角色描述
     */
    @TableField("role_description")
    private String roleDescription;
    
    /**
     * 角色类型：DEFAULT-默认角色，CUSTOM-自定义角色
     */
    @TableField("role_type")
    private String roleType;
    
    /**
     * 是否默认角色
     */
    @TableField("is_default")
    private Boolean isDefault;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
    
    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;
    
    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;
    
    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;
}
