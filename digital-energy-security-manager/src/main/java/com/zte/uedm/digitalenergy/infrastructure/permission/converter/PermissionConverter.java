package com.zte.uedm.digitalenergy.infrastructure.permission.converter;

import com.zte.uedm.digitalenergy.domain.permission.entity.*;
import com.zte.uedm.digitalenergy.domain.permission.valueobject.MenuType;
import com.zte.uedm.digitalenergy.domain.permission.valueobject.Organization;
import com.zte.uedm.digitalenergy.domain.permission.valueobject.RoleType;
import com.zte.uedm.digitalenergy.domain.permission.valueobject.UserStatus;
import com.zte.uedm.digitalenergy.infrastructure.permission.po.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 权限管理对象转换器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface PermissionConverter {
    
    PermissionConverter INSTANCE = Mappers.getMapper(PermissionConverter.class);
    
    // ========== Role 转换 ==========
    
    /**
     * Role实体转RolePO
     * 
     * @param role 角色实体
     * @return 角色PO
     */
    @Mapping(target = "roleType", expression = "java(role.getRoleType() != null ? role.getRoleType().getCode() : null)")
    @Mapping(target = "status", expression = "java(role.getStatus() != null ? role.getStatus().getCode() : null)")
    RolePO toRolePO(Role role);
    
    /**
     * RolePO转Role实体
     * 
     * @param rolePO 角色PO
     * @return 角色实体
     */
    @Mapping(target = "roleType", expression = "java(rolePO.getRoleType() != null ? RoleType.fromCode(rolePO.getRoleType()) : null)")
    @Mapping(target = "status", expression = "java(rolePO.getStatus() != null ? UserStatus.fromCode(rolePO.getStatus()) : null)")
    @Mapping(target = "menuPermissions", ignore = true)
    Role toRole(RolePO rolePO);
    
    /**
     * Role实体列表转RolePO列表
     * 
     * @param roles 角色实体列表
     * @return 角色PO列表
     */
    List<RolePO> toRolePOList(List<Role> roles);
    
    /**
     * RolePO列表转Role实体列表
     * 
     * @param rolePOs 角色PO列表
     * @return 角色实体列表
     */
    List<Role> toRoleList(List<RolePO> rolePOs);
    
    // ========== User 转换 ==========
    
    /**
     * User实体转UserPO
     * 
     * @param user 用户实体
     * @return 用户PO
     */
    @Mapping(target = "organization", expression = "java(user.getOrganization() != null ? user.getOrganization().getName() : null)")
    @Mapping(target = "status", expression = "java(user.getStatus() != null ? user.getStatus().getCode() : null)")
    UserPO toUserPO(User user);
    
    /**
     * UserPO转User实体
     * 
     * @param userPO 用户PO
     * @return 用户实体
     */
    @Mapping(target = "organization", expression = "java(userPO.getOrganization() != null ? new Organization(userPO.getOrganization(), null, null) : null)")
    @Mapping(target = "status", expression = "java(userPO.getStatus() != null ? UserStatus.fromCode(userPO.getStatus()) : null)")
    @Mapping(target = "roleAssignments", ignore = true)
    User toUser(UserPO userPO);
    
    /**
     * User实体列表转UserPO列表
     * 
     * @param users 用户实体列表
     * @return 用户PO列表
     */
    List<UserPO> toUserPOList(List<User> users);
    
    /**
     * UserPO列表转User实体列表
     * 
     * @param userPOs 用户PO列表
     * @return 用户实体列表
     */
    List<User> toUserList(List<UserPO> userPOs);
    
    // ========== Menu 转换 ==========
    
    /**
     * Menu实体转MenuPO
     * 
     * @param menu 菜单实体
     * @return 菜单PO
     */
    @Mapping(target = "menuType", expression = "java(menu.getMenuType() != null ? menu.getMenuType().getCode() : null)")
    @Mapping(target = "status", expression = "java(menu.getStatus() != null ? menu.getStatus().getCode() : null)")
    MenuPO toMenuPO(Menu menu);
    
    /**
     * MenuPO转Menu实体
     * 
     * @param menuPO 菜单PO
     * @return 菜单实体
     */
    @Mapping(target = "menuType", expression = "java(menuPO.getMenuType() != null ? MenuType.fromCode(menuPO.getMenuType()) : null)")
    @Mapping(target = "status", expression = "java(menuPO.getStatus() != null ? UserStatus.fromCode(menuPO.getStatus()) : null)")
    @Mapping(target = "children", ignore = true)
    Menu toMenu(MenuPO menuPO);
    
    /**
     * Menu实体列表转MenuPO列表
     * 
     * @param menus 菜单实体列表
     * @return 菜单PO列表
     */
    List<MenuPO> toMenuPOList(List<Menu> menus);
    
    /**
     * MenuPO列表转Menu实体列表
     * 
     * @param menuPOs 菜单PO列表
     * @return 菜单实体列表
     */
    List<Menu> toMenuList(List<MenuPO> menuPOs);
    
    // ========== UserGroup 转换 ==========
    
    /**
     * UserGroup实体转UserGroupPO
     * 
     * @param userGroup 用户组实体
     * @return 用户组PO
     */
    @Mapping(target = "status", expression = "java(userGroup.getStatus() != null ? userGroup.getStatus().getCode() : null)")
    UserGroupPO toUserGroupPO(UserGroup userGroup);
    
    /**
     * UserGroupPO转UserGroup实体
     * 
     * @param userGroupPO 用户组PO
     * @return 用户组实体
     */
    @Mapping(target = "status", expression = "java(userGroupPO.getStatus() != null ? UserStatus.fromCode(userGroupPO.getStatus()) : null)")
    @Mapping(target = "members", ignore = true)
    @Mapping(target = "roleAssignments", ignore = true)
    UserGroup toUserGroup(UserGroupPO userGroupPO);
    
    /**
     * UserGroup实体列表转UserGroupPO列表
     * 
     * @param userGroups 用户组实体列表
     * @return 用户组PO列表
     */
    List<UserGroupPO> toUserGroupPOList(List<UserGroup> userGroups);
    
    /**
     * UserGroupPO列表转UserGroup实体列表
     * 
     * @param userGroupPOs 用户组PO列表
     * @return 用户组实体列表
     */
    List<UserGroup> toUserGroupList(List<UserGroupPO> userGroupPOs);
    
    // ========== 关联关系转换 ==========
    
    /**
     * RoleMenuPermission实体转RoleMenuPO
     * 
     * @param roleMenuPermission 角色菜单权限实体
     * @return 角色菜单PO
     */
    RoleMenuPO toRoleMenuPO(RoleMenuPermission roleMenuPermission);
    
    /**
     * RoleMenuPO转RoleMenuPermission实体
     * 
     * @param roleMenuPO 角色菜单PO
     * @return 角色菜单权限实体
     */
    RoleMenuPermission toRoleMenuPermission(RoleMenuPO roleMenuPO);
    
    /**
     * UserRoleAssignment实体转UserRolePO
     * 
     * @param userRoleAssignment 用户角色分配实体
     * @return 用户角色PO
     */
    UserRolePO toUserRolePO(UserRoleAssignment userRoleAssignment);
    
    /**
     * UserRolePO转UserRoleAssignment实体
     * 
     * @param userRolePO 用户角色PO
     * @return 用户角色分配实体
     */
    UserRoleAssignment toUserRoleAssignment(UserRolePO userRolePO);
    
    /**
     * UserGroupMember实体转UserGroupMemberPO
     * 
     * @param userGroupMember 用户组成员实体
     * @return 用户组成员PO
     */
    UserGroupMemberPO toUserGroupMemberPO(UserGroupMember userGroupMember);
    
    /**
     * UserGroupMemberPO转UserGroupMember实体
     * 
     * @param userGroupMemberPO 用户组成员PO
     * @return 用户组成员实体
     */
    UserGroupMember toUserGroupMember(UserGroupMemberPO userGroupMemberPO);
    
    /**
     * UserGroupRoleAssignment实体转UserGroupRolePO
     * 
     * @param userGroupRoleAssignment 用户组角色分配实体
     * @return 用户组角色PO
     */
    UserGroupRolePO toUserGroupRolePO(UserGroupRoleAssignment userGroupRoleAssignment);
    
    /**
     * UserGroupRolePO转UserGroupRoleAssignment实体
     * 
     * @param userGroupRolePO 用户组角色PO
     * @return 用户组角色分配实体
     */
    UserGroupRoleAssignment toUserGroupRoleAssignment(UserGroupRolePO userGroupRolePO);
}
