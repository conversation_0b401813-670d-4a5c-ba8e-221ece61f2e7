package com.zte.uedm.digitalenergy.infrastructure.permission.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 菜单持久化对象
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@TableName("t_menu")
public class MenuPO {
    
    /**
     * 菜单ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 菜单名称
     */
    @TableField("menu_name")
    private String menuName;
    
    /**
     * 菜单编码
     */
    @TableField("menu_code")
    private String menuCode;
    
    /**
     * 父菜单ID，0表示根菜单
     */
    @TableField("parent_id")
    private Long parentId;
    
    /**
     * 菜单路径
     */
    @TableField("menu_path")
    private String menuPath;
    
    /**
     * 排序号
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 菜单层级
     */
    @TableField("menu_level")
    private Integer menuLevel;

    /**
     * 菜单类型：MENU-菜单，BUTTON-按钮
     */
    @TableField("menu_type")
    private String menuType;

    /**
     * 权限编码
     */
    @TableField("permission_code")
    private String permissionCode;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;
}
