package com.zte.uedm.digitalenergy.application.permission.dto;

import lombok.Data;

import java.util.List;

/**
 * 用户权限继承数据传输对象
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class UserPermissionInheritanceDTO {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 用户工号
     */
    private String userCode;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 直接分配的角色ID列表
     */
    private List<Long> directRoleIds;
    
    /**
     * 直接分配的角色列表（用于展示）
     */
    private List<RoleDTO> directRoles;
    
    /**
     * 通过用户组继承的角色ID列表
     */
    private List<Long> groupRoleIds;
    
    /**
     * 通过用户组继承的角色列表（用于展示）
     */
    private List<RoleDTO> groupRoles;
    
    /**
     * 用户所属的用户组ID列表
     */
    private List<Long> userGroupIds;
    
    /**
     * 用户所属的用户组列表（用于展示）
     */
    private List<UserGroupDTO> userGroups;
    
    /**
     * 所有权限菜单列表（直接分配 + 用户组继承）
     */
    private List<MenuDTO> allPermissions;
    
    /**
     * 直接分配的权限菜单列表
     */
    private List<MenuDTO> directPermissions;
    
    /**
     * 通过用户组继承的权限菜单列表
     */
    private List<MenuDTO> groupPermissions;
    
    /**
     * 权限继承路径详情
     */
    private List<PermissionInheritancePathDTO> inheritancePaths;
    
    /**
     * 权限继承路径详情DTO
     */
    @Data
    public static class PermissionInheritancePathDTO {
        
        /**
         * 菜单ID
         */
        private Long menuId;
        
        /**
         * 菜单名称
         */
        private String menuName;
        
        /**
         * 菜单编码
         */
        private String menuCode;
        
        /**
         * 继承来源类型：DIRECT-直接分配，GROUP-用户组继承
         */
        private String sourceType;
        
        /**
         * 来源角色ID
         */
        private Long sourceRoleId;
        
        /**
         * 来源角色名称
         */
        private String sourceRoleName;
        
        /**
         * 来源用户组ID（如果是用户组继承）
         */
        private Long sourceUserGroupId;
        
        /**
         * 来源用户组名称（如果是用户组继承）
         */
        private String sourceUserGroupName;
        
        /**
         * 继承路径描述
         */
        private String pathDescription;
    }
}
