package com.zte.uedm.digitalenergy.domain.permission.repository;

import com.zte.uedm.digitalenergy.domain.permission.entity.UserGroup;

import java.util.List;
import java.util.Optional;

/**
 * 用户组仓储接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface UserGroupRepository {
    
    /**
     * 保存用户组
     * 
     * @param userGroup 用户组实体
     * @return 保存后的用户组实体
     */
    UserGroup save(UserGroup userGroup);
    
    /**
     * 根据ID查找用户组
     * 
     * @param id 用户组ID
     * @return 用户组实体
     */
    Optional<UserGroup> findById(Long id);
    
    /**
     * 根据用户组编码查找用户组
     * 
     * @param groupCode 用户组编码
     * @return 用户组实体
     */
    Optional<UserGroup> findByGroupCode(String groupCode);
    
    /**
     * 根据用户组名称查找用户组
     * 
     * @param groupName 用户组名称
     * @return 用户组实体
     */
    Optional<UserGroup> findByGroupName(String groupName);
    
    /**
     * 查找所有用户组
     * 
     * @return 用户组列表
     */
    List<UserGroup> findAll();
    
    /**
     * 分页查询用户组列表
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param groupName 用户组名称（可选）
     * @return 用户组列表
     */
    List<UserGroup> findByPage(int pageNum, int pageSize, String groupName);
    
    /**
     * 统计用户组总数
     * 
     * @param groupName 用户组名称（可选）
     * @return 用户组总数
     */
    long countUserGroups(String groupName);
    
    /**
     * 删除用户组
     * 
     * @param userGroup 用户组实体
     */
    void delete(UserGroup userGroup);
    
    /**
     * 根据ID删除用户组
     * 
     * @param id 用户组ID
     */
    void deleteById(Long id);
    
    /**
     * 批量删除用户组
     * 
     * @param ids 用户组ID列表
     */
    void deleteByIds(List<Long> ids);
    
    /**
     * 检查用户组编码是否存在
     * 
     * @param groupCode 用户组编码
     * @return true-存在，false-不存在
     */
    boolean existsByGroupCode(String groupCode);
    
    /**
     * 检查用户组名称是否存在
     * 
     * @param groupName 用户组名称
     * @return true-存在，false-不存在
     */
    boolean existsByGroupName(String groupName);
    
    /**
     * 检查用户组编码是否存在（排除指定ID）
     * 
     * @param groupCode 用户组编码
     * @param excludeId 排除的用户组ID
     * @return true-存在，false-不存在
     */
    boolean existsByGroupCodeAndIdNot(String groupCode, Long excludeId);
    
    /**
     * 检查用户组名称是否存在（排除指定ID）
     * 
     * @param groupName 用户组名称
     * @param excludeId 排除的用户组ID
     * @return true-存在，false-不存在
     */
    boolean existsByGroupNameAndIdNot(String groupName, Long excludeId);
    
    /**
     * 检查用户组是否存在
     * 
     * @param id 用户组ID
     * @return true-存在，false-不存在
     */
    boolean existsById(Long id);
    
    /**
     * 根据角色ID查找用户组列表
     * 
     * @param roleId 角色ID
     * @return 用户组列表
     */
    List<UserGroup> findByRoleId(Long roleId);
    
    /**
     * 根据用户ID查找用户组列表
     * 
     * @param userId 用户ID
     * @return 用户组列表
     */
    List<UserGroup> findByUserId(Long userId);
    
    /**
     * 查找用户组的所有角色ID
     * 
     * @param userGroupId 用户组ID
     * @return 角色ID列表
     */
    List<Long> findRoleIdsByUserGroupId(Long userGroupId);
    
    /**
     * 查找用户组的所有成员ID
     * 
     * @param userGroupId 用户组ID
     * @return 用户ID列表
     */
    List<Long> findMemberIdsByUserGroupId(Long userGroupId);
    
    /**
     * 检查用户是否属于指定用户组
     * 
     * @param userId 用户ID
     * @param userGroupId 用户组ID
     * @return true-属于，false-不属于
     */
    boolean isMemberOfUserGroup(Long userId, Long userGroupId);
    
    /**
     * 检查角色是否分配给指定用户组
     * 
     * @param roleId 角色ID
     * @param userGroupId 用户组ID
     * @return true-已分配，false-未分配
     */
    boolean isRoleAssignedToUserGroup(Long roleId, Long userGroupId);
}
