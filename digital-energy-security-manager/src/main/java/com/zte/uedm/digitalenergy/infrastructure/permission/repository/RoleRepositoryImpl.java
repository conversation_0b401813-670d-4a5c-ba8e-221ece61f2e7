package com.zte.uedm.digitalenergy.infrastructure.permission.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zte.uedm.digitalenergy.domain.permission.aggregate.RoleAggregate;
import com.zte.uedm.digitalenergy.domain.permission.entity.Role;
import com.zte.uedm.digitalenergy.domain.permission.entity.RoleMenuPermission;
import com.zte.uedm.digitalenergy.domain.permission.repository.RoleRepository;
import com.zte.uedm.digitalenergy.domain.permission.valueobject.RoleType;
import com.zte.uedm.digitalenergy.infrastructure.permission.converter.PermissionConverter;
import com.zte.uedm.digitalenergy.infrastructure.permission.mapper.RoleMapper;
import com.zte.uedm.digitalenergy.infrastructure.permission.mapper.RoleMenuMapper;
import com.zte.uedm.digitalenergy.infrastructure.permission.po.RoleMenuPO;
import com.zte.uedm.digitalenergy.infrastructure.permission.po.RolePO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 角色仓储实现
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Repository
public class RoleRepositoryImpl implements RoleRepository {
    
    private static final Logger logger = LoggerFactory.getLogger(RoleRepositoryImpl.class);
    
    @Autowired
    private RoleMapper roleMapper;
    
    @Autowired
    private RoleMenuMapper roleMenuMapper;
    
    @Override
    @Transactional
    public RoleAggregate save(RoleAggregate roleAggregate) {
        logger.info("Saving role aggregate: {}", roleAggregate.getRoleCode());
        
        Role role = roleAggregate.getRole();
        RolePO rolePO = PermissionConverter.INSTANCE.toRolePO(role);
        
        if (role.getId() == null) {
            // 新增角色
            rolePO.setCreateTime(LocalDateTime.now());
            roleMapper.insert(rolePO);
            role.setId(rolePO.getId());
        } else {
            // 更新角色
            rolePO.setUpdateTime(LocalDateTime.now());
            roleMapper.updateById(rolePO);
        }
        
        // 保存角色菜单权限
        saveRoleMenuPermissions(role.getId(), roleAggregate.getMenuPermissions());
        
        logger.info("Role aggregate saved successfully with id: {}", role.getId());
        return roleAggregate;
    }
    
    @Override
    public Optional<RoleAggregate> findById(Long id) {
        logger.debug("Finding role by id: {}", id);
        
        RolePO rolePO = roleMapper.selectById(id);
        if (rolePO == null) {
            return Optional.empty();
        }
        
        Role role = PermissionConverter.INSTANCE.toRole(rolePO);
        
        // 加载角色菜单权限
        List<Long> menuIds = roleMenuMapper.selectMenuIdsByRoleId(id);
        for (Long menuId : menuIds) {
            role.addMenuPermission(menuId);
        }
        
        return Optional.of(new RoleAggregate(role));
    }
    
    @Override
    public Optional<RoleAggregate> findByRoleCode(String roleCode) {
        logger.debug("Finding role by code: {}", roleCode);
        
        RolePO rolePO = roleMapper.selectByRoleCode(roleCode);
        if (rolePO == null) {
            return Optional.empty();
        }
        
        Role role = PermissionConverter.INSTANCE.toRole(rolePO);
        
        // 加载角色菜单权限
        List<Long> menuIds = roleMenuMapper.selectMenuIdsByRoleId(role.getId());
        for (Long menuId : menuIds) {
            role.addMenuPermission(menuId);
        }
        
        return Optional.of(new RoleAggregate(role));
    }
    
    @Override
    public Optional<RoleAggregate> findByRoleName(String roleName) {
        logger.debug("Finding role by name: {}", roleName);
        
        RolePO rolePO = roleMapper.selectByRoleName(roleName);
        if (rolePO == null) {
            return Optional.empty();
        }
        
        Role role = PermissionConverter.INSTANCE.toRole(rolePO);
        
        // 加载角色菜单权限
        List<Long> menuIds = roleMenuMapper.selectMenuIdsByRoleId(role.getId());
        for (Long menuId : menuIds) {
            role.addMenuPermission(menuId);
        }
        
        return Optional.of(new RoleAggregate(role));
    }
    
    @Override
    public List<Role> findAll() {
        logger.debug("Finding all roles");
        
        List<RolePO> rolePOs = roleMapper.selectList(null);
        return PermissionConverter.INSTANCE.toRoleList(rolePOs);
    }
    
    @Override
    public List<Role> findByRoleType(RoleType roleType) {
        logger.debug("Finding roles by type: {}", roleType);
        
        List<RolePO> rolePOs = roleMapper.selectByRoleType(roleType.getCode());
        return PermissionConverter.INSTANCE.toRoleList(rolePOs);
    }
    
    @Override
    public List<Role> findByPage(int pageNum, int pageSize, String roleName, RoleType roleType) {
        logger.debug("Finding roles by page: pageNum={}, pageSize={}, roleName={}, roleType={}", 
                    pageNum, pageSize, roleName, roleType);
        
        int offset = (pageNum - 1) * pageSize;
        String roleTypeCode = roleType != null ? roleType.getCode() : null;
        
        List<RolePO> rolePOs = roleMapper.selectByPage(offset, pageSize, roleName, roleTypeCode);
        return PermissionConverter.INSTANCE.toRoleList(rolePOs);
    }
    
    @Override
    public long countRoles(String roleName, RoleType roleType) {
        logger.debug("Counting roles: roleName={}, roleType={}", roleName, roleType);
        
        String roleTypeCode = roleType != null ? roleType.getCode() : null;
        return roleMapper.countRoles(roleName, roleTypeCode);
    }
    
    @Override
    public long countCustomRoles() {
        logger.debug("Counting custom roles");
        
        return roleMapper.countCustomRoles();
    }
    
    @Override
    @Transactional
    public void delete(RoleAggregate roleAggregate) {
        logger.info("Deleting role aggregate: {}", roleAggregate.getRoleCode());
        
        Long roleId = roleAggregate.getId();
        
        // 删除角色菜单权限
        roleMenuMapper.deleteByRoleId(roleId);
        
        // 删除角色
        roleMapper.deleteById(roleId);
        
        logger.info("Role aggregate deleted successfully: {}", roleAggregate.getRoleCode());
    }
    
    @Override
    @Transactional
    public void deleteById(Long id) {
        logger.info("Deleting role by id: {}", id);
        
        // 删除角色菜单权限
        roleMenuMapper.deleteByRoleId(id);
        
        // 删除角色
        roleMapper.deleteById(id);
        
        logger.info("Role deleted successfully with id: {}", id);
    }
    
    @Override
    @Transactional
    public void deleteByIds(List<Long> ids) {
        logger.info("Deleting roles by ids: {}", ids);
        
        for (Long id : ids) {
            deleteById(id);
        }
        
        logger.info("Roles deleted successfully, count: {}", ids.size());
    }
    
    @Override
    public boolean existsByRoleCode(String roleCode) {
        RolePO rolePO = roleMapper.selectByRoleCode(roleCode);
        return rolePO != null;
    }
    
    @Override
    public boolean existsByRoleName(String roleName) {
        RolePO rolePO = roleMapper.selectByRoleName(roleName);
        return rolePO != null;
    }
    
    @Override
    public boolean existsByRoleCodeAndIdNot(String roleCode, Long excludeId) {
        return roleMapper.countByRoleCodeAndIdNot(roleCode, excludeId) > 0;
    }
    
    @Override
    public boolean existsByRoleNameAndIdNot(String roleName, Long excludeId) {
        return roleMapper.countByRoleNameAndIdNot(roleName, excludeId) > 0;
    }
    
    @Override
    public long countUsersByRoleId(Long roleId) {
        return roleMapper.countUsersByRoleId(roleId);
    }
    
    @Override
    public long countUserGroupsByRoleId(Long roleId) {
        return roleMapper.countUserGroupsByRoleId(roleId);
    }
    
    /**
     * 保存角色菜单权限
     * 
     * @param roleId 角色ID
     * @param menuPermissions 菜单权限列表
     */
    private void saveRoleMenuPermissions(Long roleId, List<RoleMenuPermission> menuPermissions) {
        logger.debug("Saving role menu permissions for role: {}", roleId);
        
        // 删除现有权限
        roleMenuMapper.deleteByRoleId(roleId);
        
        // 插入新权限
        if (menuPermissions != null && !menuPermissions.isEmpty()) {
            List<RoleMenuPO> roleMenuPOs = menuPermissions.stream()
                    .map(permission -> {
                        RoleMenuPO po = new RoleMenuPO();
                        po.setRoleId(roleId);
                        po.setMenuId(permission.getMenuId());
                        po.setCreateTime(LocalDateTime.now());
                        return po;
                    })
                    .collect(Collectors.toList());
            
            roleMenuMapper.batchInsert(roleMenuPOs);
        }
        
        logger.debug("Role menu permissions saved, count: {}", 
                    menuPermissions != null ? menuPermissions.size() : 0);
    }
}
