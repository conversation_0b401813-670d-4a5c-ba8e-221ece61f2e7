package com.zte.uedm.digitalenergy.infrastructure.permission.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.digitalenergy.infrastructure.permission.po.UserPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户映射器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface UserMapper extends BaseMapper<UserPO> {
    
    /**
     * 根据用户工号查询用户
     * 
     * @param userCode 用户工号
     * @return 用户
     */
    @Select("SELECT * FROM t_user WHERE user_code = #{userCode}")
    UserPO selectByUserCode(@Param("userCode") String userCode);
    
    /**
     * 根据用户名查询用户
     * 
     * @param username 用户名
     * @return 用户
     */
    @Select("SELECT * FROM t_user WHERE username = #{username}")
    UserPO selectByUsername(@Param("username") String username);
    
    /**
     * 检查用户工号是否存在（排除指定ID）
     * 
     * @param userCode 用户工号
     * @param excludeId 排除的用户ID
     * @return 存在数量
     */
    @Select("SELECT COUNT(*) FROM t_user WHERE user_code = #{userCode} AND id != #{excludeId}")
    int countByUserCodeAndIdNot(@Param("userCode") String userCode, @Param("excludeId") Long excludeId);
    
    /**
     * 根据角色ID查询用户列表
     * 
     * @param roleId 角色ID
     * @return 用户列表
     */
    @Select("SELECT u.* FROM t_user u INNER JOIN t_user_role ur ON u.id = ur.user_id WHERE ur.role_id = #{roleId} AND u.status = 1")
    List<UserPO> selectByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 根据用户组ID查询用户列表
     * 
     * @param userGroupId 用户组ID
     * @return 用户列表
     */
    @Select("SELECT u.* FROM t_user u INNER JOIN t_user_group_member ugm ON u.id = ugm.user_id WHERE ugm.user_group_id = #{userGroupId} AND u.status = 1")
    List<UserPO> selectByUserGroupId(@Param("userGroupId") Long userGroupId);
    
    /**
     * 查询用户的所有角色ID（包括直接分配和用户组继承）
     * 
     * @param userId 用户ID
     * @return 角色ID列表
     */
    @Select({
        "SELECT DISTINCT role_id FROM (",
        "  SELECT ur.role_id FROM t_user_role ur WHERE ur.user_id = #{userId}",
        "  UNION",
        "  SELECT ugr.role_id FROM t_user_group_member ugm",
        "  INNER JOIN t_user_group_role ugr ON ugm.user_group_id = ugr.user_group_id",
        "  WHERE ugm.user_id = #{userId}",
        ") AS all_roles"
    })
    List<Long> selectAllRoleIdsByUserId(@Param("userId") Long userId);
    
    /**
     * 查询用户的直接分配角色ID
     * 
     * @param userId 用户ID
     * @return 角色ID列表
     */
    @Select("SELECT role_id FROM t_user_role WHERE user_id = #{userId}")
    List<Long> selectDirectRoleIdsByUserId(@Param("userId") Long userId);
    
    /**
     * 查询用户通过用户组继承的角色ID
     * 
     * @param userId 用户ID
     * @return 角色ID列表
     */
    @Select({
        "SELECT DISTINCT ugr.role_id FROM t_user_group_member ugm",
        "INNER JOIN t_user_group_role ugr ON ugm.user_group_id = ugr.user_group_id",
        "WHERE ugm.user_id = #{userId}"
    })
    List<Long> selectGroupRoleIdsByUserId(@Param("userId") Long userId);
    
    /**
     * 查询用户所属的用户组ID列表
     * 
     * @param userId 用户ID
     * @return 用户组ID列表
     */
    @Select("SELECT user_group_id FROM t_user_group_member WHERE user_id = #{userId}")
    List<Long> selectUserGroupIdsByUserId(@Param("userId") Long userId);
    
    /**
     * 分页查询用户列表
     * 
     * @param offset 偏移量
     * @param limit 限制数量
     * @param username 用户名（可选）
     * @param userCode 用户工号（可选）
     * @param organization 组织机构（可选）
     * @return 用户列表
     */
    List<UserPO> selectByPage(@Param("offset") int offset, 
                             @Param("limit") int limit,
                             @Param("username") String username, 
                             @Param("userCode") String userCode,
                             @Param("organization") String organization);
    
    /**
     * 统计用户总数
     * 
     * @param username 用户名（可选）
     * @param userCode 用户工号（可选）
     * @param organization 组织机构（可选）
     * @return 用户总数
     */
    long countUsers(@Param("username") String username, 
                   @Param("userCode") String userCode,
                   @Param("organization") String organization);
}
