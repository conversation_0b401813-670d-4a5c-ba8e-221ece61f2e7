package com.zte.uedm.digitalenergy.infrastructure.permission.repository;

import com.zte.uedm.digitalenergy.domain.permission.entity.UserGroup;
import com.zte.uedm.digitalenergy.domain.permission.repository.UserGroupRepository;
import com.zte.uedm.digitalenergy.infrastructure.permission.converter.PermissionConverter;
import com.zte.uedm.digitalenergy.infrastructure.permission.mapper.UserGroupMapper;
import com.zte.uedm.digitalenergy.infrastructure.permission.mapper.UserGroupMemberMapper;
import com.zte.uedm.digitalenergy.infrastructure.permission.mapper.UserGroupRoleMapper;
import com.zte.uedm.digitalenergy.infrastructure.permission.po.UserGroupPO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户组仓储实现
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Repository
public class UserGroupRepositoryImpl implements UserGroupRepository {
    
    private static final Logger logger = LoggerFactory.getLogger(UserGroupRepositoryImpl.class);
    
    @Autowired
    private UserGroupMapper userGroupMapper;
    
    @Autowired
    private UserGroupMemberMapper userGroupMemberMapper;
    
    @Autowired
    private UserGroupRoleMapper userGroupRoleMapper;
    
    @Override
    @Transactional
    public UserGroup save(UserGroup userGroup) {
        logger.info("Saving user group: {}", userGroup.getGroupCode());
        
        UserGroupPO userGroupPO = PermissionConverter.INSTANCE.toUserGroupPO(userGroup);
        
        if (userGroup.getId() == null) {
            // 新增用户组
            userGroupPO.setCreateTime(LocalDateTime.now());
            userGroupMapper.insert(userGroupPO);
            userGroup.setId(userGroupPO.getId());
        } else {
            // 更新用户组
            userGroupPO.setUpdateTime(LocalDateTime.now());
            userGroupMapper.updateById(userGroupPO);
        }
        
        // 保存用户组成员关系
        saveUserGroupMembers(userGroup.getId(), userGroup.getMemberIds());

        // 保存用户组角色分配
        saveUserGroupRoles(userGroup.getId(), userGroup.getRoleIds());
        
        logger.info("User group saved successfully with id: {}", userGroup.getId());
        return userGroup;
    }
    
    @Override
    public Optional<UserGroup> findById(Long id) {
        logger.debug("Finding user group by id: {}", id);
        
        UserGroupPO userGroupPO = userGroupMapper.selectById(id);
        if (userGroupPO == null) {
            return Optional.empty();
        }
        
        UserGroup userGroup = PermissionConverter.INSTANCE.toUserGroup(userGroupPO);
        
        // 加载用户组成员
        List<Long> memberIds = userGroupMemberMapper.selectUserIdsByUserGroupId(id);
        for (Long memberId : memberIds) {
            userGroup.addMember(memberId);
        }
        
        // 加载用户组角色分配
        List<Long> roleIds = userGroupRoleMapper.selectRoleIdsByUserGroupId(id);
        for (Long roleId : roleIds) {
            userGroup.assignRole(roleId);
        }
        
        return Optional.of(userGroup);
    }
    
    @Override
    public Optional<UserGroup> findByGroupCode(String groupCode) {
        logger.debug("Finding user group by code: {}", groupCode);
        
        UserGroupPO userGroupPO = userGroupMapper.selectByGroupCode(groupCode);
        if (userGroupPO == null) {
            return Optional.empty();
        }
        
        UserGroup userGroup = PermissionConverter.INSTANCE.toUserGroup(userGroupPO);
        
        // 加载用户组成员和角色分配
        loadUserGroupDetails(userGroup);
        
        return Optional.of(userGroup);
    }
    
    @Override
    public Optional<UserGroup> findByGroupName(String groupName) {
        logger.debug("Finding user group by name: {}", groupName);
        
        UserGroupPO userGroupPO = userGroupMapper.selectByGroupName(groupName);
        if (userGroupPO == null) {
            return Optional.empty();
        }
        
        UserGroup userGroup = PermissionConverter.INSTANCE.toUserGroup(userGroupPO);
        
        // 加载用户组成员和角色分配
        loadUserGroupDetails(userGroup);
        
        return Optional.of(userGroup);
    }
    
    @Override
    public List<UserGroup> findAll() {
        logger.debug("Finding all user groups");
        
        List<UserGroupPO> userGroupPOs = userGroupMapper.selectList(null);
        return PermissionConverter.INSTANCE.toUserGroupList(userGroupPOs);
    }
    
    @Override
    public List<UserGroup> findByPage(int pageNum, int pageSize, String groupName) {
        logger.debug("Finding user groups by page: pageNum={}, pageSize={}, groupName={}", 
                    pageNum, pageSize, groupName);
        
        int offset = (pageNum - 1) * pageSize;
        
        List<UserGroupPO> userGroupPOs = userGroupMapper.selectByPage(offset, pageSize, groupName);
        return PermissionConverter.INSTANCE.toUserGroupList(userGroupPOs);
    }
    
    @Override
    public long countUserGroups(String groupName) {
        logger.debug("Counting user groups: groupName={}", groupName);
        
        return userGroupMapper.countUserGroups(groupName);
    }
    
    @Override
    @Transactional
    public void delete(UserGroup userGroup) {
        logger.info("Deleting user group: {}", userGroup.getGroupCode());
        
        Long userGroupId = userGroup.getId();
        
        // 删除用户组成员关系
        userGroupMemberMapper.deleteByUserGroupId(userGroupId);
        
        // 删除用户组角色分配
        userGroupRoleMapper.deleteByUserGroupId(userGroupId);
        
        // 删除用户组
        userGroupMapper.deleteById(userGroupId);
        
        logger.info("User group deleted successfully: {}", userGroup.getGroupCode());
    }
    
    @Override
    @Transactional
    public void deleteById(Long id) {
        logger.info("Deleting user group by id: {}", id);
        
        // 删除用户组成员关系
        userGroupMemberMapper.deleteByUserGroupId(id);
        
        // 删除用户组角色分配
        userGroupRoleMapper.deleteByUserGroupId(id);
        
        // 删除用户组
        userGroupMapper.deleteById(id);
        
        logger.info("User group deleted successfully with id: {}", id);
    }
    
    @Override
    @Transactional
    public void deleteByIds(List<Long> ids) {
        logger.info("Deleting user groups by ids: {}", ids);
        
        for (Long id : ids) {
            deleteById(id);
        }
        
        logger.info("User groups deleted successfully, count: {}", ids.size());
    }
    
    @Override
    public boolean existsByGroupCode(String groupCode) {
        UserGroupPO userGroupPO = userGroupMapper.selectByGroupCode(groupCode);
        return userGroupPO != null;
    }
    
    @Override
    public boolean existsByGroupName(String groupName) {
        UserGroupPO userGroupPO = userGroupMapper.selectByGroupName(groupName);
        return userGroupPO != null;
    }
    
    @Override
    public boolean existsByGroupCodeAndIdNot(String groupCode, Long excludeId) {
        return userGroupMapper.countByGroupCodeAndIdNot(groupCode, excludeId) > 0;
    }
    
    @Override
    public boolean existsByGroupNameAndIdNot(String groupName, Long excludeId) {
        return userGroupMapper.countByGroupNameAndIdNot(groupName, excludeId) > 0;
    }
    
    @Override
    public boolean existsById(Long id) {
        UserGroupPO userGroupPO = userGroupMapper.selectById(id);
        return userGroupPO != null;
    }
    
    @Override
    public List<UserGroup> findByRoleId(Long roleId) {
        logger.debug("Finding user groups by role id: {}", roleId);
        
        List<UserGroupPO> userGroupPOs = userGroupMapper.selectByRoleId(roleId);
        return PermissionConverter.INSTANCE.toUserGroupList(userGroupPOs);
    }
    
    @Override
    public List<UserGroup> findByUserId(Long userId) {
        logger.debug("Finding user groups by user id: {}", userId);
        
        List<UserGroupPO> userGroupPOs = userGroupMapper.selectByUserId(userId);
        return PermissionConverter.INSTANCE.toUserGroupList(userGroupPOs);
    }
    
    @Override
    public List<Long> findRoleIdsByUserGroupId(Long userGroupId) {
        logger.debug("Finding role ids by user group id: {}", userGroupId);
        
        return userGroupRoleMapper.selectRoleIdsByUserGroupId(userGroupId);
    }
    
    @Override
    public List<Long> findMemberIdsByUserGroupId(Long userGroupId) {
        logger.debug("Finding member ids by user group id: {}", userGroupId);
        
        return userGroupMemberMapper.selectUserIdsByUserGroupId(userGroupId);
    }
    
    @Override
    public boolean isMemberOfUserGroup(Long userId, Long userGroupId) {
        return userGroupMemberMapper.countByUserIdAndUserGroupId(userId, userGroupId) > 0;
    }
    
    @Override
    public boolean isRoleAssignedToUserGroup(Long roleId, Long userGroupId) {
        return userGroupRoleMapper.countByRoleIdAndUserGroupId(roleId, userGroupId) > 0;
    }

    /**
     * 保存用户组成员关系
     *
     * @param userGroupId 用户组ID
     * @param memberIds 成员ID列表
     */
    @Transactional
    private void saveUserGroupMembers(Long userGroupId, List<Long> memberIds) {
        // 先删除现有成员关系
        userGroupMemberMapper.deleteByUserGroupId(userGroupId);

        // 添加新的成员关系
        if (memberIds != null && !memberIds.isEmpty()) {
            for (Long memberId : memberIds) {
                userGroupMemberMapper.insertUserGroupMember(userGroupId, memberId);
            }
        }
    }

    /**
     * 保存用户组角色分配
     *
     * @param userGroupId 用户组ID
     * @param roleIds 角色ID列表
     */
    @Transactional
    private void saveUserGroupRoles(Long userGroupId, List<Long> roleIds) {
        // 先删除现有角色分配
        userGroupRoleMapper.deleteByUserGroupId(userGroupId);

        // 添加新的角色分配
        if (roleIds != null && !roleIds.isEmpty()) {
            for (Long roleId : roleIds) {
                userGroupRoleMapper.insertUserGroupRole(userGroupId, roleId);
            }
        }
    }

    /**
     * 加载用户组详细信息（成员和角色分配）
     *
     * @param userGroup 用户组实体
     */
    private void loadUserGroupDetails(UserGroup userGroup) {
        if (userGroup.getId() != null) {
            // 加载用户组成员
            List<Long> memberIds = userGroupMemberMapper.selectUserIdsByUserGroupId(userGroup.getId());
            for (Long memberId : memberIds) {
                userGroup.addMember(memberId);
            }

            // 加载用户组角色分配
            List<Long> roleIds = userGroupRoleMapper.selectRoleIdsByUserGroupId(userGroup.getId());
            for (Long roleId : roleIds) {
                userGroup.assignRole(roleId);
            }
        }
    }
}
