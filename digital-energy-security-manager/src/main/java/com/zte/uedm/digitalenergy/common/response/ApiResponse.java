package com.zte.uedm.digitalenergy.common.response;

import java.time.LocalDateTime;

/**
 * 统一API响应包装类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class ApiResponse<T> {
    
    /**
     * 响应状态码
     */
    private Integer code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 响应时间戳
     */
    private LocalDateTime timestamp;
    
    /**
     * 默认构造函数
     */
    public ApiResponse() {
        this.timestamp = LocalDateTime.now();
    }
    
    /**
     * 构造函数
     * 
     * @param code 状态码
     * @param message 消息
     * @param data 数据
     */
    public ApiResponse(Integer code, String message, T data) {
        this();
        this.code = code;
        this.message = message;
        this.data = data;
    }
    
    /**
     * 成功响应（无数据）
     * 
     * @param <T> 数据类型
     * @return 响应对象
     */
    public static <T> ApiResponse<T> success() {
        return new ApiResponse<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), null);
    }
    
    /**
     * 成功响应（带数据）
     * 
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 响应对象
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(ResultCode.SUCCESS.getCode(), ResultCode.SUCCESS.getMessage(), data);
    }
    
    /**
     * 成功响应（自定义消息）
     * 
     * @param message 响应消息
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 响应对象
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(ResultCode.SUCCESS.getCode(), message, data);
    }
    
    /**
     * 失败响应
     * 
     * @param <T> 数据类型
     * @return 响应对象
     */
    public static <T> ApiResponse<T> error() {
        return new ApiResponse<>(ResultCode.INTERNAL_SERVER_ERROR.getCode(), 
                                ResultCode.INTERNAL_SERVER_ERROR.getMessage(), null);
    }
    
    /**
     * 失败响应（自定义消息）
     * 
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 响应对象
     */
    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>(ResultCode.INTERNAL_SERVER_ERROR.getCode(), message, null);
    }
    
    /**
     * 失败响应（自定义状态码和消息）
     * 
     * @param code 状态码
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 响应对象
     */
    public static <T> ApiResponse<T> error(Integer code, String message) {
        return new ApiResponse<>(code, message, null);
    }
    
    /**
     * 失败响应（使用结果码枚举）
     * 
     * @param resultCode 结果码枚举
     * @param <T> 数据类型
     * @return 响应对象
     */
    public static <T> ApiResponse<T> error(ResultCode resultCode) {
        return new ApiResponse<>(resultCode.getCode(), resultCode.getMessage(), null);
    }
    
    /**
     * 参数错误响应
     * 
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 响应对象
     */
    public static <T> ApiResponse<T> badRequest(String message) {
        return new ApiResponse<>(ResultCode.BAD_REQUEST.getCode(), message, null);
    }
    
    /**
     * 未授权响应
     * 
     * @param <T> 数据类型
     * @return 响应对象
     */
    public static <T> ApiResponse<T> unauthorized() {
        return new ApiResponse<>(ResultCode.UNAUTHORIZED.getCode(), 
                                ResultCode.UNAUTHORIZED.getMessage(), null);
    }
    
    /**
     * 禁止访问响应
     * 
     * @param <T> 数据类型
     * @return 响应对象
     */
    public static <T> ApiResponse<T> forbidden() {
        return new ApiResponse<>(ResultCode.FORBIDDEN.getCode(), 
                                ResultCode.FORBIDDEN.getMessage(), null);
    }
    
    /**
     * 资源未找到响应
     * 
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 响应对象
     */
    public static <T> ApiResponse<T> notFound(String message) {
        return new ApiResponse<>(ResultCode.NOT_FOUND.getCode(), message, null);
    }
    
    /**
     * 判断是否成功
     * 
     * @return true-成功，false-失败
     */
    public boolean isSuccess() {
        return ResultCode.SUCCESS.getCode().equals(this.code);
    }
    
    /**
     * 判断是否失败
     * 
     * @return true-失败，false-成功
     */
    public boolean isError() {
        return !isSuccess();
    }
    
    // Getters and Setters
    public Integer getCode() {
        return code;
    }
    
    public void setCode(Integer code) {
        this.code = code;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public T getData() {
        return data;
    }
    
    public void setData(T data) {
        this.data = data;
    }
    
    public LocalDateTime getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
    }
    
    @Override
    public String toString() {
        return "ApiResponse{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", timestamp=" + timestamp +
                '}';
    }
}
