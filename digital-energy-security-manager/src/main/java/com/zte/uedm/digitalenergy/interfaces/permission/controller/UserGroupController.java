package com.zte.uedm.digitalenergy.interfaces.permission.controller;

import com.zte.uedm.digitalenergy.application.permission.dto.PageQueryDTO;
import com.zte.uedm.digitalenergy.application.permission.dto.PageResultDTO;
import com.zte.uedm.digitalenergy.application.permission.dto.UserDTO;
import com.zte.uedm.digitalenergy.application.permission.dto.UserGroupDTO;
import com.zte.uedm.digitalenergy.application.permission.service.UserGroupApplicationService;
import com.zte.uedm.digitalenergy.common.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 用户组管理控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Tag(name = "用户组管理", description = "用户组管理相关接口")
@RestController
@RequestMapping("/api/permission/user-groups")
@Validated
public class UserGroupController {
    
    private static final Logger logger = LoggerFactory.getLogger(UserGroupController.class);
    
    @Autowired
    private UserGroupApplicationService userGroupApplicationService;
    
    /**
     * 创建用户组
     * 
     * @param userGroupDTO 用户组信息
     * @param request HTTP请求
     * @return 创建的用户组信息
     */
    @Operation(summary = "创建用户组", description = "创建新的用户组")
    @PostMapping
    public ApiResponse<UserGroupDTO> createUserGroup(
            @Valid @RequestBody UserGroupDTO userGroupDTO,
            HttpServletRequest request) {
        
        logger.info("Creating user group: {}", userGroupDTO.getGroupCode());
        
        String createBy = getCurrentUser(request);
        UserGroupDTO result = userGroupApplicationService.createUserGroup(userGroupDTO, createBy);
        
        logger.info("User group created successfully with id: {}", result.getId());
        
        return ApiResponse.success("用户组创建成功", result);
    }
    
    /**
     * 更新用户组
     * 
     * @param id 用户组ID
     * @param userGroupDTO 用户组信息
     * @param request HTTP请求
     * @return 更新的用户组信息
     */
    @Operation(summary = "更新用户组", description = "更新指定用户组信息")
    @PutMapping("/{id}")
    public ApiResponse<UserGroupDTO> updateUserGroup(
            @Parameter(description = "用户组ID") @PathVariable @NotNull Long id,
            @Valid @RequestBody UserGroupDTO userGroupDTO,
            HttpServletRequest request) {
        
        logger.info("Updating user group: {}", id);
        
        String updateBy = getCurrentUser(request);
        UserGroupDTO result = userGroupApplicationService.updateUserGroup(id, userGroupDTO, updateBy);
        
        logger.info("User group updated successfully: {}", id);
        
        return ApiResponse.success("用户组更新成功", result);
    }
    
    /**
     * 删除用户组
     *
     * @param id 用户组ID
     * @return 删除结果
     */
    @Operation(summary = "删除用户组", description = "删除指定用户组")
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteUserGroup(
            @Parameter(description = "用户组ID") @PathVariable @NotNull Long id) {
        
        logger.info("Deleting user group: {}", id);
        
        userGroupApplicationService.deleteUserGroup(id);
        
        logger.info("User group deleted successfully: {}", id);
        
        return ApiResponse.success("用户组删除成功");
    }
    
    /**
     * 批量删除用户组
     *
     * @param ids 用户组ID列表
     * @return 删除结果
     */
    @Operation(summary = "批量删除用户组", description = "批量删除指定用户组")
    @DeleteMapping("/batch")
    public ApiResponse<String> deleteUserGroups(
            @Parameter(description = "用户组ID列表") @RequestBody @NotEmpty List<Long> ids) {
        
        logger.info("Deleting user groups: {}", ids);
        
        userGroupApplicationService.deleteUserGroups(ids);
        
        logger.info("User groups deleted successfully, count: {}", ids.size());
        
        return ApiResponse.success("用户组批量删除成功");
    }
    
    /**
     * 根据ID查询用户组
     * 
     * @param id 用户组ID
     * @return 用户组信息
     */
    @Operation(summary = "查询用户组详情", description = "根据ID查询用户组详细信息")
    @GetMapping("/{id}")
    public ApiResponse<UserGroupDTO> getUserGroupById(
            @Parameter(description = "用户组ID") @PathVariable @NotNull Long id) {
        
        logger.debug("Getting user group by id: {}", id);
        
        UserGroupDTO result = userGroupApplicationService.getUserGroupById(id);
        
        return ApiResponse.success(result);
    }
    
    /**
     * 根据用户组编码查询用户组
     * 
     * @param groupCode 用户组编码
     * @return 用户组信息
     */
    @Operation(summary = "根据编码查询用户组", description = "根据用户组编码查询用户组信息")
    @GetMapping("/code/{groupCode}")
    public ApiResponse<UserGroupDTO> getUserGroupByCode(
            @Parameter(description = "用户组编码") @PathVariable @NotNull String groupCode) {
        
        logger.debug("Getting user group by code: {}", groupCode);
        
        UserGroupDTO result = userGroupApplicationService.getUserGroupByCode(groupCode);
        
        return ApiResponse.success(result);
    }
    
    /**
     * 分页查询用户组列表
     * 
     * @param pageQuery 分页查询参数
     * @param groupName 用户组名称（可选）
     * @return 分页结果
     */
    @Operation(summary = "分页查询用户组", description = "分页查询用户组列表")
    @GetMapping("/page")
    public ApiResponse<PageResultDTO<UserGroupDTO>> getUserGroupsByPage(
            @Valid PageQueryDTO pageQuery,
            @Parameter(description = "用户组名称") @RequestParam(required = false) String groupName) {
        
        logger.debug("Getting user groups by page: pageNum={}, pageSize={}, groupName={}", 
                    pageQuery.getPageNum(), pageQuery.getPageSize(), groupName);
        
        PageResultDTO<UserGroupDTO> result = userGroupApplicationService.getUserGroupsByPage(pageQuery, groupName);
        
        return ApiResponse.success(result);
    }
    
    /**
     * 查询所有用户组
     * 
     * @return 用户组列表
     */
    @Operation(summary = "查询所有用户组", description = "查询系统中所有用户组")
    @GetMapping("/all")
    public ApiResponse<List<UserGroupDTO>> getAllUserGroups() {
        
        logger.debug("Getting all user groups");
        
        List<UserGroupDTO> result = userGroupApplicationService.getAllUserGroups();
        
        return ApiResponse.success(result);
    }
    
    /**
     * 查询用户组成员
     * 
     * @param id 用户组ID
     * @return 用户组成员列表
     */
    @Operation(summary = "查询用户组成员", description = "查询指定用户组的成员列表")
    @GetMapping("/{id}/members")
    public ApiResponse<List<UserDTO>> getUserGroupMembers(
            @Parameter(description = "用户组ID") @PathVariable @NotNull Long id) {
        
        logger.debug("Getting user group members for group: {}", id);
        
        List<UserDTO> result = userGroupApplicationService.getUserGroupMembers(id);
        
        return ApiResponse.success(result);
    }
    
    /**
     * 添加用户组成员
     * 
     * @param id 用户组ID
     * @param userIds 用户ID列表
     * @return 操作结果
     */
    @Operation(summary = "添加用户组成员", description = "向用户组添加成员")
    @PostMapping("/{id}/members")
    public ApiResponse<String> addMembers(
            @Parameter(description = "用户组ID") @PathVariable @NotNull Long id,
            @Parameter(description = "用户ID列表") @RequestBody @NotEmpty List<Long> userIds) {
        
        logger.info("Adding members to user group: {}, members: {}", id, userIds);
        
        userGroupApplicationService.addMembers(id, userIds);
        
        logger.info("Members added successfully to user group: {}", id);
        
        return ApiResponse.success("用户组成员添加成功");
    }
    
    /**
     * 移除用户组成员
     * 
     * @param id 用户组ID
     * @param userId 用户ID
     * @return 操作结果
     */
    @Operation(summary = "移除用户组成员", description = "从用户组移除指定成员")
    @DeleteMapping("/{id}/members/{userId}")
    public ApiResponse<String> removeMember(
            @Parameter(description = "用户组ID") @PathVariable @NotNull Long id,
            @Parameter(description = "用户ID") @PathVariable @NotNull Long userId) {
        
        logger.info("Removing member from user group: {}, member: {}", id, userId);
        
        userGroupApplicationService.removeMember(id, userId);
        
        logger.info("Member removed successfully from user group: {}", id);
        
        return ApiResponse.success("用户组成员移除成功");
    }
    
    /**
     * 为用户组分配角色
     * 
     * @param id 用户组ID
     * @param roleIds 角色ID列表
     * @return 操作结果
     */
    @Operation(summary = "分配用户组角色", description = "为用户组分配角色")
    @PostMapping("/{id}/roles")
    public ApiResponse<String> assignRoles(
            @Parameter(description = "用户组ID") @PathVariable @NotNull Long id,
            @Parameter(description = "角色ID列表") @RequestBody @NotEmpty List<Long> roleIds) {
        
        logger.info("Assigning roles to user group: {}, roles: {}", id, roleIds);
        
        userGroupApplicationService.assignRoles(id, roleIds);
        
        logger.info("Roles assigned successfully to user group: {}", id);
        
        return ApiResponse.success("用户组角色分配成功");
    }
    
    /**
     * 取消用户组角色分配
     * 
     * @param id 用户组ID
     * @param roleId 角色ID
     * @return 操作结果
     */
    @Operation(summary = "取消用户组角色", description = "取消用户组的角色分配")
    @DeleteMapping("/{id}/roles/{roleId}")
    public ApiResponse<String> unassignRole(
            @Parameter(description = "用户组ID") @PathVariable @NotNull Long id,
            @Parameter(description = "角色ID") @PathVariable @NotNull Long roleId) {
        
        logger.info("Unassigning role from user group: {}, role: {}", id, roleId);
        
        userGroupApplicationService.unassignRole(id, roleId);
        
        logger.info("Role unassigned successfully from user group: {}", id);
        
        return ApiResponse.success("用户组角色取消成功");
    }
    
    /**
     * 获取当前用户
     * 
     * @param request HTTP请求
     * @return 当前用户
     */
    private String getCurrentUser(HttpServletRequest request) {
        // 这里应该从Security Context或JWT Token中获取当前用户
        // 暂时返回固定值，实际项目中需要实现
        String user = request.getHeader("X-User-Code");
        return user != null ? user : "system";
    }
}
