package com.zte.uedm.digitalenergy.application.permission.service;

import com.zte.uedm.digitalenergy.application.permission.dto.PageQueryDTO;
import com.zte.uedm.digitalenergy.application.permission.dto.PageResultDTO;
import com.zte.uedm.digitalenergy.application.permission.dto.RoleDTO;
import com.zte.uedm.digitalenergy.application.permission.assembler.PermissionAssembler;
import com.zte.uedm.digitalenergy.common.exception.PermissionException;
import com.zte.uedm.digitalenergy.common.response.ResultCode;
import com.zte.uedm.digitalenergy.domain.permission.aggregate.RoleAggregate;
import com.zte.uedm.digitalenergy.domain.permission.entity.Role;
import com.zte.uedm.digitalenergy.domain.permission.repository.RoleRepository;
import com.zte.uedm.digitalenergy.domain.permission.service.PermissionDomainService;
import com.zte.uedm.digitalenergy.domain.permission.valueobject.RoleType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;

/**
 * 角色应用服务
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class RoleApplicationService {
    
    private static final Logger logger = LoggerFactory.getLogger(RoleApplicationService.class);
    
    @Autowired
    private RoleRepository roleRepository;
    
    @Autowired
    private PermissionDomainService permissionDomainService;
    
    @Autowired
    private PermissionAssembler permissionAssembler;
    
    /**
     * 创建角色
     * 
     * @param roleDTO 角色DTO
     * @param createBy 创建人
     * @return 角色DTO
     */
    @Transactional
    public RoleDTO createRole(RoleDTO roleDTO, String createBy) {
        logger.info("Creating role: {}", roleDTO.getRoleCode());
        
        // 验证角色编码和名称唯一性
        validateRoleUniqueness(roleDTO.getRoleCode(), roleDTO.getRoleName(), null);
        
        // 检查自定义角色数量限制
        validateCustomRoleLimit();
        
        // 创建角色聚合
        RoleAggregate roleAggregate = RoleAggregate.createCustomRole(
                roleDTO.getRoleName(),
                roleDTO.getRoleCode(),
                roleDTO.getRoleDescription(),
                createBy
        );
        
        // 分配菜单权限
        if (roleDTO.getMenuIds() != null && !roleDTO.getMenuIds().isEmpty()) {
            roleAggregate.assignMenuPermissions(roleDTO.getMenuIds());
        }
        
        // 保存角色
        RoleAggregate savedAggregate = roleRepository.save(roleAggregate);
        
        logger.info("Role created successfully with id: {}", savedAggregate.getId());
        
        return permissionAssembler.toRoleDTO(savedAggregate.getRole());
    }
    
    /**
     * 更新角色
     * 
     * @param id 角色ID
     * @param roleDTO 角色DTO
     * @param updateBy 更新人
     * @return 角色DTO
     */
    @Transactional
    public RoleDTO updateRole(Long id, RoleDTO roleDTO, String updateBy) {
        logger.info("Updating role: {}", id);
        
        // 查找角色
        RoleAggregate roleAggregate = findRoleAggregateById(id);
        
        // 验证角色名称唯一性（排除当前角色）
        if (!roleAggregate.getRoleName().equals(roleDTO.getRoleName())) {
            if (roleRepository.existsByRoleNameAndIdNot(roleDTO.getRoleName(), id)) {
                throw PermissionException.roleAlreadyExists(roleDTO.getRoleName());
            }
        }
        
        // 更新角色信息
        roleAggregate.updateRole(roleDTO.getRoleDescription(), updateBy);
        
        // 更新菜单权限
        if (roleDTO.getMenuIds() != null) {
            roleAggregate.assignMenuPermissions(roleDTO.getMenuIds());
        }
        
        // 保存角色
        RoleAggregate savedAggregate = roleRepository.save(roleAggregate);
        
        logger.info("Role updated successfully: {}", id);
        
        return permissionAssembler.toRoleDTO(savedAggregate.getRole());
    }
    
    /**
     * 删除角色
     * 
     * @param id 角色ID
     */
    @Transactional
    public void deleteRole(Long id) {
        logger.info("Deleting role: {}", id);
        
        // 查找角色
        RoleAggregate roleAggregate = findRoleAggregateById(id);
        
        // 验证角色是否可删除
        roleAggregate.validateDeletable();
        
        // 检查是否有关联的用户或用户组
        if (!permissionDomainService.canDeleteRole(id)) {
            throw new PermissionException(ResultCode.ROLE_CANNOT_DELETE.getCode(),
                    "Cannot delete role because it is associated with users or user groups");
        }
        
        // 删除角色
        roleRepository.delete(roleAggregate);
        
        logger.info("Role deleted successfully: {}", id);
    }
    
    /**
     * 批量删除角色
     * 
     * @param ids 角色ID列表
     */
    @Transactional
    public void deleteRoles(List<Long> ids) {
        logger.info("Deleting roles: {}", ids);
        
        for (Long id : ids) {
            deleteRole(id);
        }
        
        logger.info("Roles deleted successfully, count: {}", ids.size());
    }
    
    /**
     * 根据ID查询角色
     * 
     * @param id 角色ID
     * @return 角色DTO
     */
    public RoleDTO getRoleById(Long id) {
        logger.debug("Getting role by id: {}", id);
        
        RoleAggregate roleAggregate = findRoleAggregateById(id);
        return permissionAssembler.toRoleDTO(roleAggregate.getRole());
    }
    
    /**
     * 根据角色编码查询角色
     * 
     * @param roleCode 角色编码
     * @return 角色DTO
     */
    public RoleDTO getRoleByCode(String roleCode) {
        logger.debug("Getting role by code: {}", roleCode);
        
        Optional<RoleAggregate> optional = roleRepository.findByRoleCode(roleCode);
        if (optional.isPresent()) {
            throw PermissionException.roleNotFound(null);
        }
        
        return permissionAssembler.toRoleDTO(optional.get().getRole());
    }
    
    /**
     * 分页查询角色列表
     * 
     * @param pageQuery 分页查询参数
     * @param roleName 角色名称（可选）
     * @param roleType 角色类型（可选）
     * @return 分页结果
     */
    public PageResultDTO<RoleDTO> getRolesByPage(PageQueryDTO pageQuery, String roleName, String roleType) {
        logger.debug("Getting roles by page: pageNum={}, pageSize={}, roleName={}, roleType={}", 
                    pageQuery.getPageNum(), pageQuery.getPageSize(), roleName, roleType);
        
        RoleType roleTypeEnum = StringUtils.hasText(roleType) ? RoleType.fromCode(roleType) : null;
        
        // 查询角色列表
        List<Role> roles = roleRepository.findByPage(
                pageQuery.getPageNum(), 
                pageQuery.getPageSize(), 
                roleName, 
                roleTypeEnum
        );
        
        // 统计总数
        long total = roleRepository.countRoles(roleName, roleTypeEnum);
        
        // 转换为DTO
        List<RoleDTO> roleDTOs = permissionAssembler.toRoleDTOList(roles);
        
        return new PageResultDTO<>(pageQuery.getPageNum(), pageQuery.getPageSize(), total, roleDTOs);
    }
    
    /**
     * 查询所有角色
     * 
     * @return 角色DTO列表
     */
    public List<RoleDTO> getAllRoles() {
        logger.debug("Getting all roles");
        
        List<Role> roles = roleRepository.findAll();
        return permissionAssembler.toRoleDTOList(roles);
    }
    
    /**
     * 根据角色类型查询角色列表
     * 
     * @param roleType 角色类型
     * @return 角色DTO列表
     */
    public List<RoleDTO> getRolesByType(String roleType) {
        logger.debug("Getting roles by type: {}", roleType);
        
        RoleType roleTypeEnum = RoleType.fromCode(roleType);
        List<Role> roles = roleRepository.findByRoleType(roleTypeEnum);
        return permissionAssembler.toRoleDTOList(roles);
    }
    
    /**
     * 查找角色聚合根
     * 
     * @param id 角色ID
     * @return 角色聚合根
     */
    private RoleAggregate findRoleAggregateById(Long id) {
        Optional<RoleAggregate> optional = roleRepository.findById(id);
        if (optional.isPresent()) {
            throw PermissionException.roleNotFound(id);
        }
        return optional.get();
    }
    
    /**
     * 验证角色唯一性
     * 
     * @param roleCode 角色编码
     * @param roleName 角色名称
     * @param excludeId 排除的角色ID
     */
    private void validateRoleUniqueness(String roleCode, String roleName, Long excludeId) {
        if (excludeId == null) {
            // 新增时检查
            if (roleRepository.existsByRoleCode(roleCode)) {
                throw PermissionException.roleAlreadyExists(roleCode);
            }
            if (roleRepository.existsByRoleName(roleName)) {
                throw PermissionException.roleAlreadyExists(roleName);
            }
        } else {
            // 更新时检查
            if (roleRepository.existsByRoleCodeAndIdNot(roleCode, excludeId)) {
                throw PermissionException.roleAlreadyExists(roleCode);
            }
            if (roleRepository.existsByRoleNameAndIdNot(roleName, excludeId)) {
                throw PermissionException.roleAlreadyExists(roleName);
            }
        }
    }
    
    /**
     * 验证自定义角色数量限制
     */
    private void validateCustomRoleLimit() {
        long customRoleCount = roleRepository.countCustomRoles();
        if (customRoleCount >= RoleAggregate.MAX_CUSTOM_ROLE_COUNT) {
            throw PermissionException.roleCountLimitExceeded(RoleAggregate.MAX_CUSTOM_ROLE_COUNT);
        }
    }
}
