package com.zte.uedm.digitalenergy.domain.permission.entity;

import com.zte.uedm.digitalenergy.domain.permission.valueobject.UserStatus;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 用户组实体
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class UserGroup {
    
    /**
     * 用户组角色分配最大数量限制
     */
    public static final int MAX_ROLE_COUNT = 10;
    
    /**
     * 用户组最大数量限制
     */
    public static final int MAX_USER_GROUP_COUNT = 1000;
    
    /**
     * 用户组ID
     */
    private Long id;
    
    /**
     * 用户组名称
     */
    private String groupName;
    
    /**
     * 用户组编码
     */
    private String groupCode;
    
    /**
     * 用户组描述
     */
    private String groupDescription;
    
    /**
     * 状态
     */
    private UserStatus status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 用户组成员列表
     */
    private List<UserGroupMember> members;
    
    /**
     * 用户组角色分配列表
     */
    private List<UserGroupRoleAssignment> roleAssignments;
    
    /**
     * 默认构造函数
     */
    public UserGroup() {
        this.members = new ArrayList<>();
        this.roleAssignments = new ArrayList<>();
    }
    
    /**
     * 构造函数
     * 
     * @param groupName 用户组名称
     * @param groupCode 用户组编码
     * @param groupDescription 用户组描述
     */
    public UserGroup(String groupName, String groupCode, String groupDescription) {
        this();
        this.setGroupName(groupName);
        this.setGroupCode(groupCode);
        this.groupDescription = groupDescription;
        this.status = UserStatus.ENABLED;
        this.createTime = LocalDateTime.now();
    }
    
    /**
     * 设置用户组名称
     * 
     * @param groupName 用户组名称
     */
    public void setGroupName(String groupName) {
        if (groupName == null || groupName.trim().isEmpty()) {
            throw new IllegalArgumentException("Group name cannot be null or empty");
        }
        if (groupName.length() > 50) {
            throw new IllegalArgumentException("Group name cannot exceed 50 characters");
        }
        this.groupName = groupName.trim();
    }
    
    /**
     * 设置用户组编码
     * 
     * @param groupCode 用户组编码
     */
    public void setGroupCode(String groupCode) {
        if (groupCode == null || groupCode.trim().isEmpty()) {
            throw new IllegalArgumentException("Group code cannot be null or empty");
        }
        this.groupCode = groupCode.trim();
    }
    
    /**
     * 添加成员
     * 
     * @param userId 用户ID
     */
    public void addMember(Long userId) {
        if (userId == null) {
            throw new IllegalArgumentException("User ID cannot be null");
        }
        
        // 检查是否已存在
        boolean exists = members.stream()
                .anyMatch(member -> member.getUserId().equals(userId));
        
        if (!exists) {
            UserGroupMember member = new UserGroupMember(this.id, userId);
            members.add(member);
        }
    }
    
    /**
     * 移除成员
     * 
     * @param userId 用户ID
     */
    public void removeMember(Long userId) {
        if (userId == null) {
            return;
        }
        
        members.removeIf(member -> member.getUserId().equals(userId));
    }
    
    /**
     * 批量设置成员
     *
     * @param userIds 用户ID列表
     */
    public void setMembers(List<Long> userIds) {
        if (userIds == null) {
            throw new IllegalArgumentException("User IDs cannot be null");
        }

        this.members.clear();
        for (Long userId : userIds) {
            addMember(userId);
        }
    }

    /**
     * 清除所有成员
     */
    public void clearMembers() {
        this.members.clear();
    }
    
    /**
     * 分配角色
     * 
     * @param roleId 角色ID
     */
    public void assignRole(Long roleId) {
        if (roleId == null) {
            throw new IllegalArgumentException("Role ID cannot be null");
        }
        
        // 检查角色数量限制
        if (roleAssignments.size() >= MAX_ROLE_COUNT) {
            throw new IllegalStateException("User group cannot have more than " + MAX_ROLE_COUNT + " roles");
        }
        
        // 检查是否已分配
        boolean exists = roleAssignments.stream()
                .anyMatch(assignment -> assignment.getRoleId().equals(roleId));
        
        if (!exists) {
            UserGroupRoleAssignment assignment = new UserGroupRoleAssignment(this.id, roleId);
            roleAssignments.add(assignment);
        }
    }
    
    /**
     * 取消角色分配
     * 
     * @param roleId 角色ID
     */
    public void unassignRole(Long roleId) {
        if (roleId == null) {
            return;
        }
        
        roleAssignments.removeIf(assignment -> assignment.getRoleId().equals(roleId));
    }
    
    /**
     * 批量设置角色
     *
     * @param roleIds 角色ID列表
     */
    public void setRoles(List<Long> roleIds) {
        if (roleIds == null) {
            throw new IllegalArgumentException("Role IDs cannot be null");
        }

        if (roleIds.size() > MAX_ROLE_COUNT) {
            throw new IllegalStateException("User group cannot have more than " + MAX_ROLE_COUNT + " roles");
        }

        this.roleAssignments.clear();
        for (Long roleId : roleIds) {
            assignRole(roleId);
        }
    }

    /**
     * 清除所有角色分配
     */
    public void clearRoles() {
        this.roleAssignments.clear();
    }
    
    /**
     * 获取用户组分配的角色ID列表
     * 
     * @return 角色ID列表
     */
    public List<Long> getRoleIds() {
        return roleAssignments.stream()
                .map(UserGroupRoleAssignment::getRoleId)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
    
    /**
     * 获取用户组成员ID列表
     * 
     * @return 用户ID列表
     */
    public List<Long> getMemberIds() {
        return members.stream()
                .map(UserGroupMember::getUserId)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
    
    /**
     * 检查用户组是否有指定角色
     * 
     * @param roleId 角色ID
     * @return true-有该角色，false-没有该角色
     */
    public boolean hasRole(Long roleId) {
        if (roleId == null) {
            return false;
        }
        
        return roleAssignments.stream()
                .anyMatch(assignment -> assignment.getRoleId().equals(roleId));
    }
    
    /**
     * 检查用户是否为组成员
     * 
     * @param userId 用户ID
     * @return true-是成员，false-不是成员
     */
    public boolean hasMember(Long userId) {
        if (userId == null) {
            return false;
        }
        
        return members.stream()
                .anyMatch(member -> member.getUserId().equals(userId));
    }
    
    /**
     * 更新用户组信息
     * 
     * @param groupDescription 用户组描述
     * @param updateBy 更新人
     */
    public void updateUserGroup(String groupDescription, String updateBy) {
        this.groupDescription = groupDescription;
        this.updateBy = updateBy;
        this.updateTime = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getGroupName() {
        return groupName;
    }
    
    public String getGroupCode() {
        return groupCode;
    }
    
    public String getGroupDescription() {
        return groupDescription;
    }
    
    public void setGroupDescription(String groupDescription) {
        this.groupDescription = groupDescription;
    }
    
    public UserStatus getStatus() {
        return status;
    }
    
    public void setStatus(UserStatus status) {
        this.status = status;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public String getCreateBy() {
        return createBy;
    }
    
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    public String getUpdateBy() {
        return updateBy;
    }
    
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    
    public List<UserGroupMember> getMembers() {
        return new ArrayList<>(members);
    }
    
    public List<UserGroupRoleAssignment> getRoleAssignments() {
        return new ArrayList<>(roleAssignments);
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserGroup userGroup = (UserGroup) o;
        return Objects.equals(id, userGroup.id) &&
               Objects.equals(groupCode, userGroup.groupCode);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id, groupCode);
    }
    
    @Override
    public String toString() {
        return "UserGroup{" +
                "id=" + id +
                ", groupName='" + groupName + '\'' +
                ", groupCode='" + groupCode + '\'' +
                ", status=" + status +
                ", memberCount=" + members.size() +
                ", roleCount=" + roleAssignments.size() +
                '}';
    }
}
