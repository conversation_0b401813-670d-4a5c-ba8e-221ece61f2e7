package com.zte.uedm.digitalenergy.domain.permission.entity;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 角色菜单权限实体
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class RoleMenuPermission {
    
    /**
     * 权限ID
     */
    private Long id;
    
    /**
     * 角色ID
     */
    private Long roleId;
    
    /**
     * 菜单ID
     */
    private Long menuId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 默认构造函数
     */
    public RoleMenuPermission() {
    }
    
    /**
     * 构造函数
     * 
     * @param roleId 角色ID
     * @param menuId 菜单ID
     */
    public RoleMenuPermission(Long roleId, Long menuId) {
        if (roleId == null) {
            throw new IllegalArgumentException("Role ID cannot be null");
        }
        if (menuId == null) {
            throw new IllegalArgumentException("Menu ID cannot be null");
        }
        
        this.roleId = roleId;
        this.menuId = menuId;
        this.createTime = LocalDateTime.now();
    }
    
    /**
     * 检查权限是否有效
     * 
     * @return true-有效，false-无效
     */
    public boolean isValid() {
        return roleId != null && menuId != null;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getRoleId() {
        return roleId;
    }
    
    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }
    
    public Long getMenuId() {
        return menuId;
    }
    
    public void setMenuId(Long menuId) {
        this.menuId = menuId;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RoleMenuPermission that = (RoleMenuPermission) o;
        return Objects.equals(roleId, that.roleId) &&
               Objects.equals(menuId, that.menuId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(roleId, menuId);
    }
    
    @Override
    public String toString() {
        return "RoleMenuPermission{" +
                "id=" + id +
                ", roleId=" + roleId +
                ", menuId=" + menuId +
                ", createTime=" + createTime +
                '}';
    }
}
