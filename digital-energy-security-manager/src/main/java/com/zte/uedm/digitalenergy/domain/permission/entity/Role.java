package com.zte.uedm.digitalenergy.domain.permission.entity;

import com.zte.uedm.digitalenergy.domain.permission.valueobject.RoleType;
import com.zte.uedm.digitalenergy.domain.permission.valueobject.UserStatus;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 角色实体
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class Role {
    
    /**
     * 角色ID
     */
    private Long id;
    
    /**
     * 角色名称
     */
    private String roleName;
    
    /**
     * 角色编码
     */
    private String roleCode;
    
    /**
     * 角色描述
     */
    private String roleDescription;
    
    /**
     * 角色类型
     */
    private RoleType roleType;
    
    /**
     * 是否默认角色
     */
    private Boolean isDefault;
    
    /**
     * 状态
     */
    private UserStatus status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 角色关联的菜单权限列表
     */
    private List<RoleMenuPermission> menuPermissions;
    
    /**
     * 默认构造函数
     */
    public Role() {
        this.menuPermissions = new ArrayList<>();
    }
    
    /**
     * 构造函数
     * 
     * @param roleName 角色名称
     * @param roleCode 角色编码
     * @param roleDescription 角色描述
     * @param roleType 角色类型
     */
    public Role(String roleName, String roleCode, String roleDescription, RoleType roleType) {
        this();
        this.setRoleName(roleName);
        this.setRoleCode(roleCode);
        this.roleDescription = roleDescription;
        this.roleType = roleType;
        this.isDefault = roleType.isDefault();
        this.status = UserStatus.ENABLED;
        this.createTime = LocalDateTime.now();
    }
    
    /**
     * 设置角色名称
     * 
     * @param roleName 角色名称
     */
    public void setRoleName(String roleName) {
        if (roleName == null || roleName.trim().isEmpty()) {
            throw new IllegalArgumentException("Role name cannot be null or empty");
        }
        if (roleName.length() > 20) {
            throw new IllegalArgumentException("Role name cannot exceed 20 characters");
        }
        this.roleName = roleName.trim();
    }
    
    /**
     * 设置角色编码
     * 
     * @param roleCode 角色编码
     */
    public void setRoleCode(String roleCode) {
        if (roleCode == null || roleCode.trim().isEmpty()) {
            throw new IllegalArgumentException("Role code cannot be null or empty");
        }
        this.roleCode = roleCode.trim();
    }
    
    /**
     * 添加菜单权限
     * 
     * @param menuId 菜单ID
     */
    public void addMenuPermission(Long menuId) {
        if (menuId == null) {
            throw new IllegalArgumentException("Menu ID cannot be null");
        }
        
        // 检查是否已存在
        boolean exists = menuPermissions.stream()
                .anyMatch(permission -> permission.getMenuId().equals(menuId));
        
        if (!exists) {
            RoleMenuPermission permission = new RoleMenuPermission(this.id, menuId);
            menuPermissions.add(permission);
        }
    }
    
    /**
     * 移除菜单权限
     * 
     * @param menuId 菜单ID
     */
    public void removeMenuPermission(Long menuId) {
        if (menuId == null) {
            return;
        }
        
        menuPermissions.removeIf(permission -> permission.getMenuId().equals(menuId));
    }
    
    /**
     * 批量设置菜单权限
     * 
     * @param menuIds 菜单ID列表
     */
    public void setMenuPermissions(List<Long> menuIds) {
        if (menuIds == null) {
            throw new IllegalArgumentException("Menu IDs cannot be null");
        }
        
        this.menuPermissions.clear();
        for (Long menuId : menuIds) {
            addMenuPermission(menuId);
        }
    }
    
    /**
     * 检查是否可删除
     * 
     * @return true-可删除，false-不可删除
     */
    public boolean isDeletable() {
        return roleType != null && roleType.isDeletable();
    }
    
    /**
     * 检查是否可修改角色名称
     * 
     * @return true-可修改，false-不可修改
     */
    public boolean isNameEditable() {
        return roleType != null && roleType.isDeletable();
    }
    
    /**
     * 更新角色信息
     * 
     * @param roleDescription 角色描述
     * @param updateBy 更新人
     */
    public void updateRole(String roleDescription, String updateBy) {
        this.roleDescription = roleDescription;
        this.updateBy = updateBy;
        this.updateTime = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getRoleName() {
        return roleName;
    }
    
    public String getRoleCode() {
        return roleCode;
    }
    
    public String getRoleDescription() {
        return roleDescription;
    }
    
    public void setRoleDescription(String roleDescription) {
        this.roleDescription = roleDescription;
    }
    
    public RoleType getRoleType() {
        return roleType;
    }
    
    public void setRoleType(RoleType roleType) {
        this.roleType = roleType;
    }
    
    public Boolean getIsDefault() {
        return isDefault;
    }
    
    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }
    
    public UserStatus getStatus() {
        return status;
    }
    
    public void setStatus(UserStatus status) {
        this.status = status;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public String getCreateBy() {
        return createBy;
    }
    
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    public String getUpdateBy() {
        return updateBy;
    }
    
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    
    public List<RoleMenuPermission> getMenuPermissions() {
        return new ArrayList<>(menuPermissions);
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Role role = (Role) o;
        return Objects.equals(id, role.id) &&
               Objects.equals(roleCode, role.roleCode);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id, roleCode);
    }
    
    @Override
    public String toString() {
        return "Role{" +
                "id=" + id +
                ", roleName='" + roleName + '\'' +
                ", roleCode='" + roleCode + '\'' +
                ", roleType=" + roleType +
                ", isDefault=" + isDefault +
                ", status=" + status +
                '}';
    }
}
