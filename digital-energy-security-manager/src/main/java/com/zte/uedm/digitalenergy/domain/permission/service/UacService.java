package com.zte.uedm.digitalenergy.domain.permission.service;

import com.zte.uedm.digitalenergy.application.permission.dto.UserValidationDTO;

/**
 * UAC用户验证服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface UacService {
    
    /**
     * 验证用户身份
     * 
     * @param userCode 用户工号
     * @return 用户验证结果
     */
    UserValidationDTO validateUser(String userCode);
    
    /**
     * 批量验证用户身份
     * 
     * @param userCodes 用户工号列表
     * @return 用户验证结果列表
     */
    java.util.List<UserValidationDTO> validateUsers(java.util.List<String> userCodes);
    
    /**
     * 检查用户是否存在
     * 
     * @param userCode 用户工号
     * @return true-存在，false-不存在
     */
    boolean userExists(String userCode);
    
    /**
     * 获取用户详细信息
     * 
     * @param userCode 用户工号
     * @return 用户详细信息
     */
    UserValidationDTO getUserDetails(String userCode);
}
