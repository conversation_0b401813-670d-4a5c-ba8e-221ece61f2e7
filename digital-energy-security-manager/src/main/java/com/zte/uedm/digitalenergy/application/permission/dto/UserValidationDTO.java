package com.zte.uedm.digitalenergy.application.permission.dto;

import lombok.Data;

/**
 * 用户验证数据传输对象
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class UserValidationDTO {
    
    /**
     * 用户工号
     */
    private String userCode;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 组织机构
     */
    private String organization;
    
    /**
     * 是否验证通过
     */
    private boolean valid;
    
    /**
     * 验证失败原因
     */
    private String failureReason;
    
    /**
     * 员工类型
     */
    private String employeeType;
    
    /**
     * 部门编码
     */
    private String departmentCode;
    
    /**
     * 部门名称
     */
    private String departmentName;
    
    /**
     * 邮箱
     */
    private String email;
    
    /**
     * 手机号
     */
    private String mobile;
    
    /**
     * 默认构造函数
     */
    public UserValidationDTO() {
    }
    
    /**
     * 构造函数
     * 
     * @param userCode 用户工号
     * @param username 用户名
     * @param organization 组织机构
     * @param valid 是否验证通过
     */
    public UserValidationDTO(String userCode, String username, String organization, boolean valid) {
        this.userCode = userCode;
        this.username = username;
        this.organization = organization;
        this.valid = valid;
    }
    
    /**
     * 创建验证成功的结果
     * 
     * @param userCode 用户工号
     * @param username 用户名
     * @param organization 组织机构
     * @return 验证结果
     */
    public static UserValidationDTO success(String userCode, String username, String organization) {
        return new UserValidationDTO(userCode, username, organization, true);
    }
    
    /**
     * 创建验证失败的结果
     * 
     * @param userCode 用户工号
     * @param failureReason 失败原因
     * @return 验证结果
     */
    public static UserValidationDTO failure(String userCode, String failureReason) {
        UserValidationDTO result = new UserValidationDTO();
        result.setUserCode(userCode);
        result.setValid(false);
        result.setFailureReason(failureReason);
        return result;
    }
}
