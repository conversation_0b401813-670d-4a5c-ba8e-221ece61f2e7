package com.zte.uedm.digitalenergy.infrastructure.permission.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.digitalenergy.infrastructure.permission.po.UserRolePO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户角色关联映射器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface UserRoleMapper extends BaseMapper<UserRolePO> {
    
    /**
     * 根据用户ID查询角色ID列表
     * 
     * @param userId 用户ID
     * @return 角色ID列表
     */
    @Select("SELECT role_id FROM t_user_role WHERE user_id = #{userId}")
    List<Long> selectRoleIdsByUserId(@Param("userId") Long userId);
    
    /**
     * 根据角色ID查询用户ID列表
     * 
     * @param roleId 角色ID
     * @return 用户ID列表
     */
    @Select("SELECT user_id FROM t_user_role WHERE role_id = #{roleId}")
    List<Long> selectUserIdsByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 根据用户ID删除用户角色关联
     * 
     * @param userId 用户ID
     * @return 删除数量
     */
    @Delete("DELETE FROM t_user_role WHERE user_id = #{userId}")
    int deleteByUserId(@Param("userId") Long userId);
    
    /**
     * 根据角色ID删除用户角色关联
     * 
     * @param roleId 角色ID
     * @return 删除数量
     */
    @Delete("DELETE FROM t_user_role WHERE role_id = #{roleId}")
    int deleteByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 根据用户ID和角色ID删除关联
     * 
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 删除数量
     */
    @Delete("DELETE FROM t_user_role WHERE user_id = #{userId} AND role_id = #{roleId}")
    int deleteByUserIdAndRoleId(@Param("userId") Long userId, @Param("roleId") Long roleId);
    
    /**
     * 批量插入用户角色关联
     * 
     * @param userRoleList 用户角色关联列表
     * @return 插入数量
     */
    int batchInsert(@Param("list") List<UserRolePO> userRoleList);
    
    /**
     * 检查用户角色关联是否存在
     * 
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 存在数量
     */
    @Select("SELECT COUNT(*) FROM t_user_role WHERE user_id = #{userId} AND role_id = #{roleId}")
    int countByUserIdAndRoleId(@Param("userId") Long userId, @Param("roleId") Long roleId);
    
    /**
     * 统计用户的角色数量
     * 
     * @param userId 用户ID
     * @return 角色数量
     */
    @Select("SELECT COUNT(*) FROM t_user_role WHERE user_id = #{userId}")
    int countRolesByUserId(@Param("userId") Long userId);
}
