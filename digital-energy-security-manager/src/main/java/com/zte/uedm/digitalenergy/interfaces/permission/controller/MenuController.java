package com.zte.uedm.digitalenergy.interfaces.permission.controller;

import com.zte.uedm.digitalenergy.application.permission.dto.MenuDTO;
import com.zte.uedm.digitalenergy.application.permission.service.MenuApplicationService;
import com.zte.uedm.digitalenergy.common.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 菜单管理控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Tag(name = "菜单管理", description = "菜单管理相关接口")
@RestController
@RequestMapping("/api/permission/menus")
@Validated
public class MenuController {
    
    private static final Logger logger = LoggerFactory.getLogger(MenuController.class);
    
    @Autowired
    private MenuApplicationService menuApplicationService;
    
    /**
     * 查询菜单树
     * 
     * @return 菜单树
     */
    @Operation(summary = "查询菜单树", description = "查询系统菜单树结构")
    @GetMapping("/tree")
    public ApiResponse<List<MenuDTO>> getMenuTree() {
        
        logger.debug("Getting menu tree");
        
        List<MenuDTO> result = menuApplicationService.getMenuTree();
        
        return ApiResponse.success(result);
    }
    
    /**
     * 根据用户ID查询用户菜单树
     * 
     * @param userId 用户ID
     * @return 用户菜单树
     */
    @Operation(summary = "查询用户菜单树", description = "根据用户ID查询用户有权限的菜单树")
    @GetMapping("/user/{userId}/tree")
    public ApiResponse<List<MenuDTO>> getUserMenuTree(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long userId) {
        
        logger.debug("Getting user menu tree for user: {}", userId);
        
        List<MenuDTO> result = menuApplicationService.getUserMenuTree(userId);
        
        return ApiResponse.success(result);
    }
    
    /**
     * 根据角色ID查询角色菜单列表
     * 
     * @param roleId 角色ID
     * @return 角色菜单列表
     */
    @Operation(summary = "查询角色菜单", description = "根据角色ID查询角色有权限的菜单列表")
    @GetMapping("/role/{roleId}")
    public ApiResponse<List<MenuDTO>> getRoleMenus(
            @Parameter(description = "角色ID") @PathVariable @NotNull Long roleId) {
        
        logger.debug("Getting role menus for role: {}", roleId);
        
        List<MenuDTO> result = menuApplicationService.getRoleMenus(roleId);
        
        return ApiResponse.success(result);
    }
    
    /**
     * 查询所有菜单
     * 
     * @return 菜单列表
     */
    @Operation(summary = "查询所有菜单", description = "查询系统中所有菜单")
    @GetMapping("/all")
    public ApiResponse<List<MenuDTO>> getAllMenus() {
        
        logger.debug("Getting all menus");
        
        List<MenuDTO> result = menuApplicationService.getAllMenus();
        
        return ApiResponse.success(result);
    }
    
    /**
     * 根据ID查询菜单
     * 
     * @param id 菜单ID
     * @return 菜单信息
     */
    @Operation(summary = "查询菜单详情", description = "根据ID查询菜单详细信息")
    @GetMapping("/{id}")
    public ApiResponse<MenuDTO> getMenuById(
            @Parameter(description = "菜单ID") @PathVariable @NotNull Long id) {
        
        logger.debug("Getting menu by id: {}", id);
        
        MenuDTO result = menuApplicationService.getMenuById(id);
        
        return ApiResponse.success(result);
    }
}
