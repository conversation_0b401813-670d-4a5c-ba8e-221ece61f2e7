package com.zte.uedm.digitalenergy.application.permission.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户数据传输对象
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class UserDTO {
    
    /**
     * 用户ID
     */
    private Long id;
    
    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Size(max = 50, message = "用户名不能超过50个字符")
    private String username;
    
    /**
     * 用户工号
     */
    @NotBlank(message = "用户工号不能为空")
    @Size(max = 50, message = "用户工号不能超过50个字符")
    private String userCode;
    
    /**
     * 组织机构
     */
    private String organization;
    
    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 直接分配的角色ID列表
     */
    private List<Long> roleIds;
    
    /**
     * 直接分配的角色列表（用于展示）
     */
    private List<RoleDTO> roles;
    
    /**
     * 所有角色ID列表（包括用户组继承）
     */
    private List<Long> allRoleIds;
    
    /**
     * 所有角色列表（包括用户组继承，用于展示）
     */
    private List<RoleDTO> allRoles;
    
    /**
     * 用户组ID列表
     */
    private List<Long> userGroupIds;
    
    /**
     * 用户组列表（用于展示）
     */
    private List<UserGroupDTO> userGroups;
    
    /**
     * 用户菜单权限列表
     */
    private List<MenuDTO> menus;
    
    /**
     * 权限继承路径信息
     */
    private PermissionInheritanceDTO permissionInheritance;
}
