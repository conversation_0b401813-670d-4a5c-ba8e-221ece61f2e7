package com.zte.uedm.digitalenergy.application.permission.dto;

import lombok.Data;

import java.util.List;

/**
 * 权限继承路径数据传输对象
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class PermissionInheritanceDTO {
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 直接分配的角色ID列表
     */
    private List<Long> directRoleIds;
    
    /**
     * 直接分配的角色列表
     */
    private List<RoleDTO> directRoles;
    
    /**
     * 通过用户组继承的角色ID列表
     */
    private List<Long> groupRoleIds;
    
    /**
     * 通过用户组继承的角色列表
     */
    private List<RoleDTO> groupRoles;
    
    /**
     * 用户所属的用户组ID列表
     */
    private List<Long> userGroupIds;
    
    /**
     * 用户所属的用户组列表
     */
    private List<UserGroupDTO> userGroups;
    
    /**
     * 所有角色ID列表（直接 + 继承）
     */
    private List<Long> allRoleIds;
    
    /**
     * 所有角色列表（直接 + 继承）
     */
    private List<RoleDTO> allRoles;
    
    /**
     * 权限继承详情列表
     */
    private List<PermissionInheritanceDetailDTO> inheritanceDetails;
    
    /**
     * 权限继承详情
     */
    @Data
    public static class PermissionInheritanceDetailDTO {
        
        /**
         * 权限来源类型：DIRECT-直接分配，GROUP-用户组继承
         */
        private String sourceType;
        
        /**
         * 来源名称（角色名称或用户组名称）
         */
        private String sourceName;
        
        /**
         * 角色ID
         */
        private Long roleId;
        
        /**
         * 角色名称
         */
        private String roleName;
        
        /**
         * 用户组ID（如果是用户组继承）
         */
        private Long userGroupId;
        
        /**
         * 用户组名称（如果是用户组继承）
         */
        private String userGroupName;
        
        /**
         * 菜单权限列表
         */
        private List<MenuDTO> menus;
    }
}
