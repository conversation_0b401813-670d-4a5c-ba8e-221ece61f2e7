package com.zte.uedm.digitalenergy.domain.permission.valueobject;

/**
 * 角色类型值对象
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public enum RoleType {
    
    /**
     * 默认角色 - 系统预置，不可删除
     */
    DEFAULT("DEFAULT", "默认角色"),
    
    /**
     * 自定义角色 - 用户创建，可以删除
     */
    CUSTOM("CUSTOM", "自定义角色");
    
    private final String code;
    private final String description;
    
    RoleType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取角色类型
     * 
     * @param code 角色类型代码
     * @return 角色类型枚举
     */
    public static RoleType fromCode(String code) {
        for (RoleType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown role type code: " + code);
    }
    
    /**
     * 判断是否为默认角色
     * 
     * @return true-默认角色，false-自定义角色
     */
    public boolean isDefault() {
        return this == DEFAULT;
    }
    
    /**
     * 判断是否可删除
     * 
     * @return true-可删除，false-不可删除
     */
    public boolean isDeletable() {
        return this == CUSTOM;
    }
}
