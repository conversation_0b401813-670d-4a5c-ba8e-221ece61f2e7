package com.zte.uedm.digitalenergy;

import com.zte.oes.dexcloud.commons.annotation.EnableDexCloudAutoConfiguration;
import com.zte.oes.dexcloud.commons.component.configclient.annotation.EnableConfigClient;
import com.zte.oes.dexcloud.commons.component.msb.annotation.EnableMSB;
import com.zte.oes.dexcloud.commons.component.retrofit.annotation.EnableRetrofitRPC;
import com.zte.oes.dexcloud.i18n.api.annotation.EnableI18n;
import com.zte.oes.dexcloud.redis.redisson.mult.annotation.EnableRedissonMult;
import org.dexcloud.springboot.kafka.config.annotation.EnableKafka;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication
@EnableDexCloudAutoConfiguration
@EnableMSB
@EnableKafka
@EnableRetrofitRPC
@EnableRedissonMult
@EnableAsync
@EnableConfigClient
@EnableI18n
public class BootstrapApp
{
    public static void main(String[] args)
    {
        SpringApplication.run(BootstrapApp.class, args);
    }
}
