package com.zte.uedm.digitalenergy.domain.permission.valueobject;

/**
 * 菜单类型值对象
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public enum MenuType {
    
    /**
     * 菜单类型
     */
    MENU("MENU", "菜单"),
    
    /**
     * 按钮类型
     */
    BUTTON("BUTTON", "按钮");
    
    private final String code;
    private final String description;
    
    MenuType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取菜单类型
     * 
     * @param code 菜单类型代码
     * @return 菜单类型枚举
     */
    public static MenuType fromCode(String code) {
        for (MenuType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown menu type code: " + code);
    }
    
    /**
     * 判断是否为菜单类型
     * 
     * @return true-菜单，false-按钮
     */
    public boolean isMenu() {
        return this == MENU;
    }
    
    /**
     * 判断是否为按钮类型
     * 
     * @return true-按钮，false-菜单
     */
    public boolean isButton() {
        return this == BUTTON;
    }
}
