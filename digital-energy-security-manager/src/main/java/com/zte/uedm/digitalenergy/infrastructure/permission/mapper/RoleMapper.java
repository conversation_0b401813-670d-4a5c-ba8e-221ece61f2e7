package com.zte.uedm.digitalenergy.infrastructure.permission.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.digitalenergy.infrastructure.permission.po.RolePO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 角色映射器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface RoleMapper extends BaseMapper<RolePO> {
    
    /**
     * 根据角色类型查询角色列表
     * 
     * @param roleType 角色类型
     * @return 角色列表
     */
    @Select("SELECT * FROM t_role WHERE role_type = #{roleType} AND status = 1 ORDER BY create_time")
    List<RolePO> selectByRoleType(@Param("roleType") String roleType);
    
    /**
     * 根据角色编码查询角色
     * 
     * @param roleCode 角色编码
     * @return 角色
     */
    @Select("SELECT * FROM t_role WHERE role_code = #{roleCode}")
    RolePO selectByRoleCode(@Param("roleCode") String roleCode);
    
    /**
     * 根据角色名称查询角色
     * 
     * @param roleName 角色名称
     * @return 角色
     */
    @Select("SELECT * FROM t_role WHERE role_name = #{roleName}")
    RolePO selectByRoleName(@Param("roleName") String roleName);
    
    /**
     * 检查角色编码是否存在（排除指定ID）
     * 
     * @param roleCode 角色编码
     * @param excludeId 排除的角色ID
     * @return 存在数量
     */
    @Select("SELECT COUNT(*) FROM t_role WHERE role_code = #{roleCode} AND id != #{excludeId}")
    int countByRoleCodeAndIdNot(@Param("roleCode") String roleCode, @Param("excludeId") Long excludeId);
    
    /**
     * 检查角色名称是否存在（排除指定ID）
     * 
     * @param roleName 角色名称
     * @param excludeId 排除的角色ID
     * @return 存在数量
     */
    @Select("SELECT COUNT(*) FROM t_role WHERE role_name = #{roleName} AND id != #{excludeId}")
    int countByRoleNameAndIdNot(@Param("roleName") String roleName, @Param("excludeId") Long excludeId);
    
    /**
     * 统计自定义角色数量
     * 
     * @return 自定义角色数量
     */
    @Select("SELECT COUNT(*) FROM t_role WHERE role_type = 'CUSTOM'")
    long countCustomRoles();
    
    /**
     * 查询角色关联的用户数量
     * 
     * @param roleId 角色ID
     * @return 用户数量
     */
    @Select("SELECT COUNT(*) FROM t_user_role WHERE role_id = #{roleId}")
    long countUsersByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 查询角色关联的用户组数量
     * 
     * @param roleId 角色ID
     * @return 用户组数量
     */
    @Select("SELECT COUNT(*) FROM t_user_group_role WHERE role_id = #{roleId}")
    long countUserGroupsByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 分页查询角色列表
     * 
     * @param offset 偏移量
     * @param limit 限制数量
     * @param roleName 角色名称（可选）
     * @param roleType 角色类型（可选）
     * @return 角色列表
     */
    List<RolePO> selectByPage(@Param("offset") int offset, 
                             @Param("limit") int limit,
                             @Param("roleName") String roleName, 
                             @Param("roleType") String roleType);
    
    /**
     * 统计角色总数
     * 
     * @param roleName 角色名称（可选）
     * @param roleType 角色类型（可选）
     * @return 角色总数
     */
    long countRoles(@Param("roleName") String roleName, @Param("roleType") String roleType);
}
