package com.zte.uedm.digitalenergy.interfaces.permission.controller;

import com.zte.uedm.digitalenergy.application.permission.dto.MenuDTO;
import com.zte.uedm.digitalenergy.application.permission.dto.PageQueryDTO;
import com.zte.uedm.digitalenergy.application.permission.dto.PageResultDTO;
import com.zte.uedm.digitalenergy.application.permission.dto.UserDTO;
import com.zte.uedm.digitalenergy.application.permission.dto.UserPermissionInheritanceDTO;
import com.zte.uedm.digitalenergy.application.permission.dto.UserValidationDTO;
import com.zte.uedm.digitalenergy.application.permission.service.MenuApplicationService;
import com.zte.uedm.digitalenergy.application.permission.service.UserApplicationService;
import com.zte.uedm.digitalenergy.common.response.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 用户管理控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Tag(name = "用户管理", description = "用户管理相关接口")
@RestController
@RequestMapping("/api/permission/users")
@Validated
public class UserController {
    
    private static final Logger logger = LoggerFactory.getLogger(UserController.class);
    
    @Autowired
    private UserApplicationService userApplicationService;
    
    @Autowired
    private MenuApplicationService menuApplicationService;
    
    /**
     * 创建用户
     * 
     * @param userDTO 用户信息
     * @param request HTTP请求
     * @return 创建的用户信息
     */
    @Operation(summary = "创建用户", description = "创建新的用户")
    @PostMapping
    public ApiResponse<UserDTO> createUser(
            @Valid @RequestBody UserDTO userDTO,
            HttpServletRequest request) {
        
        logger.info("Creating user: {}", userDTO.getUserCode());
        
        String createBy = getCurrentUser(request);
        UserDTO result = userApplicationService.createUser(userDTO, createBy);
        
        logger.info("User created successfully with id: {}", result.getId());
        
        return ApiResponse.success("用户创建成功", result);
    }
    
    /**
     * 更新用户
     * 
     * @param id 用户ID
     * @param userDTO 用户信息
     * @param request HTTP请求
     * @return 更新的用户信息
     */
    @Operation(summary = "更新用户", description = "更新指定用户信息")
    @PutMapping("/{id}")
    public ApiResponse<UserDTO> updateUser(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long id,
            @Valid @RequestBody UserDTO userDTO,
            HttpServletRequest request) {
        
        logger.info("Updating user: {}", id);
        
        String updateBy = getCurrentUser(request);
        UserDTO result = userApplicationService.updateUser(id, userDTO, updateBy);
        
        logger.info("User updated successfully: {}", id);
        
        return ApiResponse.success("用户更新成功", result);
    }
    
    /**
     * 删除用户
     *
     * @param id 用户ID
     * @return 删除结果
     */
    @Operation(summary = "删除用户", description = "删除指定用户")
    @DeleteMapping("/{id}")
    public ApiResponse<String> deleteUser(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long id) {
        
        logger.info("Deleting user: {}", id);
        
        userApplicationService.deleteUser(id);
        
        logger.info("User deleted successfully: {}", id);
        
        return ApiResponse.success("用户删除成功");
    }
    
    /**
     * 批量删除用户
     *
     * @param ids 用户ID列表
     * @return 删除结果
     */
    @Operation(summary = "批量删除用户", description = "批量删除指定用户")
    @DeleteMapping("/batch")
    public ApiResponse<String> deleteUsers(
            @Parameter(description = "用户ID列表") @RequestBody @NotEmpty List<Long> ids) {
        
        logger.info("Deleting users: {}", ids);
        
        userApplicationService.deleteUsers(ids);
        
        logger.info("Users deleted successfully, count: {}", ids.size());
        
        return ApiResponse.success("用户批量删除成功");
    }
    
    /**
     * 根据ID查询用户
     * 
     * @param id 用户ID
     * @return 用户信息
     */
    @Operation(summary = "查询用户详情", description = "根据ID查询用户详细信息")
    @GetMapping("/{id}")
    public ApiResponse<UserDTO> getUserById(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long id) {
        
        logger.debug("Getting user by id: {}", id);
        
        UserDTO result = userApplicationService.getUserById(id);
        
        return ApiResponse.success(result);
    }
    
    /**
     * 根据用户工号查询用户
     * 
     * @param userCode 用户工号
     * @return 用户信息
     */
    @Operation(summary = "根据工号查询用户", description = "根据用户工号查询用户信息")
    @GetMapping("/code/{userCode}")
    public ApiResponse<UserDTO> getUserByCode(
            @Parameter(description = "用户工号") @PathVariable @NotNull String userCode) {
        
        logger.debug("Getting user by code: {}", userCode);
        
        UserDTO result = userApplicationService.getUserByCode(userCode);
        
        return ApiResponse.success(result);
    }
    
    /**
     * 分页查询用户列表
     * 
     * @param pageQuery 分页查询参数
     * @param username 用户名（可选）
     * @param organization 组织机构（可选）
     * @return 分页结果
     */
    @Operation(summary = "分页查询用户", description = "分页查询用户列表")
    @GetMapping("/page")
    public ApiResponse<PageResultDTO<UserDTO>> getUsersByPage(
            @Valid PageQueryDTO pageQuery,
            @Parameter(description = "用户名") @RequestParam(required = false) String username,
            @Parameter(description = "组织机构") @RequestParam(required = false) String organization) {
        
        logger.debug("Getting users by page: pageNum={}, pageSize={}, username={}, organization={}", 
                    pageQuery.getPageNum(), pageQuery.getPageSize(), username, organization);
        
        PageResultDTO<UserDTO> result = userApplicationService.getUsersByPage(pageQuery, username, organization);
        
        return ApiResponse.success(result);
    }
    
    /**
     * 查询所有用户
     * 
     * @return 用户列表
     */
    @Operation(summary = "查询所有用户", description = "查询系统中所有用户")
    @GetMapping("/all")
    public ApiResponse<List<UserDTO>> getAllUsers() {
        
        logger.debug("Getting all users");
        
        List<UserDTO> result = userApplicationService.getAllUsers();
        
        return ApiResponse.success(result);
    }
    
    /**
     * 查询用户权限菜单
     * 
     * @param id 用户ID
     * @return 用户权限菜单列表
     */
    @Operation(summary = "查询用户权限", description = "查询用户有权限的菜单列表")
    @GetMapping("/{id}/permissions")
    public ApiResponse<List<MenuDTO>> getUserPermissions(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long id) {
        
        logger.debug("Getting user permissions for user: {}", id);
        
        List<MenuDTO> result = menuApplicationService.getUserMenuTree(id);
        
        return ApiResponse.success(result);
    }
    
    /**
     * 查询用户权限继承路径
     * 
     * @param id 用户ID
     * @return 用户权限继承信息
     */
    @Operation(summary = "查询用户权限继承", description = "查询用户权限继承路径详情")
    @GetMapping("/{id}/inheritance")
    public ApiResponse<UserPermissionInheritanceDTO> getUserPermissionInheritance(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long id) {
        
        logger.debug("Getting user permission inheritance for user: {}", id);
        
        UserPermissionInheritanceDTO result = userApplicationService.getUserPermissionInheritance(id);
        
        return ApiResponse.success(result);
    }
    
    /**
     * 验证用户身份
     * 
     * @param userCode 用户工号
     * @return 用户验证结果
     */
    @Operation(summary = "验证用户身份", description = "通过UAC系统验证用户身份")
    @PostMapping("/validate")
    public ApiResponse<UserValidationDTO> validateUser(
            @Parameter(description = "用户工号") @RequestParam @NotNull String userCode) {
        
        logger.debug("Validating user: {}", userCode);
        
        UserValidationDTO result = userApplicationService.validateUser(userCode);
        
        return ApiResponse.success(result);
    }
    
    /**
     * 获取当前用户
     * 
     * @param request HTTP请求
     * @return 当前用户
     */
    private String getCurrentUser(HttpServletRequest request) {
        // 这里应该从Security Context或JWT Token中获取当前用户
        // 暂时返回固定值，实际项目中需要实现
        String user = request.getHeader("X-User-Code");
        return user != null ? user : "system";
    }
}
