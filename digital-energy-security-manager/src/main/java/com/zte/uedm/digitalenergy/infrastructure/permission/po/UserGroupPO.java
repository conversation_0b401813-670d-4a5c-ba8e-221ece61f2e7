package com.zte.uedm.digitalenergy.infrastructure.permission.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户组持久化对象
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@TableName("t_user_group")
public class UserGroupPO {
    
    /**
     * 用户组ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户组名称
     */
    @TableField("group_name")
    private String groupName;
    
    /**
     * 用户组编码
     */
    @TableField("group_code")
    private String groupCode;
    
    /**
     * 用户组描述
     */
    @TableField("group_description")
    private String groupDescription;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
    
    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;
    
    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;
    
    /**
     * 更新人
     */
    @TableField("update_by")
    private String updateBy;
}
