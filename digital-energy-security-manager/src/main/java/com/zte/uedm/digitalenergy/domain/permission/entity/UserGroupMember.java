package com.zte.uedm.digitalenergy.domain.permission.entity;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 用户组成员实体
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class UserGroupMember {
    
    /**
     * 成员关系ID
     */
    private Long id;
    
    /**
     * 用户组ID
     */
    private Long userGroupId;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 默认构造函数
     */
    public UserGroupMember() {
    }
    
    /**
     * 构造函数
     * 
     * @param userGroupId 用户组ID
     * @param userId 用户ID
     */
    public UserGroupMember(Long userGroupId, Long userId) {
        if (userGroupId == null) {
            throw new IllegalArgumentException("User group ID cannot be null");
        }
        if (userId == null) {
            throw new IllegalArgumentException("User ID cannot be null");
        }
        
        this.userGroupId = userGroupId;
        this.userId = userId;
        this.createTime = LocalDateTime.now();
    }
    
    /**
     * 检查成员关系是否有效
     * 
     * @return true-有效，false-无效
     */
    public boolean isValid() {
        return userGroupId != null && userId != null;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserGroupId() {
        return userGroupId;
    }
    
    public void setUserGroupId(Long userGroupId) {
        this.userGroupId = userGroupId;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserGroupMember that = (UserGroupMember) o;
        return Objects.equals(userGroupId, that.userGroupId) &&
               Objects.equals(userId, that.userId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(userGroupId, userId);
    }
    
    @Override
    public String toString() {
        return "UserGroupMember{" +
                "id=" + id +
                ", userGroupId=" + userGroupId +
                ", userId=" + userId +
                ", createTime=" + createTime +
                '}';
    }
}
