package com.zte.uedm.digitalenergy.domain.permission.aggregate;

import com.zte.uedm.digitalenergy.domain.permission.entity.Role;
import com.zte.uedm.digitalenergy.domain.permission.entity.RoleMenuPermission;
import com.zte.uedm.digitalenergy.domain.permission.valueobject.RoleType;

import java.util.List;

/**
 * 角色聚合根
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class RoleAggregate {
    
    /**
     * 自定义角色最大数量限制
     */
    public static final int MAX_CUSTOM_ROLE_COUNT = 1000;
    
    /**
     * 角色实体
     */
    private Role role;
    
    /**
     * 构造函数
     * 
     * @param role 角色实体
     */
    public RoleAggregate(Role role) {
        if (role == null) {
            throw new IllegalArgumentException("Role cannot be null");
        }
        this.role = role;
    }
    
    /**
     * 创建自定义角色
     * 
     * @param roleName 角色名称
     * @param roleCode 角色编码
     * @param roleDescription 角色描述
     * @param createBy 创建人
     * @return 角色聚合
     */
    public static RoleAggregate createCustomRole(String roleName, String roleCode, 
                                                String roleDescription, String createBy) {
        Role role = new Role(roleName, roleCode, roleDescription, RoleType.CUSTOM);
        role.setCreateBy(createBy);
        return new RoleAggregate(role);
    }
    
    /**
     * 创建默认角色
     * 
     * @param roleName 角色名称
     * @param roleCode 角色编码
     * @param roleDescription 角色描述
     * @return 角色聚合
     */
    public static RoleAggregate createDefaultRole(String roleName, String roleCode, String roleDescription) {
        Role role = new Role(roleName, roleCode, roleDescription, RoleType.DEFAULT);
        role.setCreateBy("system");
        return new RoleAggregate(role);
    }
    
    /**
     * 验证角色是否可以删除
     * 
     * @throws IllegalStateException 如果角色不可删除
     */
    public void validateDeletable() {
        if (!role.isDeletable()) {
            throw new IllegalStateException("Default role cannot be deleted: " + role.getRoleCode());
        }
    }
    
    /**
     * 验证角色名称是否可以修改
     * 
     * @throws IllegalStateException 如果角色名称不可修改
     */
    public void validateNameEditable() {
        if (!role.isNameEditable()) {
            throw new IllegalStateException("Default role name cannot be modified: " + role.getRoleCode());
        }
    }
    
    /**
     * 更新角色信息
     * 
     * @param roleDescription 角色描述
     * @param updateBy 更新人
     */
    public void updateRole(String roleDescription, String updateBy) {
        role.updateRole(roleDescription, updateBy);
    }
    
    /**
     * 分配菜单权限
     * 
     * @param menuIds 菜单ID列表
     */
    public void assignMenuPermissions(List<Long> menuIds) {
        if (menuIds == null || menuIds.isEmpty()) {
            throw new IllegalArgumentException("Menu permissions cannot be empty for role");
        }
        
        role.setMenuPermissions(menuIds);
    }
    
    /**
     * 添加单个菜单权限
     * 
     * @param menuId 菜单ID
     */
    public void addMenuPermission(Long menuId) {
        role.addMenuPermission(menuId);
    }
    
    /**
     * 移除单个菜单权限
     * 
     * @param menuId 菜单ID
     */
    public void removeMenuPermission(Long menuId) {
        role.removeMenuPermission(menuId);
    }
    
    /**
     * 获取角色菜单权限列表
     * 
     * @return 菜单权限列表
     */
    public List<RoleMenuPermission> getMenuPermissions() {
        return role.getMenuPermissions();
    }
    
    /**
     * 检查是否为默认角色
     * 
     * @return true-默认角色，false-自定义角色
     */
    public boolean isDefaultRole() {
        return role.getRoleType() != null && role.getRoleType().isDefault();
    }
    
    /**
     * 检查是否为自定义角色
     * 
     * @return true-自定义角色，false-默认角色
     */
    public boolean isCustomRole() {
        return role.getRoleType() != null && !role.getRoleType().isDefault();
    }
    
    /**
     * 获取角色实体
     * 
     * @return 角色实体
     */
    public Role getRole() {
        return role;
    }
    
    /**
     * 获取角色ID
     * 
     * @return 角色ID
     */
    public Long getId() {
        return role.getId();
    }
    
    /**
     * 获取角色名称
     * 
     * @return 角色名称
     */
    public String getRoleName() {
        return role.getRoleName();
    }
    
    /**
     * 获取角色编码
     * 
     * @return 角色编码
     */
    public String getRoleCode() {
        return role.getRoleCode();
    }
    
    /**
     * 获取角色描述
     * 
     * @return 角色描述
     */
    public String getRoleDescription() {
        return role.getRoleDescription();
    }
    
    /**
     * 获取角色类型
     * 
     * @return 角色类型
     */
    public RoleType getRoleType() {
        return role.getRoleType();
    }
    
    @Override
    public String toString() {
        return "RoleAggregate{" +
                "role=" + role +
                '}';
    }
}
