package com.zte.uedm.digitalenergy.domain.permission.entity;

import com.zte.uedm.digitalenergy.domain.permission.valueobject.Organization;
import com.zte.uedm.digitalenergy.domain.permission.valueobject.UserStatus;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 用户实体
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class User {
    
    /**
     * 用户角色分配最大数量限制
     */
    public static final int MAX_ROLE_COUNT = 10;
    
    /**
     * 用户ID
     */
    private Long id;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 用户工号
     */
    private String userCode;
    
    /**
     * 组织机构
     */
    private Organization organization;
    
    /**
     * 状态
     */
    private UserStatus status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 用户角色分配列表
     */
    private List<UserRoleAssignment> roleAssignments;
    
    /**
     * 默认构造函数
     */
    public User() {
        this.roleAssignments = new ArrayList<>();
    }
    
    /**
     * 构造函数
     * 
     * @param username 用户名
     * @param userCode 用户工号
     * @param organization 组织机构
     */
    public User(String username, String userCode, Organization organization) {
        this();
        this.setUsername(username);
        this.setUserCode(userCode);
        this.organization = organization;
        this.status = UserStatus.ENABLED;
        this.createTime = LocalDateTime.now();
    }
    
    /**
     * 设置用户名
     * 
     * @param username 用户名
     */
    public void setUsername(String username) {
        if (username == null || username.trim().isEmpty()) {
            throw new IllegalArgumentException("Username cannot be null or empty");
        }
        this.username = username.trim();
    }
    
    /**
     * 设置用户工号
     * 
     * @param userCode 用户工号
     */
    public void setUserCode(String userCode) {
        if (userCode == null || userCode.trim().isEmpty()) {
            throw new IllegalArgumentException("User code cannot be null or empty");
        }
        this.userCode = userCode.trim();
    }
    
    /**
     * 分配角色
     * 
     * @param roleId 角色ID
     */
    public void assignRole(Long roleId) {
        if (roleId == null) {
            throw new IllegalArgumentException("Role ID cannot be null");
        }
        
        // 检查角色数量限制
        if (roleAssignments.size() >= MAX_ROLE_COUNT) {
            throw new IllegalStateException("User cannot have more than " + MAX_ROLE_COUNT + " roles");
        }
        
        // 检查是否已分配
        boolean exists = roleAssignments.stream()
                .anyMatch(assignment -> assignment.getRoleId().equals(roleId));
        
        if (!exists) {
            UserRoleAssignment assignment = new UserRoleAssignment(this.id, roleId);
            roleAssignments.add(assignment);
        }
    }
    
    /**
     * 取消角色分配
     * 
     * @param roleId 角色ID
     */
    public void unassignRole(Long roleId) {
        if (roleId == null) {
            return;
        }
        
        roleAssignments.removeIf(assignment -> assignment.getRoleId().equals(roleId));
    }
    
    /**
     * 批量设置角色
     * 
     * @param roleIds 角色ID列表
     */
    public void setRoles(List<Long> roleIds) {
        if (roleIds == null) {
            throw new IllegalArgumentException("Role IDs cannot be null");
        }
        
        if (roleIds.size() > MAX_ROLE_COUNT) {
            throw new IllegalStateException("User cannot have more than " + MAX_ROLE_COUNT + " roles");
        }
        
        this.roleAssignments.clear();
        for (Long roleId : roleIds) {
            assignRole(roleId);
        }
    }
    
    /**
     * 获取用户分配的角色ID列表
     * 
     * @return 角色ID列表
     */
    public List<Long> getRoleIds() {
        return roleAssignments.stream()
                .map(UserRoleAssignment::getRoleId)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
    
    /**
     * 检查用户是否有指定角色
     * 
     * @param roleId 角色ID
     * @return true-有该角色，false-没有该角色
     */
    public boolean hasRole(Long roleId) {
        if (roleId == null) {
            return false;
        }
        
        return roleAssignments.stream()
                .anyMatch(assignment -> assignment.getRoleId().equals(roleId));
    }
    
    /**
     * 更新用户信息
     * 
     * @param updateBy 更新人
     */
    public void updateUser(String updateBy) {
        this.updateBy = updateBy;
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 启用用户
     */
    public void enable() {
        this.status = UserStatus.ENABLED;
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 禁用用户
     */
    public void disable() {
        this.status = UserStatus.DISABLED;
        this.updateTime = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getUsername() {
        return username;
    }
    
    public String getUserCode() {
        return userCode;
    }
    
    public Organization getOrganization() {
        return organization;
    }
    
    public void setOrganization(Organization organization) {
        this.organization = organization;
    }
    
    public UserStatus getStatus() {
        return status;
    }
    
    public void setStatus(UserStatus status) {
        this.status = status;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public String getCreateBy() {
        return createBy;
    }
    
    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    public String getUpdateBy() {
        return updateBy;
    }
    
    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }
    
    public List<UserRoleAssignment> getRoleAssignments() {
        return new ArrayList<>(roleAssignments);
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        User user = (User) o;
        return Objects.equals(id, user.id) &&
               Objects.equals(userCode, user.userCode);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id, userCode);
    }
    
    @Override
    public String toString() {
        return "User{" +
                "id=" + id +
                ", username='" + username + '\'' +
                ", userCode='" + userCode + '\'' +
                ", organization=" + organization +
                ", status=" + status +
                '}';
    }
}
