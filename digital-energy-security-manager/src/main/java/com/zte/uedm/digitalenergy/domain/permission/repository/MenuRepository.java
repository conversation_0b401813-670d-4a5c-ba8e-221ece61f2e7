package com.zte.uedm.digitalenergy.domain.permission.repository;

import com.zte.uedm.digitalenergy.domain.permission.entity.Menu;
import com.zte.uedm.digitalenergy.domain.permission.valueobject.MenuType;

import java.util.List;
import java.util.Optional;

/**
 * 菜单仓储接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface MenuRepository {
    
    /**
     * 保存菜单
     * 
     * @param menu 菜单实体
     * @return 保存后的菜单实体
     */
    Menu save(Menu menu);
    
    /**
     * 根据ID查找菜单
     * 
     * @param id 菜单ID
     * @return 菜单实体
     */
    Optional<Menu> findById(Long id);
    
    /**
     * 根据菜单编码查找菜单
     * 
     * @param menuCode 菜单编码
     * @return 菜单实体
     */
    Optional<Menu> findByMenuCode(String menuCode);
    
    /**
     * 查找所有菜单
     * 
     * @return 菜单列表
     */
    List<Menu> findAll();
    
    /**
     * 查找所有启用的菜单
     * 
     * @return 菜单列表
     */
    List<Menu> findAllEnabled();
    
    /**
     * 根据父菜单ID查找子菜单列表
     * 
     * @param parentId 父菜单ID
     * @return 子菜单列表
     */
    List<Menu> findByParentId(Long parentId);
    
    /**
     * 查找根菜单列表
     * 
     * @return 根菜单列表
     */
    List<Menu> findRootMenus();
    
    /**
     * 根据菜单类型查找菜单列表
     * 
     * @param menuType 菜单类型
     * @return 菜单列表
     */
    List<Menu> findByMenuType(MenuType menuType);
    
    /**
     * 构建菜单树
     * 
     * @return 菜单树列表
     */
    List<Menu> buildMenuTree();
    
    /**
     * 构建启用的菜单树
     * 
     * @return 菜单树列表
     */
    List<Menu> buildEnabledMenuTree();
    
    /**
     * 根据用户ID查找用户有权限的菜单列表
     * 
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<Menu> findMenusByUserId(Long userId);
    
    /**
     * 根据用户ID构建用户菜单树
     * 
     * @param userId 用户ID
     * @return 菜单树列表
     */
    List<Menu> buildUserMenuTree(Long userId);
    
    /**
     * 根据角色ID查找角色有权限的菜单列表
     * 
     * @param roleId 角色ID
     * @return 菜单列表
     */
    List<Menu> findMenusByRoleId(Long roleId);
    
    /**
     * 根据角色ID列表查找菜单列表
     * 
     * @param roleIds 角色ID列表
     * @return 菜单列表
     */
    List<Menu> findMenusByRoleIds(List<Long> roleIds);
    
    /**
     * 删除菜单
     * 
     * @param menu 菜单实体
     */
    void delete(Menu menu);
    
    /**
     * 根据ID删除菜单
     * 
     * @param id 菜单ID
     */
    void deleteById(Long id);
    
    /**
     * 检查菜单编码是否存在
     * 
     * @param menuCode 菜单编码
     * @return true-存在，false-不存在
     */
    boolean existsByMenuCode(String menuCode);
    
    /**
     * 检查菜单名称是否存在
     * 
     * @param menuName 菜单名称
     * @return true-存在，false-不存在
     */
    boolean existsByMenuName(String menuName);
    
    /**
     * 检查菜单是否有子菜单
     * 
     * @param menuId 菜单ID
     * @return true-有子菜单，false-无子菜单
     */
    boolean hasChildren(Long menuId);
    
    /**
     * 查找菜单的所有子菜单ID（递归）
     * 
     * @param menuId 菜单ID
     * @return 子菜单ID列表
     */
    List<Long> findAllChildrenIds(Long menuId);
    
    /**
     * 根据权限编码查找菜单
     * 
     * @param permissionCode 权限编码
     * @return 菜单实体
     */
    Optional<Menu> findByPermissionCode(String permissionCode);
    
    /**
     * 查找指定层级的菜单列表
     * 
     * @param level 菜单层级
     * @return 菜单列表
     */
    List<Menu> findByLevel(Integer level);
}
