package com.zte.uedm.digitalenergy.infrastructure.permission.repository;

import com.zte.uedm.digitalenergy.domain.permission.entity.Menu;
import com.zte.uedm.digitalenergy.domain.permission.repository.MenuRepository;
import com.zte.uedm.digitalenergy.domain.permission.valueobject.MenuType;
import com.zte.uedm.digitalenergy.infrastructure.permission.converter.PermissionConverter;
import com.zte.uedm.digitalenergy.infrastructure.permission.mapper.MenuMapper;
import com.zte.uedm.digitalenergy.infrastructure.permission.po.MenuPO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 菜单仓储实现
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Repository
public class MenuRepositoryImpl implements MenuRepository {
    
    private static final Logger logger = LoggerFactory.getLogger(MenuRepositoryImpl.class);
    
    @Autowired
    private MenuMapper menuMapper;
    
    @Override
    public Menu save(Menu menu) {
        logger.info("Saving menu: {}", menu.getMenuCode());
        
        MenuPO menuPO = PermissionConverter.INSTANCE.toMenuPO(menu);
        
        if (menu.getId() == null) {
            // 新增菜单
            menuPO.setCreateTime(LocalDateTime.now());
            menuMapper.insert(menuPO);
            menu.setId(menuPO.getId());
        } else {
            // 更新菜单
            menuPO.setUpdateTime(LocalDateTime.now());
            menuMapper.updateById(menuPO);
        }
        
        logger.info("Menu saved successfully with id: {}", menu.getId());
        return menu;
    }
    
    @Override
    public Optional<Menu> findById(Long id) {
        logger.debug("Finding menu by id: {}", id);
        
        MenuPO menuPO = menuMapper.selectById(id);
        if (menuPO == null) {
            return Optional.empty();
        }
        
        Menu menu = PermissionConverter.INSTANCE.toMenu(menuPO);
        return Optional.of(menu);
    }
    
    @Override
    public Optional<Menu> findByMenuCode(String menuCode) {
        logger.debug("Finding menu by code: {}", menuCode);
        
        MenuPO menuPO = menuMapper.selectByMenuCode(menuCode);
        if (menuPO == null) {
            return Optional.empty();
        }
        
        Menu menu = PermissionConverter.INSTANCE.toMenu(menuPO);
        return Optional.of(menu);
    }
    
    @Override
    public List<Menu> findAll() {
        logger.debug("Finding all menus");
        
        List<MenuPO> menuPOs = menuMapper.selectList(null);
        return PermissionConverter.INSTANCE.toMenuList(menuPOs);
    }
    
    @Override
    public List<Menu> findAllEnabled() {
        logger.debug("Finding all enabled menus");
        
        List<MenuPO> menuPOs = menuMapper.selectAllEnabled();
        return PermissionConverter.INSTANCE.toMenuList(menuPOs);
    }
    
    @Override
    public List<Menu> findByParentId(Long parentId) {
        logger.debug("Finding menus by parent id: {}", parentId);
        
        List<MenuPO> menuPOs = menuMapper.selectByParentId(parentId);
        return PermissionConverter.INSTANCE.toMenuList(menuPOs);
    }
    
    @Override
    public List<Menu> findRootMenus() {
        logger.debug("Finding root menus");
        
        List<MenuPO> menuPOs = menuMapper.selectRootMenus();
        return PermissionConverter.INSTANCE.toMenuList(menuPOs);
    }
    
    @Override
    public List<Menu> findByMenuType(MenuType menuType) {
        logger.debug("Finding menus by type: {}", menuType);
        
        List<MenuPO> menuPOs = menuMapper.selectByMenuType(menuType.getCode());
        return PermissionConverter.INSTANCE.toMenuList(menuPOs);
    }
    
    @Override
    public List<Menu> buildMenuTree() {
        logger.debug("Building menu tree");
        
        List<Menu> allMenus = findAllEnabled();
        return buildTree(allMenus);
    }
    
    @Override
    public List<Menu> buildEnabledMenuTree() {
        logger.debug("Building enabled menu tree");
        
        List<Menu> enabledMenus = findAllEnabled();
        return buildTree(enabledMenus);
    }
    
    @Override
    public List<Menu> findMenusByUserId(Long userId) {
        logger.debug("Finding menus by user id: {}", userId);
        
        List<MenuPO> menuPOs = menuMapper.selectMenusByUserId(userId);
        return PermissionConverter.INSTANCE.toMenuList(menuPOs);
    }
    
    @Override
    public List<Menu> buildUserMenuTree(Long userId) {
        logger.debug("Building user menu tree for user: {}", userId);
        
        List<Menu> userMenus = findMenusByUserId(userId);
        return buildTree(userMenus);
    }
    
    @Override
    public List<Menu> findMenusByRoleId(Long roleId) {
        logger.debug("Finding menus by role id: {}", roleId);
        
        List<MenuPO> menuPOs = menuMapper.selectMenusByRoleId(roleId);
        return PermissionConverter.INSTANCE.toMenuList(menuPOs);
    }
    
    @Override
    public List<Menu> findMenusByRoleIds(List<Long> roleIds) {
        logger.debug("Finding menus by role ids: {}", roleIds);
        
        if (roleIds == null || roleIds.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<MenuPO> menuPOs = menuMapper.selectMenusByRoleIds(roleIds);
        return PermissionConverter.INSTANCE.toMenuList(menuPOs);
    }
    
    @Override
    public void delete(Menu menu) {
        logger.info("Deleting menu: {}", menu.getMenuCode());
        
        menuMapper.deleteById(menu.getId());
        
        logger.info("Menu deleted successfully: {}", menu.getMenuCode());
    }
    
    @Override
    public void deleteById(Long id) {
        logger.info("Deleting menu by id: {}", id);
        
        menuMapper.deleteById(id);
        
        logger.info("Menu deleted successfully with id: {}", id);
    }
    
    @Override
    public boolean existsByMenuCode(String menuCode) {
        return menuMapper.countByMenuCode(menuCode) > 0;
    }
    
    @Override
    public boolean existsByMenuName(String menuName) {
        return menuMapper.countByMenuName(menuName) > 0;
    }
    
    @Override
    public boolean hasChildren(Long menuId) {
        return menuMapper.countChildren(menuId) > 0;
    }
    
    @Override
    public List<Long> findAllChildrenIds(Long menuId) {
        logger.debug("Finding all children ids for menu: {}", menuId);
        
        return menuMapper.selectAllChildrenIds(menuId);
    }
    
    @Override
    public Optional<Menu> findByPermissionCode(String permissionCode) {
        logger.debug("Finding menu by permission code: {}", permissionCode);
        
        MenuPO menuPO = menuMapper.selectByPermissionCode(permissionCode);
        if (menuPO == null) {
            return Optional.empty();
        }
        
        Menu menu = PermissionConverter.INSTANCE.toMenu(menuPO);
        return Optional.of(menu);
    }
    
    @Override
    public List<Menu> findByLevel(Integer level) {
        logger.debug("Finding menus by level: {}", level);
        
        List<MenuPO> menuPOs = menuMapper.selectByLevel(level);
        return PermissionConverter.INSTANCE.toMenuList(menuPOs);
    }
    
    /**
     * 构建菜单树
     * 
     * @param menus 菜单列表
     * @return 菜单树
     */
    private List<Menu> buildTree(List<Menu> menus) {
        if (menus == null || menus.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 按ID分组，便于查找
        Map<Long, Menu> menuMap = menus.stream()
                .collect(Collectors.toMap(Menu::getId, menu -> menu));
        
        // 根菜单列表
        List<Menu> rootMenus = new ArrayList<>();
        
        // 构建树形结构
        for (Menu menu : menus) {
            if (menu.getParentId() == null || menu.getParentId() == 0L) {
                // 根菜单
                rootMenus.add(menu);
            } else {
                // 子菜单，添加到父菜单的children中
                Menu parent = menuMap.get(menu.getParentId());
                if (parent != null) {
                    parent.addChild(menu);
                }
            }
        }
        
        // 按排序号排序
        rootMenus.sort(Comparator.comparing(Menu::getSortOrder, Comparator.nullsLast(Integer::compareTo)));
        
        // 递归排序子菜单
        sortChildren(rootMenus);
        
        return rootMenus;
    }
    
    /**
     * 递归排序子菜单
     * 
     * @param menus 菜单列表
     */
    private void sortChildren(List<Menu> menus) {
        for (Menu menu : menus) {
            List<Menu> children = menu.getChildren();
            if (!children.isEmpty()) {
                children.sort(Comparator.comparing(Menu::getSortOrder, Comparator.nullsLast(Integer::compareTo)));
                sortChildren(children);
            }
        }
    }
}
