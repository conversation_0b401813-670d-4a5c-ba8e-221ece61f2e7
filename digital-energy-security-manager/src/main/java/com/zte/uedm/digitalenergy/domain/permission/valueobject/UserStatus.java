package com.zte.uedm.digitalenergy.domain.permission.valueobject;

/**
 * 用户状态值对象
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public enum UserStatus {
    
    /**
     * 启用状态
     */
    ENABLED(1, "启用"),
    
    /**
     * 禁用状态
     */
    DISABLED(0, "禁用");
    
    private final Integer code;
    private final String description;
    
    UserStatus(Integer code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取用户状态
     * 
     * @param code 状态代码
     * @return 用户状态枚举
     */
    public static UserStatus fromCode(Integer code) {
        for (UserStatus status : values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown user status code: " + code);
    }
    
    /**
     * 判断是否为启用状态
     * 
     * @return true-启用，false-禁用
     */
    public boolean isEnabled() {
        return this == ENABLED;
    }
}
