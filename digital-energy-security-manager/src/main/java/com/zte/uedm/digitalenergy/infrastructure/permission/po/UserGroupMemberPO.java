package com.zte.uedm.digitalenergy.infrastructure.permission.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户组成员关联持久化对象
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@TableName("t_user_group_member")
public class UserGroupMemberPO {
    
    /**
     * 关联ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户组ID
     */
    @TableField("user_group_id")
    private Long userGroupId;
    
    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
}
