package com.zte.uedm.digitalenergy.common.exception;

import com.zte.uedm.digitalenergy.common.response.ResultCode;

/**
 * 权限管理业务异常
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class PermissionException extends RuntimeException {
    
    /**
     * 错误码
     */
    private Integer code;
    
    /**
     * 错误消息
     */
    private String message;
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     */
    public PermissionException(String message) {
        super(message);
        this.code = ResultCode.BUSINESS_ERROR.getCode();
        this.message = message;
    }
    
    /**
     * 构造函数
     * 
     * @param code 错误码
     * @param message 错误消息
     */
    public PermissionException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 构造函数
     * 
     * @param resultCode 结果码枚举
     */
    public PermissionException(ResultCode resultCode) {
        super(resultCode.getMessage());
        this.code = resultCode.getCode();
        this.message = resultCode.getMessage();
    }
    
    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param cause 原因异常
     */
    public PermissionException(String message, Throwable cause) {
        super(message, cause);
        this.code = ResultCode.BUSINESS_ERROR.getCode();
        this.message = message;
    }
    
    /**
     * 构造函数
     * 
     * @param code 错误码
     * @param message 错误消息
     * @param cause 原因异常
     */
    public PermissionException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }
    
    /**
     * 构造函数
     * 
     * @param resultCode 结果码枚举
     * @param cause 原因异常
     */
    public PermissionException(ResultCode resultCode, Throwable cause) {
        super(resultCode.getMessage(), cause);
        this.code = resultCode.getCode();
        this.message = resultCode.getMessage();
    }
    
    // 静态工厂方法
    
    /**
     * 角色不存在异常
     * 
     * @param roleId 角色ID
     * @return 权限异常
     */
    public static PermissionException roleNotFound(Long roleId) {
        return new PermissionException(ResultCode.ROLE_NOT_FOUND.getCode(), 
                "Role not found with id: " + roleId);
    }
    
    /**
     * 角色已存在异常
     * 
     * @param roleCode 角色编码
     * @return 权限异常
     */
    public static PermissionException roleAlreadyExists(String roleCode) {
        return new PermissionException(ResultCode.ROLE_ALREADY_EXISTS.getCode(), 
                "Role already exists with code: " + roleCode);
    }
    
    /**
     * 角色不能删除异常
     * 
     * @param roleCode 角色编码
     * @return 权限异常
     */
    public static PermissionException roleCannotDelete(String roleCode) {
        return new PermissionException(ResultCode.ROLE_CANNOT_DELETE.getCode(), 
                "Role cannot be deleted: " + roleCode);
    }
    
    /**
     * 用户不存在异常
     *
     * @param userId 用户ID
     * @return 权限异常
     */
    public static PermissionException userNotFound(Long userId) {
        return new PermissionException(ResultCode.USER_NOT_FOUND.getCode(),
                "User not found with id: " + userId);
    }

    /**
     * 用户不存在异常
     *
     * @param userCode 用户工号
     * @return 权限异常
     */
    public static PermissionException userNotFound(String userCode) {
        return new PermissionException(ResultCode.USER_NOT_FOUND.getCode(),
                "User not found with code: " + userCode);
    }
    
    /**
     * 用户已存在异常
     * 
     * @param userCode 用户工号
     * @return 权限异常
     */
    public static PermissionException userAlreadyExists(String userCode) {
        return new PermissionException(ResultCode.USER_ALREADY_EXISTS.getCode(), 
                "User already exists with code: " + userCode);
    }
    
    /**
     * 用户组不存在异常
     *
     * @param userGroupId 用户组ID
     * @return 权限异常
     */
    public static PermissionException userGroupNotFound(Long userGroupId) {
        return new PermissionException(ResultCode.USER_GROUP_NOT_FOUND.getCode(),
                "User group not found with id: " + userGroupId);
    }

    /**
     * 用户组不存在异常
     *
     * @param groupCode 用户组编码
     * @return 权限异常
     */
    public static PermissionException userGroupNotFound(String groupCode) {
        return new PermissionException(ResultCode.USER_GROUP_NOT_FOUND.getCode(),
                "User group not found with code: " + groupCode);
    }

    /**
     * 用户组已存在异常
     *
     * @param groupCode 用户组编码
     * @return 权限异常
     */
    public static PermissionException userGroupAlreadyExists(String groupCode) {
        return new PermissionException(ResultCode.USER_GROUP_ALREADY_EXISTS.getCode(),
                "User group already exists with code: " + groupCode);
    }
    
    /**
     * 菜单不存在异常
     * 
     * @param menuId 菜单ID
     * @return 权限异常
     */
    public static PermissionException menuNotFound(Long menuId) {
        return new PermissionException(ResultCode.MENU_NOT_FOUND.getCode(), 
                "Menu not found with id: " + menuId);
    }
    
    /**
     * 权限不足异常
     * 
     * @param operation 操作名称
     * @return 权限异常
     */
    public static PermissionException permissionDenied(String operation) {
        return new PermissionException(ResultCode.PERMISSION_DENIED.getCode(), 
                "Permission denied for operation: " + operation);
    }
    
    /**
     * 角色数量超出限制异常
     * 
     * @param limit 限制数量
     * @return 权限异常
     */
    public static PermissionException roleCountLimitExceeded(int limit) {
        return new PermissionException(ResultCode.ROLE_COUNT_LIMIT_EXCEEDED.getCode(), 
                "Role count limit exceeded, maximum allowed: " + limit);
    }
    
    /**
     * 用户角色数量超出限制异常
     * 
     * @param limit 限制数量
     * @return 权限异常
     */
    public static PermissionException userRoleLimitExceeded(int limit) {
        return new PermissionException(ResultCode.USER_ROLE_LIMIT_EXCEEDED.getCode(), 
                "User role count limit exceeded, maximum allowed: " + limit);
    }
    
    /**
     * 用户组角色数量超出限制异常
     * 
     * @param limit 限制数量
     * @return 权限异常
     */
    public static PermissionException userGroupRoleLimitExceeded(int limit) {
        return new PermissionException(ResultCode.USER_GROUP_ROLE_LIMIT_EXCEEDED.getCode(), 
                "User group role count limit exceeded, maximum allowed: " + limit);
    }
    
    public Integer getCode() {
        return code;
    }
    
    @Override
    public String getMessage() {
        return message;
    }
    
    @Override
    public String toString() {
        return "PermissionException{" +
                "code=" + code +
                ", message='" + message + '\'' +
                '}';
    }
}
