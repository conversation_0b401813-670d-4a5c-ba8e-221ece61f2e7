package com.zte.uedm.digitalenergy.application.permission.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 菜单数据传输对象
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class MenuDTO {
    
    /**
     * 菜单ID
     */
    private Long id;
    
    /**
     * 菜单名称
     */
    @NotBlank(message = "菜单名称不能为空")
    @Size(max = 100, message = "菜单名称不能超过100个字符")
    private String menuName;
    
    /**
     * 菜单编码
     */
    @NotBlank(message = "菜单编码不能为空")
    @Size(max = 100, message = "菜单编码不能超过100个字符")
    private String menuCode;
    
    /**
     * 父菜单ID，0表示根菜单
     */
    private Long parentId;
    
    /**
     * 菜单路径
     */
    @Size(max = 200, message = "菜单路径不能超过200个字符")
    private String menuPath;
    
    /**
     * 菜单图标
     */
    @Size(max = 100, message = "菜单图标不能超过100个字符")
    private String menuIcon;
    
    /**
     * 排序号
     */
    private Integer sortOrder;
    
    /**
     * 菜单层级
     */
    private Integer menuLevel;
    
    /**
     * 菜单类型：MENU-菜单，BUTTON-按钮
     */
    private String menuType;
    
    /**
     * 权限编码
     */
    @Size(max = 100, message = "权限编码不能超过100个字符")
    private String permissionCode;
    
    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 子菜单列表
     */
    private List<MenuDTO> children;
    
    /**
     * 是否选中（用于权限分配界面）
     */
    private Boolean checked;
    
    /**
     * 是否展开（用于树形展示）
     */
    private Boolean expanded;
}
