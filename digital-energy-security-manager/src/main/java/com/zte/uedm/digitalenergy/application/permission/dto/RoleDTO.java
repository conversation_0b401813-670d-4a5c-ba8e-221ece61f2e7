package com.zte.uedm.digitalenergy.application.permission.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色数据传输对象
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class RoleDTO {
    
    /**
     * 角色ID
     */
    private Long id;
    
    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 20, message = "角色名称不能超过20个字符")
    private String roleName;
    
    /**
     * 角色编码
     */
    @NotBlank(message = "角色编码不能为空")
    @Size(max = 50, message = "角色编码不能超过50个字符")
    private String roleCode;
    
    /**
     * 角色描述
     */
    @Size(max = 200, message = "角色描述不能超过200个字符")
    private String roleDescription;
    
    /**
     * 角色类型：DEFAULT-默认角色，CUSTOM-自定义角色
     */
    private String roleType;
    
    /**
     * 是否默认角色
     */
    private Boolean isDefault;
    
    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 菜单权限ID列表
     */
    private List<Long> menuIds;
    
    /**
     * 菜单权限列表（用于展示）
     */
    private List<MenuDTO> menus;
    
    /**
     * 关联用户数量
     */
    private Long userCount;
    
    /**
     * 关联用户组数量
     */
    private Long userGroupCount;
}
