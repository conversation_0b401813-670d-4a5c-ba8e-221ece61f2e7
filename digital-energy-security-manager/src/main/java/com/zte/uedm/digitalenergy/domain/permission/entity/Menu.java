package com.zte.uedm.digitalenergy.domain.permission.entity;

import com.zte.uedm.digitalenergy.domain.permission.valueobject.MenuType;
import com.zte.uedm.digitalenergy.domain.permission.valueobject.UserStatus;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 菜单实体
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class Menu {
    
    /**
     * 菜单ID
     */
    private Long id;
    
    /**
     * 菜单名称
     */
    private String menuName;
    
    /**
     * 菜单编码
     */
    private String menuCode;
    
    /**
     * 父菜单ID，0表示根菜单
     */
    private Long parentId;
    
    /**
     * 菜单路径
     */
    private String menuPath;
    
    /**
     * 菜单图标
     */
    private String menuIcon;
    
    /**
     * 排序号
     */
    private Integer sortOrder;
    
    /**
     * 菜单层级
     */
    private Integer menuLevel;
    
    /**
     * 菜单类型
     */
    private MenuType menuType;
    
    /**
     * 权限编码
     */
    private String permissionCode;
    
    /**
     * 状态
     */
    private UserStatus status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 子菜单列表
     */
    private List<Menu> children;
    
    /**
     * 默认构造函数
     */
    public Menu() {
        this.children = new ArrayList<>();
    }
    
    /**
     * 构造函数
     * 
     * @param menuName 菜单名称
     * @param menuCode 菜单编码
     * @param parentId 父菜单ID
     * @param menuPath 菜单路径
     * @param menuType 菜单类型
     */
    public Menu(String menuName, String menuCode, Long parentId, String menuPath, MenuType menuType) {
        this();
        this.setMenuName(menuName);
        this.setMenuCode(menuCode);
        this.parentId = parentId != null ? parentId : 0L;
        this.menuPath = menuPath;
        this.menuType = menuType != null ? menuType : MenuType.MENU;
        this.permissionCode = menuCode;
        this.status = UserStatus.ENABLED;
        this.sortOrder = 0;
        this.menuLevel = calculateMenuLevel(parentId);
        this.createTime = LocalDateTime.now();
    }
    
    /**
     * 设置菜单名称
     * 
     * @param menuName 菜单名称
     */
    public void setMenuName(String menuName) {
        if (menuName == null || menuName.trim().isEmpty()) {
            throw new IllegalArgumentException("Menu name cannot be null or empty");
        }
        this.menuName = menuName.trim();
    }
    
    /**
     * 设置菜单编码
     * 
     * @param menuCode 菜单编码
     */
    public void setMenuCode(String menuCode) {
        if (menuCode == null || menuCode.trim().isEmpty()) {
            throw new IllegalArgumentException("Menu code cannot be null or empty");
        }
        this.menuCode = menuCode.trim();
    }
    
    /**
     * 计算菜单层级
     * 
     * @param parentId 父菜单ID
     * @return 菜单层级
     */
    private Integer calculateMenuLevel(Long parentId) {
        if (parentId == null || parentId == 0L) {
            return 1;
        }
        // 这里简化处理，实际应该查询父菜单的层级
        return 2;
    }
    
    /**
     * 添加子菜单
     * 
     * @param child 子菜单
     */
    public void addChild(Menu child) {
        if (child == null) {
            return;
        }
        
        if (!children.contains(child)) {
            children.add(child);
        }
    }
    
    /**
     * 移除子菜单
     * 
     * @param child 子菜单
     */
    public void removeChild(Menu child) {
        if (child != null) {
            children.remove(child);
        }
    }
    
    /**
     * 判断是否为根菜单
     * 
     * @return true-根菜单，false-子菜单
     */
    public boolean isRoot() {
        return parentId == null || parentId == 0L;
    }
    
    /**
     * 判断是否为叶子菜单
     * 
     * @return true-叶子菜单，false-有子菜单
     */
    public boolean isLeaf() {
        return children.isEmpty();
    }
    
    /**
     * 判断是否有子菜单
     * 
     * @return true-有子菜单，false-无子菜单
     */
    public boolean hasChildren() {
        return !children.isEmpty();
    }
    
    /**
     * 更新菜单信息
     * 
     * @param menuName 菜单名称
     * @param menuPath 菜单路径
     * @param menuIcon 菜单图标
     * @param sortOrder 排序号
     */
    public void updateMenu(String menuName, String menuPath, String menuIcon, Integer sortOrder) {
        this.setMenuName(menuName);
        this.menuPath = menuPath;
        this.menuIcon = menuIcon;
        this.sortOrder = sortOrder != null ? sortOrder : 0;
        this.updateTime = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getMenuName() {
        return menuName;
    }
    
    public String getMenuCode() {
        return menuCode;
    }
    
    public Long getParentId() {
        return parentId;
    }
    
    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }
    
    public String getMenuPath() {
        return menuPath;
    }
    
    public void setMenuPath(String menuPath) {
        this.menuPath = menuPath;
    }
    
    public String getMenuIcon() {
        return menuIcon;
    }
    
    public void setMenuIcon(String menuIcon) {
        this.menuIcon = menuIcon;
    }
    
    public Integer getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    public Integer getMenuLevel() {
        return menuLevel;
    }
    
    public void setMenuLevel(Integer menuLevel) {
        this.menuLevel = menuLevel;
    }
    
    public MenuType getMenuType() {
        return menuType;
    }
    
    public void setMenuType(MenuType menuType) {
        this.menuType = menuType;
    }
    
    public String getPermissionCode() {
        return permissionCode;
    }
    
    public void setPermissionCode(String permissionCode) {
        this.permissionCode = permissionCode;
    }
    
    public UserStatus getStatus() {
        return status;
    }
    
    public void setStatus(UserStatus status) {
        this.status = status;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    public List<Menu> getChildren() {
        return new ArrayList<>(children);
    }
    
    public void setChildren(List<Menu> children) {
        this.children = children != null ? new ArrayList<>(children) : new ArrayList<>();
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Menu menu = (Menu) o;
        return Objects.equals(id, menu.id) &&
               Objects.equals(menuCode, menu.menuCode);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(id, menuCode);
    }
    
    @Override
    public String toString() {
        return "Menu{" +
                "id=" + id +
                ", menuName='" + menuName + '\'' +
                ", menuCode='" + menuCode + '\'' +
                ", parentId=" + parentId +
                ", menuLevel=" + menuLevel +
                ", menuType=" + menuType +
                ", status=" + status +
                '}';
    }
}
