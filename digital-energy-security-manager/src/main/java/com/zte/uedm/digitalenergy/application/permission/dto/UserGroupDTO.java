package com.zte.uedm.digitalenergy.application.permission.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户组数据传输对象
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class UserGroupDTO {
    
    /**
     * 用户组ID
     */
    private Long id;
    
    /**
     * 用户组名称
     */
    @NotBlank(message = "用户组名称不能为空")
    @Size(max = 50, message = "用户组名称不能超过50个字符")
    private String groupName;
    
    /**
     * 用户组编码
     */
    @NotBlank(message = "用户组编码不能为空")
    @Size(max = 50, message = "用户组编码不能超过50个字符")
    private String groupCode;
    
    /**
     * 用户组描述
     */
    @Size(max = 200, message = "用户组描述不能超过200个字符")
    private String groupDescription;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 成员用户ID列表
     */
    private List<Long> memberIds;
    
    /**
     * 成员用户列表（用于展示）
     */
    private List<UserDTO> members;
    
    /**
     * 角色ID列表
     */
    private List<Long> roleIds;
    
    /**
     * 角色列表（用于展示）
     */
    private List<RoleDTO> roles;
    
    /**
     * 用户组菜单权限列表
     */
    private List<MenuDTO> menus;
    
    /**
     * 成员数量
     */
    private Integer memberCount;
    
    /**
     * 角色数量
     */
    private Integer roleCount;
}
