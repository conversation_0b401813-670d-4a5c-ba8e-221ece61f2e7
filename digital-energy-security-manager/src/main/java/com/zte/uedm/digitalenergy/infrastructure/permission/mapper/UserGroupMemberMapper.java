package com.zte.uedm.digitalenergy.infrastructure.permission.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户组成员关系数据访问接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface UserGroupMemberMapper {
    
    /**
     * 插入用户组成员关系
     * 
     * @param userGroupId 用户组ID
     * @param userId 用户ID
     * @return 影响行数
     */
    int insertUserGroupMember(@Param("userGroupId") Long userGroupId, 
                              @Param("userId") Long userId);
    
    /**
     * 根据用户组ID删除成员关系
     * 
     * @param userGroupId 用户组ID
     * @return 影响行数
     */
    int deleteByUserGroupId(@Param("userGroupId") Long userGroupId);
    
    /**
     * 根据用户ID删除成员关系
     * 
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteByUserId(@Param("userId") Long userId);
    
    /**
     * 删除指定用户组的指定成员
     * 
     * @param userGroupId 用户组ID
     * @param userId 用户ID
     * @return 影响行数
     */
    int deleteByUserGroupIdAndUserId(@Param("userGroupId") Long userGroupId, 
                                     @Param("userId") Long userId);
    
    /**
     * 根据用户组ID查询成员用户ID列表
     * 
     * @param userGroupId 用户组ID
     * @return 用户ID列表
     */
    List<Long> selectUserIdsByUserGroupId(@Param("userGroupId") Long userGroupId);
    
    /**
     * 根据用户ID查询所属用户组ID列表
     * 
     * @param userId 用户ID
     * @return 用户组ID列表
     */
    List<Long> selectUserGroupIdsByUserId(@Param("userId") Long userId);
    
    /**
     * 统计用户组成员数量
     * 
     * @param userGroupId 用户组ID
     * @return 成员数量
     */
    long countMembersByUserGroupId(@Param("userGroupId") Long userGroupId);
    
    /**
     * 统计用户所属用户组数量
     * 
     * @param userId 用户ID
     * @return 用户组数量
     */
    long countUserGroupsByUserId(@Param("userId") Long userId);
    
    /**
     * 检查用户是否属于指定用户组
     * 
     * @param userId 用户ID
     * @param userGroupId 用户组ID
     * @return 数量（0-不属于，>0-属于）
     */
    long countByUserIdAndUserGroupId(@Param("userId") Long userId, 
                                     @Param("userGroupId") Long userGroupId);
    
    /**
     * 批量插入用户组成员关系
     * 
     * @param userGroupId 用户组ID
     * @param userIds 用户ID列表
     * @return 影响行数
     */
    int batchInsertUserGroupMembers(@Param("userGroupId") Long userGroupId, 
                                    @Param("userIds") List<Long> userIds);
    
    /**
     * 批量删除用户组成员关系
     * 
     * @param userGroupId 用户组ID
     * @param userIds 用户ID列表
     * @return 影响行数
     */
    int batchDeleteUserGroupMembers(@Param("userGroupId") Long userGroupId, 
                                    @Param("userIds") List<Long> userIds);
}
