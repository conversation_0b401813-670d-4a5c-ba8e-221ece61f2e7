package com.zte.uedm.digitalenergy.application.permission.service;

import com.zte.uedm.digitalenergy.application.permission.assembler.PermissionAssembler;
import com.zte.uedm.digitalenergy.application.permission.dto.*;
import com.zte.uedm.digitalenergy.common.exception.PermissionException;
import com.zte.uedm.digitalenergy.common.response.ResultCode;
import com.zte.uedm.digitalenergy.domain.permission.entity.User;
import com.zte.uedm.digitalenergy.domain.permission.repository.UserRepository;
import com.zte.uedm.digitalenergy.domain.permission.service.PermissionDomainService;
import com.zte.uedm.digitalenergy.domain.permission.service.UacService;
import com.zte.uedm.digitalenergy.domain.permission.valueobject.Organization;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 用户应用服务
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class UserApplicationService {
    
    private static final Logger logger = LoggerFactory.getLogger(UserApplicationService.class);
    
    @Autowired
    private UserRepository userRepository;
    
    @Autowired
    private PermissionDomainService permissionDomainService;
    
    @Autowired
    private UacService uacService;
    
    @Autowired
    private PermissionAssembler permissionAssembler;
    
    /**
     * 创建用户
     * 
     * @param userDTO 用户DTO
     * @param createBy 创建人
     * @return 用户DTO
     */
    @Transactional
    public UserDTO createUser(UserDTO userDTO, String createBy) {
        logger.info("Creating user: {}", userDTO.getUserCode());
        
        // 验证用户工号唯一性
        validateUserCodeUniqueness(userDTO.getUserCode(), null);
        
        // 通过UAC验证用户身份
        UserValidationDTO validationResult = uacService.validateUser(userDTO.getUserCode());
        if (!validationResult.isValid()) {
            throw new PermissionException(ResultCode.USER_NOT_FOUND_IN_UAC.getCode(),
                    "User not found in UAC system: " + userDTO.getUserCode());
        }
        
        // 创建用户实体
        Organization organization = new Organization(validationResult.getOrganization());
        User user = new User(validationResult.getUsername(), userDTO.getUserCode(), organization);
        user.setCreateBy(createBy);
        
        // 分配角色
        if (userDTO.getRoleIds() != null && !userDTO.getRoleIds().isEmpty()) {
            user.setRoles(userDTO.getRoleIds());
        }
        
        // 保存用户
        User savedUser = userRepository.save(user);
        
        logger.info("User created successfully with id: {}", savedUser.getId());
        
        return permissionAssembler.toUserDTO(savedUser);
    }
    
    /**
     * 更新用户
     * 
     * @param id 用户ID
     * @param userDTO 用户DTO
     * @param updateBy 更新人
     * @return 用户DTO
     */
    @Transactional
    public UserDTO updateUser(Long id, UserDTO userDTO, String updateBy) {
        logger.info("Updating user: {}", id);
        
        // 查找用户
        User user = findUserById(id);
        
        // 更新角色分配
        if (userDTO.getRoleIds() != null) {
            user.setRoles(userDTO.getRoleIds());
        }
        
        // 更新用户信息
        user.updateUser(updateBy);
        
        // 保存用户
        User savedUser = userRepository.save(user);
        
        logger.info("User updated successfully: {}", id);
        
        return permissionAssembler.toUserDTO(savedUser);
    }
    
    /**
     * 删除用户
     * 
     * @param id 用户ID
     */
    @Transactional
    public void deleteUser(Long id) {
        logger.info("Deleting user: {}", id);
        
        // 查找用户
        User user = findUserById(id);
        
        // 删除用户（会级联删除用户角色关系和用户组成员关系）
        userRepository.delete(user);
        
        logger.info("User deleted successfully: {}", id);
    }
    
    /**
     * 批量删除用户
     * 
     * @param ids 用户ID列表
     */
    @Transactional
    public void deleteUsers(List<Long> ids) {
        logger.info("Deleting users: {}", ids);
        
        for (Long id : ids) {
            deleteUser(id);
        }
        
        logger.info("Users deleted successfully, count: {}", ids.size());
    }
    
    /**
     * 根据ID查询用户
     * 
     * @param id 用户ID
     * @return 用户DTO
     */
    public UserDTO getUserById(Long id) {
        logger.debug("Getting user by id: {}", id);
        
        User user = findUserById(id);
        return permissionAssembler.toUserDTO(user);
    }
    
    /**
     * 根据用户工号查询用户
     * 
     * @param userCode 用户工号
     * @return 用户DTO
     */
    public UserDTO getUserByCode(String userCode) {
        logger.debug("Getting user by code: {}", userCode);
        
        Optional<User> optional = userRepository.findByUserCode(userCode);
        if (!optional.isPresent()) {
            throw PermissionException.userNotFound(userCode);
        }
        
        return permissionAssembler.toUserDTO(optional.get());
    }
    
    /**
     * 分页查询用户列表
     *
     * @param pageQuery 分页查询参数
     * @param username 用户名（可选）
     * @param organization 组织机构（可选）
     * @return 分页结果
     */
    public PageResultDTO<UserDTO> getUsersByPage(PageQueryDTO pageQuery, String username, String organization) {
        logger.debug("Getting users by page: pageNum={}, pageSize={}, username={}, organization={}",
                    pageQuery.getPageNum(), pageQuery.getPageSize(), username, organization);

        // 查询用户列表
        List<User> users = userRepository.findByPage(
                pageQuery.getPageNum(),
                pageQuery.getPageSize(),
                username,
                null, // userCode参数
                organization
        );

        // 统计总数
        long total = userRepository.countUsers(username, null, organization);

        // 转换为DTO
        List<UserDTO> userDTOs = permissionAssembler.toUserDTOList(users);

        return new PageResultDTO<>(pageQuery.getPageNum(), pageQuery.getPageSize(), total, userDTOs);
    }
    
    /**
     * 查询所有用户
     * 
     * @return 用户DTO列表
     */
    public List<UserDTO> getAllUsers() {
        logger.debug("Getting all users");
        
        List<User> users = userRepository.findAll();
        return permissionAssembler.toUserDTOList(users);
    }
    
    /**
     * 获取用户权限继承信息
     *
     * @param userId 用户ID
     * @return 用户权限继承DTO
     */
    public UserPermissionInheritanceDTO getUserPermissionInheritance(Long userId) {
        logger.debug("Getting user permission inheritance for user: {}", userId);

        com.zte.uedm.digitalenergy.domain.permission.service.PermissionDomainService.UserPermissionInheritance inheritance =
                permissionDomainService.getUserPermissionInheritance(userId);
        return permissionAssembler.toUserPermissionInheritanceDTO(inheritance);
    }
    
    /**
     * 验证用户身份
     * 
     * @param userCode 用户工号
     * @return 用户验证结果
     */
    public UserValidationDTO validateUser(String userCode) {
        logger.debug("Validating user: {}", userCode);
        
        return uacService.validateUser(userCode);
    }
    
    /**
     * 查找用户实体
     * 
     * @param id 用户ID
     * @return 用户实体
     */
    private User findUserById(Long id) {
        Optional<User> optional = userRepository.findById(id);
        if (!optional.isPresent()) {
            throw PermissionException.userNotFound(id);
        }
        return optional.get();
    }
    
    /**
     * 验证用户工号唯一性
     * 
     * @param userCode 用户工号
     * @param excludeId 排除的用户ID
     */
    private void validateUserCodeUniqueness(String userCode, Long excludeId) {
        if (excludeId == null) {
            if (userRepository.existsByUserCode(userCode)) {
                throw PermissionException.userAlreadyExists(userCode);
            }
        } else {
            if (userRepository.existsByUserCodeAndIdNot(userCode, excludeId)) {
                throw PermissionException.userAlreadyExists(userCode);
            }
        }
    }
}
