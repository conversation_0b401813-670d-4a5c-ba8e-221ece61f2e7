package com.zte.uedm.digitalenergy.infrastructure.permission.external;

import com.zte.uedm.digitalenergy.application.permission.dto.UserValidationDTO;
import com.zte.uedm.digitalenergy.domain.permission.service.UacService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.RestClientException;

import java.util.List;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * UAC用户验证服务实现
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class UacServiceImpl implements UacService {
    
    private static final Logger logger = LoggerFactory.getLogger(UacServiceImpl.class);
    
    @Value("${uac.api.base-url:http://localhost:8080/uac}")
    private String uacBaseUrl;
    
    @Value("${uac.api.timeout:5000}")
    private int timeout;
    
    @Value("${uac.api.enabled:false}")
    private boolean uacEnabled;
    
    private final RestTemplate restTemplate;
    
    public UacServiceImpl() {
        this.restTemplate = new RestTemplate();
        // 配置超时时间等
    }
    
    @Override
    public UserValidationDTO validateUser(String userCode) {
        logger.debug("Validating user through UAC: {}", userCode);
        
        if (!uacEnabled) {
            // UAC未启用时，返回模拟数据用于开发测试
            return createMockValidationResult(userCode);
        }
        
        try {
            // 调用UAC API验证用户
            String url = uacBaseUrl + "/api/users/validate";
            Map<String, String> request = new HashMap<>();
            request.put("userCode", userCode);
            
            // 这里应该调用实际的UAC API
            // UacUserResponse response = restTemplate.postForObject(url, request, UacUserResponse.class);
            
            // 暂时返回模拟数据
            return createMockValidationResult(userCode);
            
        } catch (RestClientException e) {
            logger.error("Failed to validate user through UAC: {}", userCode, e);
            return UserValidationDTO.failure(userCode, "UAC service unavailable: " + e.getMessage());
        }
    }
    
    @Override
    public List<UserValidationDTO> validateUsers(List<String> userCodes) {
        logger.debug("Batch validating users through UAC: {}", userCodes);
        
        List<UserValidationDTO> results = new ArrayList<>();
        for (String userCode : userCodes) {
            results.add(validateUser(userCode));
        }
        
        return results;
    }
    
    @Override
    public boolean userExists(String userCode) {
        logger.debug("Checking if user exists in UAC: {}", userCode);
        
        UserValidationDTO result = validateUser(userCode);
        return result.isValid();
    }
    
    @Override
    public UserValidationDTO getUserDetails(String userCode) {
        logger.debug("Getting user details from UAC: {}", userCode);
        
        if (!uacEnabled) {
            return createMockValidationResult(userCode);
        }
        
        try {
            // 调用UAC API获取用户详细信息
            String url = uacBaseUrl + "/api/users/" + userCode;
            
            // 这里应该调用实际的UAC API
            // UacUserDetailResponse response = restTemplate.getForObject(url, UacUserDetailResponse.class);
            
            // 暂时返回模拟数据
            return createMockValidationResult(userCode);
            
        } catch (RestClientException e) {
            logger.error("Failed to get user details from UAC: {}", userCode, e);
            return UserValidationDTO.failure(userCode, "UAC service unavailable: " + e.getMessage());
        }
    }
    
    /**
     * 创建模拟验证结果（用于开发测试）
     * 
     * @param userCode 用户工号
     * @return 模拟验证结果
     */
    private UserValidationDTO createMockValidationResult(String userCode) {
        logger.debug("Creating mock validation result for user: {}", userCode);
        
        // 模拟一些测试用户
        if ("admin".equals(userCode) || "test001".equals(userCode) || "test002".equals(userCode)) {
            UserValidationDTO result = UserValidationDTO.success(
                userCode, 
                "测试用户" + userCode, 
                "中兴通讯股份有限公司/研发部门"
            );
            result.setEmployeeType("正式员工");
            result.setDepartmentCode("RD001");
            result.setDepartmentName("研发部门");
            result.setEmail(userCode + "@zte.com.cn");
            result.setMobile("138****" + userCode.substring(Math.max(0, userCode.length() - 4)));
            return result;
        }
        
        // 其他用户返回验证失败
        return UserValidationDTO.failure(userCode, "User not found in UAC system");
    }
    
    /**
     * UAC用户响应对象（示例）
     */
    private static class UacUserResponse {
        private String userCode;
        private String username;
        private String organization;
        private boolean valid;
        private String errorMessage;
        
        // getters and setters
    }
    
    /**
     * UAC用户详细信息响应对象（示例）
     */
    private static class UacUserDetailResponse extends UacUserResponse {
        private String employeeType;
        private String departmentCode;
        private String departmentName;
        private String email;
        private String mobile;
        
        // getters and setters
    }
}
