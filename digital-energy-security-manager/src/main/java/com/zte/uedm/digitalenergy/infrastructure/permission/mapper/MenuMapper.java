package com.zte.uedm.digitalenergy.infrastructure.permission.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zte.uedm.digitalenergy.infrastructure.permission.po.MenuPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 菜单映射器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface MenuMapper extends BaseMapper<MenuPO> {
    
    /**
     * 根据菜单编码查询菜单
     * 
     * @param menuCode 菜单编码
     * @return 菜单
     */
    @Select("SELECT * FROM t_menu WHERE menu_code = #{menuCode}")
    MenuPO selectByMenuCode(@Param("menuCode") String menuCode);
    
    /**
     * 根据权限编码查询菜单
     * 
     * @param permissionCode 权限编码
     * @return 菜单
     */
    @Select("SELECT * FROM t_menu WHERE permission_code = #{permissionCode}")
    MenuPO selectByPermissionCode(@Param("permissionCode") String permissionCode);
    
    /**
     * 查询所有菜单
     *
     * @return 菜单列表
     */
    @Select("SELECT * FROM t_menu ORDER BY menu_level, sort_order")
    List<MenuPO> selectAllEnabled();
    
    /**
     * 根据父菜单ID查询子菜单列表
     * 
     * @param parentId 父菜单ID
     * @return 子菜单列表
     */
    @Select("SELECT * FROM t_menu WHERE parent_id = #{parentId} ORDER BY sort_order")
    List<MenuPO> selectByParentId(@Param("parentId") Long parentId);
    
    /**
     * 查询根菜单列表
     * 
     * @return 根菜单列表
     */
    @Select("SELECT * FROM t_menu WHERE parent_id = 0 ORDER BY sort_order")
    List<MenuPO> selectRootMenus();
    
    /**
     * 根据菜单类型查询菜单列表
     * 
     * @param menuType 菜单类型
     * @return 菜单列表
     */
    @Select("SELECT * FROM t_menu WHERE menu_type = #{menuType} ORDER BY menu_level, sort_order")
    List<MenuPO> selectByMenuType(@Param("menuType") String menuType);
    
    /**
     * 根据菜单层级查询菜单列表
     *
     * @param level 菜单层级
     * @return 菜单列表
     */
    @Select("SELECT * FROM t_menu WHERE menu_level = #{level} ORDER BY sort_order")
    List<MenuPO> selectByLevel(@Param("level") Integer level);
    
    /**
     * 根据用户ID查询用户有权限的菜单列表
     * 
     * @param userId 用户ID
     * @return 菜单列表
     */
    @Select({
        "SELECT DISTINCT m.* FROM t_menu m",
        "INNER JOIN t_role_menu rm ON m.id = rm.menu_id",
        "INNER JOIN (",
        "  SELECT ur.role_id FROM t_user_role ur WHERE ur.user_id = #{userId}",
        "  UNION",
        "  SELECT ugr.role_id FROM t_user_group_member ugm",
        "  INNER JOIN t_user_group_role ugr ON ugm.user_group_id = ugr.user_group_id",
        "  WHERE ugm.user_id = #{userId}",
        ") AS user_roles ON rm.role_id = user_roles.role_id",
        "ORDER BY m.menu_level, m.sort_order"
    })
    List<MenuPO> selectMenusByUserId(@Param("userId") Long userId);
    
    /**
     * 根据角色ID查询角色有权限的菜单列表
     * 
     * @param roleId 角色ID
     * @return 菜单列表
     */
    @Select({
        "SELECT m.* FROM t_menu m",
        "INNER JOIN t_role_menu rm ON m.id = rm.menu_id",
        "WHERE rm.role_id = #{roleId} AND m.status = 1",
        "ORDER BY m.menu_level, m.sort_order"
    })
    List<MenuPO> selectMenusByRoleId(@Param("roleId") Long roleId);
    
    /**
     * 根据角色ID列表查询菜单列表
     * 
     * @param roleIds 角色ID列表
     * @return 菜单列表
     */
    List<MenuPO> selectMenusByRoleIds(@Param("roleIds") List<Long> roleIds);
    
    /**
     * 检查菜单编码是否存在
     * 
     * @param menuCode 菜单编码
     * @return 存在数量
     */
    @Select("SELECT COUNT(*) FROM t_menu WHERE menu_code = #{menuCode}")
    int countByMenuCode(@Param("menuCode") String menuCode);
    
    /**
     * 检查菜单名称是否存在
     * 
     * @param menuName 菜单名称
     * @return 存在数量
     */
    @Select("SELECT COUNT(*) FROM t_menu WHERE menu_name = #{menuName}")
    int countByMenuName(@Param("menuName") String menuName);
    
    /**
     * 检查菜单是否有子菜单
     * 
     * @param menuId 菜单ID
     * @return 子菜单数量
     */
    @Select("SELECT COUNT(*) FROM t_menu WHERE parent_id = #{menuId}")
    int countChildren(@Param("menuId") Long menuId);
    
    /**
     * 查询菜单的所有子菜单ID（递归）
     * 
     * @param menuId 菜单ID
     * @return 子菜单ID列表
     */
    List<Long> selectAllChildrenIds(@Param("menuId") Long menuId);
}
