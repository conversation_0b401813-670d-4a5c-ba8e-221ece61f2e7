package com.zte.uedm.digitalenergy.application.permission.dto;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.List;

/**
 * 分页查询数据传输对象
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
public class PageQueryDTO {
    
    /**
     * 页码，从1开始
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;
    
    /**
     * 页大小
     */
    @Min(value = 1, message = "页大小必须大于0")
    @Max(value = 100, message = "页大小不能超过100")
    private Integer pageSize = 10;
    
    /**
     * 排序字段
     */
    private String sortField;
    
    /**
     * 排序方向：ASC-升序，DESC-降序
     */
    private String sortOrder = "DESC";
}

/**
 * 分页结果数据传输对象
 * 
 * @param <T> 数据类型
 */
@Data
public class PageResultDTO<T> {
    
    /**
     * 当前页码
     */
    private Integer pageNum;
    
    /**
     * 页大小
     */
    private Integer pageSize;
    
    /**
     * 总记录数
     */
    private Long total;
    
    /**
     * 总页数
     */
    private Integer pages;
    
    /**
     * 数据列表
     */
    private List<T> list;
    
    /**
     * 是否有下一页
     */
    private Boolean hasNext;
    
    /**
     * 是否有上一页
     */
    private Boolean hasPrevious;
    
    /**
     * 构造函数
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param total 总记录数
     * @param list 数据列表
     */
    public PageResultDTO(Integer pageNum, Integer pageSize, Long total, List<T> list) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.total = total;
        this.list = list;
        
        // 计算总页数
        this.pages = (int) Math.ceil((double) total / pageSize);
        
        // 计算是否有下一页和上一页
        this.hasNext = pageNum < pages;
        this.hasPrevious = pageNum > 1;
    }
    
    /**
     * 创建空的分页结果
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param <T> 数据类型
     * @return 空的分页结果
     */
    public static <T> PageResultDTO<T> empty(Integer pageNum, Integer pageSize) {
        return new PageResultDTO<>(pageNum, pageSize, 0L, List.of());
    }
}
