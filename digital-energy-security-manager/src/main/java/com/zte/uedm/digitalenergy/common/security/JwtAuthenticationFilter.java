package com.zte.uedm.digitalenergy.common.security;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * JWT认证过滤器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class JwtAuthenticationFilter extends OncePerRequestFilter {
    
    private static final Logger logger = LoggerFactory.getLogger(JwtAuthenticationFilter.class);
    
    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String BEARER_PREFIX = "Bearer ";
    private static final String USER_CODE_HEADER = "X-User-Code";
    private static final String USER_NAME_HEADER = "X-User-Name";
    private static final String USER_ROLES_HEADER = "X-User-Roles";
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        try {
            // 从请求头中获取JWT Token
            String token = extractToken(request);
            
            if (StringUtils.hasText(token)) {
                // 验证并解析Token（这里简化处理，实际项目中需要验证JWT签名）
                authenticateUser(request, token);
            } else {
                // 尝试从请求头中获取用户信息（用于开发测试）
                authenticateFromHeaders(request);
            }
            
        } catch (Exception e) {
            logger.error("JWT authentication failed", e);
            // 认证失败时清除Security Context
            SecurityContextHolder.clearContext();
        }
        
        filterChain.doFilter(request, response);
    }
    
    /**
     * 从请求头中提取Token
     * 
     * @param request HTTP请求
     * @return JWT Token
     */
    private String extractToken(HttpServletRequest request) {
        String bearerToken = request.getHeader(AUTHORIZATION_HEADER);
        
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith(BEARER_PREFIX)) {
            return bearerToken.substring(BEARER_PREFIX.length());
        }
        
        return null;
    }
    
    /**
     * 验证并认证用户
     * 
     * @param request HTTP请求
     * @param token JWT Token
     */
    private void authenticateUser(HttpServletRequest request, String token) {
        // 这里应该验证JWT Token的签名和有效性
        // 并从Token中解析用户信息和权限
        // 为了简化，这里直接从请求头获取用户信息
        
        String userCode = request.getHeader(USER_CODE_HEADER);
        String userName = request.getHeader(USER_NAME_HEADER);
        String userRoles = request.getHeader(USER_ROLES_HEADER);
        
        if (StringUtils.hasText(userCode)) {
            // 解析用户角色
            List<SimpleGrantedAuthority> authorities = parseAuthorities(userRoles);
            
            // 创建认证对象
            UserPrincipal principal = new UserPrincipal(userCode, userName, authorities);
            UsernamePasswordAuthenticationToken authentication = 
                new UsernamePasswordAuthenticationToken(principal, null, authorities);
            
            // 设置到Security Context
            SecurityContextHolder.getContext().setAuthentication(authentication);
            
            logger.debug("User authenticated: {}", userCode);
        }
    }
    
    /**
     * 从请求头中认证用户（用于开发测试）
     * 
     * @param request HTTP请求
     */
    private void authenticateFromHeaders(HttpServletRequest request) {
        String userCode = request.getHeader(USER_CODE_HEADER);
        String userName = request.getHeader(USER_NAME_HEADER);
        String userRoles = request.getHeader(USER_ROLES_HEADER);
        
        if (StringUtils.hasText(userCode)) {
            // 解析用户角色
            List<SimpleGrantedAuthority> authorities = parseAuthorities(userRoles);
            
            // 创建认证对象
            UserPrincipal principal = new UserPrincipal(userCode, userName, authorities);
            UsernamePasswordAuthenticationToken authentication = 
                new UsernamePasswordAuthenticationToken(principal, null, authorities);
            
            // 设置到Security Context
            SecurityContextHolder.getContext().setAuthentication(authentication);
            
            logger.debug("User authenticated from headers: {}", userCode);
        }
    }
    
    /**
     * 解析用户权限
     * 
     * @param rolesStr 角色字符串，用逗号分隔
     * @return 权限列表
     */
    private List<SimpleGrantedAuthority> parseAuthorities(String rolesStr) {
        if (!StringUtils.hasText(rolesStr)) {
            return List.of();
        }
        
        return Arrays.stream(rolesStr.split(","))
                .map(String::trim)
                .filter(StringUtils::hasText)
                .map(role -> new SimpleGrantedAuthority("ROLE_" + role))
                .collect(Collectors.toList());
    }
    
    /**
     * 用户主体信息
     */
    public static class UserPrincipal {
        private final String userCode;
        private final String userName;
        private final List<SimpleGrantedAuthority> authorities;
        
        public UserPrincipal(String userCode, String userName, List<SimpleGrantedAuthority> authorities) {
            this.userCode = userCode;
            this.userName = userName;
            this.authorities = authorities;
        }
        
        public String getUserCode() {
            return userCode;
        }
        
        public String getUserName() {
            return userName;
        }
        
        public List<SimpleGrantedAuthority> getAuthorities() {
            return authorities;
        }
        
        @Override
        public String toString() {
            return "UserPrincipal{" +
                    "userCode='" + userCode + '\'' +
                    ", userName='" + userName + '\'' +
                    ", authorities=" + authorities +
                    '}';
        }
    }
}
