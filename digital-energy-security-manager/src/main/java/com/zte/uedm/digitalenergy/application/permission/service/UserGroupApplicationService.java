package com.zte.uedm.digitalenergy.application.permission.service;

import com.zte.uedm.digitalenergy.application.permission.assembler.PermissionAssembler;
import com.zte.uedm.digitalenergy.application.permission.dto.PageQueryDTO;
import com.zte.uedm.digitalenergy.application.permission.dto.PageResultDTO;
import com.zte.uedm.digitalenergy.application.permission.dto.UserDTO;
import com.zte.uedm.digitalenergy.application.permission.dto.UserGroupDTO;
import com.zte.uedm.digitalenergy.domain.permission.entity.User;
import com.zte.uedm.digitalenergy.domain.permission.entity.UserGroup;
import com.zte.uedm.digitalenergy.domain.permission.repository.UserGroupRepository;
import com.zte.uedm.digitalenergy.domain.permission.repository.UserRepository;
import com.zte.uedm.digitalenergy.common.exception.PermissionException;
import com.zte.uedm.digitalenergy.common.response.ResultCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 用户组应用服务
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class UserGroupApplicationService {

    private static final Logger logger = LoggerFactory.getLogger(UserGroupApplicationService.class);

    @Autowired
    private UserGroupRepository userGroupRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PermissionAssembler permissionAssembler;

    /**
     * 创建用户组
     *
     * @param userGroupDTO 用户组DTO
     * @param createBy     创建人
     * @return 用户组DTO
     */
    @Transactional
    public UserGroupDTO createUserGroup(UserGroupDTO userGroupDTO, String createBy) {
        logger.info("Creating user group: {}", userGroupDTO.getGroupCode());

        // 验证用户组编码唯一性
        validateGroupCodeUniqueness(userGroupDTO.getGroupCode(), null);

        // 检查用户组数量限制
        validateUserGroupLimit();

        // 创建用户组实体
        UserGroup userGroup = new UserGroup(
                userGroupDTO.getGroupName(),
                userGroupDTO.getGroupCode(),
                userGroupDTO.getGroupDescription()
        );
        userGroup.setCreateBy(createBy);

        // 添加成员
        if (userGroupDTO.getMemberIds() != null && !userGroupDTO.getMemberIds().isEmpty()) {
            for (Long userId : userGroupDTO.getMemberIds()) {
                validateUserExists(userId);
                userGroup.addMember(userId);
            }
        }

        // 分配角色
        if (userGroupDTO.getRoleIds() != null && !userGroupDTO.getRoleIds().isEmpty()) {
            for (Long roleId : userGroupDTO.getRoleIds()) {
                userGroup.assignRole(roleId);
            }
        }

        // 保存用户组
        UserGroup savedUserGroup = userGroupRepository.save(userGroup);

        logger.info("User group created successfully with id: {}", savedUserGroup.getId());

        return permissionAssembler.toUserGroupDTO(savedUserGroup);
    }

    /**
     * 更新用户组
     *
     * @param id           用户组ID
     * @param userGroupDTO 用户组DTO
     * @param updateBy     更新人
     * @return 用户组DTO
     */
    @Transactional
    public UserGroupDTO updateUserGroup(Long id, UserGroupDTO userGroupDTO, String updateBy) {
        logger.info("Updating user group: {}", id);

        // 查找用户组
        UserGroup userGroup = findUserGroupById(id);

        // 验证用户组名称唯一性（排除当前用户组）
        if (!userGroup.getGroupName().equals(userGroupDTO.getGroupName())) {
            if (userGroupRepository.existsByGroupNameAndIdNot(userGroupDTO.getGroupName(), id)) {
                throw PermissionException.userGroupAlreadyExists(userGroupDTO.getGroupName());
            }
        }

        // 更新用户组信息
        userGroup.updateUserGroup(userGroupDTO.getGroupDescription(), updateBy);

        // 更新成员
        if (userGroupDTO.getMemberIds() != null) {
            // 清除现有成员
            userGroup.clearMembers();
            // 添加新成员
            for (Long userId : userGroupDTO.getMemberIds()) {
                validateUserExists(userId);
                userGroup.addMember(userId);
            }
        }

        // 更新角色分配
        if (userGroupDTO.getRoleIds() != null) {
            // 清除现有角色
            userGroup.clearRoles();
            // 分配新角色
            for (Long roleId : userGroupDTO.getRoleIds()) {
                userGroup.assignRole(roleId);
            }
        }

        // 保存用户组
        UserGroup savedUserGroup = userGroupRepository.save(userGroup);

        logger.info("User group updated successfully: {}", id);

        return permissionAssembler.toUserGroupDTO(savedUserGroup);
    }

    /**
     * 删除用户组
     *
     * @param id 用户组ID
     */
    @Transactional
    public void deleteUserGroup(Long id) {
        logger.info("Deleting user group: {}", id);

        // 查找用户组
        UserGroup userGroup = findUserGroupById(id);

        // 删除用户组（会级联删除用户组成员关系和角色关系）
        userGroupRepository.delete(userGroup);

        logger.info("User group deleted successfully: {}", id);
    }

    /**
     * 批量删除用户组
     *
     * @param ids 用户组ID列表
     */
    @Transactional
    public void deleteUserGroups(List<Long> ids) {
        logger.info("Deleting user groups: {}", ids);

        for (Long id : ids) {
            deleteUserGroup(id);
        }

        logger.info("User groups deleted successfully, count: {}", ids.size());
    }

    /**
     * 根据ID查询用户组
     *
     * @param id 用户组ID
     * @return 用户组DTO
     */
    public UserGroupDTO getUserGroupById(Long id) {
        logger.debug("Getting user group by id: {}", id);

        UserGroup userGroup = findUserGroupById(id);
        return permissionAssembler.toUserGroupDTO(userGroup);
    }

    /**
     * 根据用户组编码查询用户组
     *
     * @param groupCode 用户组编码
     * @return 用户组DTO
     */
    public UserGroupDTO getUserGroupByCode(String groupCode) {
        logger.debug("Getting user group by code: {}", groupCode);

        Optional<UserGroup> optional = userGroupRepository.findByGroupCode(groupCode);
        if (!optional.isPresent()) {
            throw PermissionException.userGroupNotFound(groupCode);
        }

        return permissionAssembler.toUserGroupDTO(optional.get());
    }

    /**
     * 分页查询用户组列表
     *
     * @param pageQuery 分页查询参数
     * @param groupName 用户组名称（可选）
     * @return 分页结果
     */
    public PageResultDTO<UserGroupDTO> getUserGroupsByPage(PageQueryDTO pageQuery, String groupName) {
        logger.debug("Getting user groups by page: pageNum={}, pageSize={}, groupName={}",
                pageQuery.getPageNum(), pageQuery.getPageSize(), groupName);

        // 查询用户组列表
        List<UserGroup> userGroups = userGroupRepository.findByPage(
                pageQuery.getPageNum(),
                pageQuery.getPageSize(),
                groupName
        );

        // 统计总数
        long total = userGroupRepository.countUserGroups(groupName);

        // 转换为DTO
        List<UserGroupDTO> userGroupDTOs = permissionAssembler.toUserGroupDTOList(userGroups);

        return new PageResultDTO<>(pageQuery.getPageNum(), pageQuery.getPageSize(), total, userGroupDTOs);
    }

    /**
     * 查询所有用户组
     *
     * @return 用户组DTO列表
     */
    public List<UserGroupDTO> getAllUserGroups() {
        logger.debug("Getting all user groups");

        List<UserGroup> userGroups = userGroupRepository.findAll();
        return permissionAssembler.toUserGroupDTOList(userGroups);
    }

    /**
     * 查询用户组成员
     *
     * @param id 用户组ID
     * @return 用户DTO列表
     */
    public List<UserDTO> getUserGroupMembers(Long id) {
        logger.debug("Getting user group members for group: {}", id);

        List<User> members = userRepository.findByUserGroupId(id);
        return permissionAssembler.toUserDTOList(members);
    }

    /**
     * 添加用户组成员
     *
     * @param id      用户组ID
     * @param userIds 用户ID列表
     */
    @Transactional
    public void addMembers(Long id, List<Long> userIds) {
        logger.info("Adding members to user group: {}, members: {}", id, userIds);

        // 查找用户组
        UserGroup userGroup = findUserGroupById(id);

        // 验证用户存在并添加成员
        for (Long userId : userIds) {
            validateUserExists(userId);
            userGroup.addMember(userId);
        }

        // 保存用户组
        userGroupRepository.save(userGroup);

        logger.info("Members added successfully to user group: {}", id);
    }

    /**
     * 移除用户组成员
     *
     * @param id     用户组ID
     * @param userId 用户ID
     */
    @Transactional
    public void removeMember(Long id, Long userId) {
        logger.info("Removing member from user group: {}, member: {}", id, userId);

        // 查找用户组
        UserGroup userGroup = findUserGroupById(id);

        // 移除成员
        userGroup.removeMember(userId);

        // 保存用户组
        userGroupRepository.save(userGroup);

        logger.info("Member removed successfully from user group: {}", id);
    }

    /**
     * 为用户组分配角色
     *
     * @param id      用户组ID
     * @param roleIds 角色ID列表
     */
    @Transactional
    public void assignRoles(Long id, List<Long> roleIds) {
        logger.info("Assigning roles to user group: {}, roles: {}", id, roleIds);

        // 查找用户组
        UserGroup userGroup = findUserGroupById(id);

        // 分配角色
        for (Long roleId : roleIds) {
            userGroup.assignRole(roleId);
        }

        // 保存用户组
        userGroupRepository.save(userGroup);

        logger.info("Roles assigned successfully to user group: {}", id);
    }

    /**
     * 取消用户组角色分配
     *
     * @param id     用户组ID
     * @param roleId 角色ID
     */
    @Transactional
    public void unassignRole(Long id, Long roleId) {
        logger.info("Unassigning role from user group: {}, role: {}", id, roleId);

        // 查找用户组
        UserGroup userGroup = findUserGroupById(id);

        // 取消角色分配
        userGroup.unassignRole(roleId);

        // 保存用户组
        userGroupRepository.save(userGroup);

        logger.info("Role unassigned successfully from user group: {}", id);
    }

    /**
     * 查找用户组实体
     *
     * @param id 用户组ID
     * @return 用户组实体
     */
    private UserGroup findUserGroupById(Long id) {
        Optional<UserGroup> optional = userGroupRepository.findById(id);
        if (!optional.isPresent()) {
            throw PermissionException.userGroupNotFound(id);
        }
        return optional.get();
    }

    /**
     * 验证用户组编码唯一性
     *
     * @param groupCode 用户组编码
     * @param excludeId 排除的用户组ID
     */
    private void validateGroupCodeUniqueness(String groupCode, Long excludeId) {
        if (excludeId == null) {
            if (userGroupRepository.existsByGroupCode(groupCode)) {
                throw PermissionException.userGroupAlreadyExists(groupCode);
            }
        } else {
            if (userGroupRepository.existsByGroupCodeAndIdNot(groupCode, excludeId)) {
                throw PermissionException.userGroupAlreadyExists(groupCode);
            }
        }
    }

    /**
     * 验证用户组数量限制
     */
    private void validateUserGroupLimit() {
        long count = userGroupRepository.countUserGroups(null);
        if (count >= UserGroup.MAX_USER_GROUP_COUNT) {
            throw new PermissionException(ResultCode.USER_GROUP_COUNT_LIMIT_EXCEEDED.getCode(),
                    "Cannot create more than " + UserGroup.MAX_USER_GROUP_COUNT + " user groups");
        }
    }

    /**
     * 验证用户是否存在
     *
     * @param userId 用户ID
     */
    private void validateUserExists(Long userId) {
        if (!userRepository.existsById(userId)) {
            throw PermissionException.userNotFound(userId);
        }
    }
}
