package com.zte.uedm.digitalenergy.common.exception;

import com.zte.uedm.digitalenergy.common.response.ApiResponse;
import com.zte.uedm.digitalenergy.common.response.ResultCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);
    
    /**
     * 处理权限管理业务异常
     * 
     * @param e 权限异常
     * @return 响应结果
     */
    @ExceptionHandler(PermissionException.class)
    public ResponseEntity<ApiResponse<Void>> handlePermissionException(PermissionException e) {
        logger.warn("Permission exception occurred: {}", e.getMessage());
        
        ApiResponse<Void> response = ApiResponse.error(e.getCode(), e.getMessage());
        HttpStatus httpStatus = determineHttpStatus(e.getCode());
        
        return new ResponseEntity<>(response, httpStatus);
    }
    
    /**
     * 处理参数验证异常
     * 
     * @param e 方法参数验证异常
     * @return 响应结果
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiResponse<Void>> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        logger.warn("Method argument validation failed: {}", e.getMessage());
        
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        
        ApiResponse<Void> response = ApiResponse.error(ResultCode.VALIDATION_ERROR.getCode(), 
                "Validation failed: " + errorMessage);
        
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }
    
    /**
     * 处理绑定异常
     * 
     * @param e 绑定异常
     * @return 响应结果
     */
    @ExceptionHandler(BindException.class)
    public ResponseEntity<ApiResponse<Void>> handleBindException(BindException e) {
        logger.warn("Bind exception occurred: {}", e.getMessage());
        
        String errorMessage = e.getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        
        ApiResponse<Void> response = ApiResponse.error(ResultCode.VALIDATION_ERROR.getCode(), 
                "Bind validation failed: " + errorMessage);
        
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }
    
    /**
     * 处理约束违反异常
     * 
     * @param e 约束违反异常
     * @return 响应结果
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<ApiResponse<Void>> handleConstraintViolationException(ConstraintViolationException e) {
        logger.warn("Constraint violation exception occurred: {}", e.getMessage());
        
        Set<ConstraintViolation<?>> violations = e.getConstraintViolations();
        String errorMessage = violations.stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        
        ApiResponse<Void> response = ApiResponse.error(ResultCode.VALIDATION_ERROR.getCode(), 
                "Constraint validation failed: " + errorMessage);
        
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }
    
    /**
     * 处理方法参数类型不匹配异常
     * 
     * @param e 方法参数类型不匹配异常
     * @return 响应结果
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public ResponseEntity<ApiResponse<Void>> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException e) {
        logger.warn("Method argument type mismatch: {}", e.getMessage());
        
        String errorMessage = String.format("Parameter '%s' should be of type '%s'", 
                e.getName(), e.getRequiredType() != null ? e.getRequiredType().getSimpleName() : "unknown");
        
        ApiResponse<Void> response = ApiResponse.error(ResultCode.BAD_REQUEST.getCode(), errorMessage);
        
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }
    
    /**
     * 处理数据完整性违反异常
     * 
     * @param e 数据完整性违反异常
     * @return 响应结果
     */
    @ExceptionHandler(DataIntegrityViolationException.class)
    public ResponseEntity<ApiResponse<Void>> handleDataIntegrityViolationException(DataIntegrityViolationException e) {
        logger.error("Data integrity violation occurred", e);
        
        String errorMessage = "Data integrity constraint violation";
        if (e.getCause() != null && e.getCause().getMessage() != null) {
            String causeMessage = e.getCause().getMessage().toLowerCase();
            if (causeMessage.contains("foreign key")) {
                errorMessage = "Cannot delete record due to foreign key constraint";
            } else if (causeMessage.contains("unique")) {
                errorMessage = "Duplicate data detected";
            }
        }
        
        ApiResponse<Void> response = ApiResponse.error(ResultCode.DATA_INTEGRITY_VIOLATION.getCode(), errorMessage);
        
        return new ResponseEntity<>(response, HttpStatus.CONFLICT);
    }
    
    /**
     * 处理重复键异常
     * 
     * @param e 重复键异常
     * @return 响应结果
     */
    @ExceptionHandler(DuplicateKeyException.class)
    public ResponseEntity<ApiResponse<Void>> handleDuplicateKeyException(DuplicateKeyException e) {
        logger.warn("Duplicate key exception occurred: {}", e.getMessage());
        
        ApiResponse<Void> response = ApiResponse.error(ResultCode.DUPLICATE_KEY_ERROR.getCode(), 
                "Duplicate data detected");
        
        return new ResponseEntity<>(response, HttpStatus.CONFLICT);
    }
    
    /**
     * 处理非法参数异常
     * 
     * @param e 非法参数异常
     * @return 响应结果
     */
    @ExceptionHandler(IllegalArgumentException.class)
    public ResponseEntity<ApiResponse<Void>> handleIllegalArgumentException(IllegalArgumentException e) {
        logger.warn("Illegal argument exception occurred: {}", e.getMessage());
        
        ApiResponse<Void> response = ApiResponse.error(ResultCode.BAD_REQUEST.getCode(), e.getMessage());
        
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }
    
    /**
     * 处理非法状态异常
     * 
     * @param e 非法状态异常
     * @return 响应结果
     */
    @ExceptionHandler(IllegalStateException.class)
    public ResponseEntity<ApiResponse<Void>> handleIllegalStateException(IllegalStateException e) {
        logger.warn("Illegal state exception occurred: {}", e.getMessage());
        
        ApiResponse<Void> response = ApiResponse.error(ResultCode.BUSINESS_ERROR.getCode(), e.getMessage());
        
        return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
    }
    
    /**
     * 处理运行时异常
     * 
     * @param e 运行时异常
     * @return 响应结果
     */
    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<ApiResponse<Void>> handleRuntimeException(RuntimeException e) {
        logger.error("Runtime exception occurred", e);
        
        ApiResponse<Void> response = ApiResponse.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), 
                "Internal server error occurred");
        
        return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }
    
    /**
     * 处理通用异常
     * 
     * @param e 异常
     * @return 响应结果
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ApiResponse<Void>> handleException(Exception e) {
        logger.error("Unexpected exception occurred", e);
        
        ApiResponse<Void> response = ApiResponse.error(ResultCode.INTERNAL_SERVER_ERROR.getCode(), 
                "Unexpected error occurred");
        
        return new ResponseEntity<>(response, HttpStatus.INTERNAL_SERVER_ERROR);
    }
    
    /**
     * 根据错误码确定HTTP状态码
     * 
     * @param errorCode 错误码
     * @return HTTP状态码
     */
    private HttpStatus determineHttpStatus(Integer errorCode) {
        if (errorCode == null) {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        }
        
        if (ResultCode.isClientError(errorCode)) {
            if (errorCode.equals(ResultCode.UNAUTHORIZED.getCode())) {
                return HttpStatus.UNAUTHORIZED;
            } else if (errorCode.equals(ResultCode.FORBIDDEN.getCode()) || 
                      errorCode.equals(ResultCode.PERMISSION_DENIED.getCode())) {
                return HttpStatus.FORBIDDEN;
            } else if (errorCode.equals(ResultCode.NOT_FOUND.getCode()) ||
                      errorCode.toString().endsWith("02")) { // xxx_NOT_FOUND
                return HttpStatus.NOT_FOUND;
            } else if (errorCode.toString().endsWith("03") || // xxx_ALREADY_EXISTS
                      errorCode.toString().endsWith("04") || // xxx_DUPLICATE
                      errorCode.toString().endsWith("05")) {
                return HttpStatus.CONFLICT;
            } else {
                return HttpStatus.BAD_REQUEST;
            }
        } else if (ResultCode.isServerError(errorCode)) {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        } else if (ResultCode.isBusinessError(errorCode)) {
            return HttpStatus.BAD_REQUEST;
        } else {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        }
    }
}
