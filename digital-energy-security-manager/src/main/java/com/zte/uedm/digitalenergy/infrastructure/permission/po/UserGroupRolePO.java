package com.zte.uedm.digitalenergy.infrastructure.permission.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户组角色关联持久化对象
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@TableName("t_user_group_role")
public class UserGroupRolePO {
    
    /**
     * 关联ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    
    /**
     * 用户组ID
     */
    @TableField("user_group_id")
    private Long userGroupId;
    
    /**
     * 角色ID
     */
    @TableField("role_id")
    private Long roleId;
    
    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;
}
