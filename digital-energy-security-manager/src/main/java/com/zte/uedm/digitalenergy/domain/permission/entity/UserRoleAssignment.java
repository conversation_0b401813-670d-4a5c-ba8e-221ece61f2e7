package com.zte.uedm.digitalenergy.domain.permission.entity;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 用户角色分配实体
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public class UserRoleAssignment {
    
    /**
     * 分配ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private Long userId;
    
    /**
     * 角色ID
     */
    private Long roleId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 默认构造函数
     */
    public UserRoleAssignment() {
    }
    
    /**
     * 构造函数
     * 
     * @param userId 用户ID
     * @param roleId 角色ID
     */
    public UserRoleAssignment(Long userId, Long roleId) {
        if (userId == null) {
            throw new IllegalArgumentException("User ID cannot be null");
        }
        if (roleId == null) {
            throw new IllegalArgumentException("Role ID cannot be null");
        }
        
        this.userId = userId;
        this.roleId = roleId;
        this.createTime = LocalDateTime.now();
    }
    
    /**
     * 检查分配是否有效
     * 
     * @return true-有效，false-无效
     */
    public boolean isValid() {
        return userId != null && roleId != null;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public Long getUserId() {
        return userId;
    }
    
    public void setUserId(Long userId) {
        this.userId = userId;
    }
    
    public Long getRoleId() {
        return roleId;
    }
    
    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserRoleAssignment that = (UserRoleAssignment) o;
        return Objects.equals(userId, that.userId) &&
               Objects.equals(roleId, that.roleId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(userId, roleId);
    }
    
    @Override
    public String toString() {
        return "UserRoleAssignment{" +
                "id=" + id +
                ", userId=" + userId +
                ", roleId=" + roleId +
                ", createTime=" + createTime +
                '}';
    }
}
