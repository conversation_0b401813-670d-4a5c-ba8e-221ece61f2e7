package com.zte.uedm.digitalenergy.common.response;

/**
 * 响应状态码枚举
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public enum ResultCode {
    
    // 成功
    SUCCESS(200, "操作成功"),
    
    // 客户端错误 4xx
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权访问"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源未找到"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    
    // 服务器错误 5xx
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    
    // 业务错误 1xxx
    BUSINESS_ERROR(1000, "业务处理失败"),
    VALIDATION_ERROR(1001, "数据验证失败"),
    
    // 权限管理相关错误 2xxx
    PERMISSION_DENIED(2001, "权限不足"),
    ROLE_NOT_FOUND(2002, "角色不存在"),
    ROLE_ALREADY_EXISTS(2003, "角色已存在"),
    ROLE_CANNOT_DELETE(2004, "角色不能删除"),
    ROLE_NAME_DUPLICATE(2005, "角色名称重复"),
    ROLE_CODE_DUPLICATE(2006, "角色编码重复"),
    ROLE_COUNT_LIMIT_EXCEEDED(2007, "角色数量超出限制"),
    
    USER_NOT_FOUND(2101, "用户不存在"),
    USER_ALREADY_EXISTS(2102, "用户已存在"),
    USER_CANNOT_DELETE(2103, "用户不能删除"),
    USER_CODE_DUPLICATE(2104, "用户工号重复"),
    USER_NAME_DUPLICATE(2105, "用户名重复"),
    USER_ROLE_LIMIT_EXCEEDED(2106, "用户角色数量超出限制"),
    USER_VALIDATION_FAILED(2107, "用户身份验证失败"),
    
    USER_GROUP_NOT_FOUND(2201, "用户组不存在"),
    USER_GROUP_ALREADY_EXISTS(2202, "用户组已存在"),
    USER_GROUP_CANNOT_DELETE(2203, "用户组不能删除"),
    USER_GROUP_NAME_DUPLICATE(2204, "用户组名称重复"),
    USER_GROUP_CODE_DUPLICATE(2205, "用户组编码重复"),
    USER_GROUP_COUNT_LIMIT_EXCEEDED(2206, "用户组数量超出限制"),
    USER_GROUP_ROLE_LIMIT_EXCEEDED(2207, "用户组角色数量超出限制"),
    
    MENU_NOT_FOUND(2301, "菜单不存在"),
    MENU_ALREADY_EXISTS(2302, "菜单已存在"),
    MENU_CANNOT_DELETE(2303, "菜单不能删除"),
    MENU_CODE_DUPLICATE(2304, "菜单编码重复"),
    MENU_NAME_DUPLICATE(2305, "菜单名称重复"),
    MENU_HAS_CHILDREN(2306, "菜单存在子菜单"),
    
    // 外部服务错误 3xxx
    UAC_SERVICE_ERROR(3001, "UAC服务调用失败"),
    USER_NOT_FOUND_IN_UAC(3002, "UAC用户不存在"),
    UAC_ORGANIZATION_NOT_FOUND(3003, "UAC组织机构不存在"),
    
    // 数据库错误 4xxx
    DATABASE_ERROR(4001, "数据库操作失败"),
    DATA_INTEGRITY_VIOLATION(4002, "数据完整性约束违反"),
    DUPLICATE_KEY_ERROR(4003, "数据重复"),
    
    // 缓存错误 5xxx
    CACHE_ERROR(5001, "缓存操作失败"),
    CACHE_KEY_NOT_FOUND(5002, "缓存键不存在");
    
    private final Integer code;
    private final String message;
    
    ResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
    
    public Integer getCode() {
        return code;
    }
    
    public String getMessage() {
        return message;
    }
    
    /**
     * 根据状态码获取结果码枚举
     * 
     * @param code 状态码
     * @return 结果码枚举
     */
    public static ResultCode fromCode(Integer code) {
        for (ResultCode resultCode : values()) {
            if (resultCode.code.equals(code)) {
                return resultCode;
            }
        }
        return INTERNAL_SERVER_ERROR;
    }
    
    /**
     * 判断是否为成功状态码
     * 
     * @param code 状态码
     * @return true-成功，false-失败
     */
    public static boolean isSuccess(Integer code) {
        return SUCCESS.code.equals(code);
    }
    
    /**
     * 判断是否为客户端错误
     * 
     * @param code 状态码
     * @return true-客户端错误，false-非客户端错误
     */
    public static boolean isClientError(Integer code) {
        return code != null && code >= 400 && code < 500;
    }
    
    /**
     * 判断是否为服务器错误
     * 
     * @param code 状态码
     * @return true-服务器错误，false-非服务器错误
     */
    public static boolean isServerError(Integer code) {
        return code != null && code >= 500 && code < 600;
    }
    
    /**
     * 判断是否为业务错误
     * 
     * @param code 状态码
     * @return true-业务错误，false-非业务错误
     */
    public static boolean isBusinessError(Integer code) {
        return code != null && code >= 1000 && code < 6000;
    }
    
    @Override
    public String toString() {
        return "ResultCode{" +
                "code=" + code +
                ", message='" + message + '\'' +
                '}';
    }
}
