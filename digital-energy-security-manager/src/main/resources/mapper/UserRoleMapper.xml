<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.digitalenergy.infrastructure.permission.mapper.UserRoleMapper">

    <!-- 批量插入用户角色关联 -->
    <insert id="batchInsert">
        INSERT INTO t_user_role (user_id, role_id, create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.userId}, #{item.roleId}, #{item.createTime})
        </foreach>
    </insert>

</mapper>
