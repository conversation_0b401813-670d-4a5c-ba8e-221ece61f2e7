<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.digitalenergy.infrastructure.permission.mapper.UserMapper">

    <!-- 分页查询用户列表 -->
    <select id="selectByPage" resultType="com.zte.uedm.digitalenergy.infrastructure.permission.po.UserPO">
        SELECT * FROM t_user
        <where>
            <if test="username != null and username != ''">
                AND username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="userCode != null and userCode != ''">
                AND user_code LIKE CONCAT('%', #{userCode}, '%')
            </if>
            <if test="organization != null and organization != ''">
                AND organization LIKE CONCAT('%', #{organization}, '%')
            </if>
            AND status = 1
        </where>
        ORDER BY create_time DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 统计用户总数 -->
    <select id="countUsers" resultType="long">
        SELECT COUNT(*) FROM t_user
        <where>
            <if test="username != null and username != ''">
                AND username LIKE CONCAT('%', #{username}, '%')
            </if>
            <if test="userCode != null and userCode != ''">
                AND user_code LIKE CONCAT('%', #{userCode}, '%')
            </if>
            <if test="organization != null and organization != ''">
                AND organization LIKE CONCAT('%', #{organization}, '%')
            </if>
            AND status = 1
        </where>
    </select>

</mapper>
