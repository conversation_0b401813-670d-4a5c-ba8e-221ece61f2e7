<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.digitalenergy.infrastructure.permission.mapper.RoleMapper">

    <!-- 分页查询角色列表 -->
    <select id="selectByPage" resultType="com.zte.uedm.digitalenergy.infrastructure.permission.po.RolePO">
        SELECT * FROM t_role
        <where>
            <if test="roleName != null and roleName != ''">
                AND role_name LIKE CONCAT('%', #{roleName}, '%')
            </if>
            <if test="roleType != null and roleType != ''">
                AND role_type = #{roleType}
            </if>
            AND status = 1
        </where>
        ORDER BY create_time DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 统计角色总数 -->
    <select id="countRoles" resultType="long">
        SELECT COUNT(*) FROM t_role
        <where>
            <if test="roleName != null and roleName != ''">
                AND role_name LIKE CONCAT('%', #{roleName}, '%')
            </if>
            <if test="roleType != null and roleType != ''">
                AND role_type = #{roleType}
            </if>
            AND status = 1
        </where>
    </select>

</mapper>
