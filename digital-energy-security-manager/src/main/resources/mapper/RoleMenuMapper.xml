<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.digitalenergy.infrastructure.permission.mapper.RoleMenuMapper">

    <!-- 批量插入角色菜单关联 -->
    <insert id="batchInsert">
        INSERT INTO t_role_menu (role_id, menu_id, create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.roleId}, #{item.menuId}, #{item.createTime})
        </foreach>
    </insert>

</mapper>
