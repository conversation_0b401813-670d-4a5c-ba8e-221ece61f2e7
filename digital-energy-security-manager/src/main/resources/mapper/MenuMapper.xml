<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zte.uedm.digitalenergy.infrastructure.permission.mapper.MenuMapper">

    <!-- 根据角色ID列表查询菜单列表 -->
    <select id="selectMenusByRoleIds" resultType="com.zte.uedm.digitalenergy.infrastructure.permission.po.MenuPO">
        SELECT DISTINCT m.* FROM t_menu m
        INNER JOIN t_role_menu rm ON m.id = rm.menu_id
        WHERE rm.role_id IN
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
        AND m.status = 1
        ORDER BY m.menu_level, m.sort_order
    </select>

    <!-- 查询菜单的所有子菜单ID（递归） -->
    <select id="selectAllChildrenIds" resultType="long">
        WITH RECURSIVE menu_tree AS (
            SELECT id FROM t_menu WHERE parent_id = #{menuId}
            UNION ALL
            SELECT m.id FROM t_menu m
            INNER JOIN menu_tree mt ON m.parent_id = mt.id
        )
        SELECT id FROM menu_tree
    </select>

</mapper>
