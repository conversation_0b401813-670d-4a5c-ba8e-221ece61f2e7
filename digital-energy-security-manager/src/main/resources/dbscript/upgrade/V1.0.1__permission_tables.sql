-- 权限管理系统升级脚本 V1.0.1
-- 支持幂等性执行

-- 检查并添加新字段（如果将来需要扩展）
-- 示例：为角色表添加新字段
DO $$
BEGIN
    -- 检查是否存在 max_users 字段，如果不存在则添加
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 't_role' AND column_name = 'max_users'
    ) THEN
        ALTER TABLE t_role ADD COLUMN max_users INTEGER DEFAULT NULL COMMENT '角色最大用户数限制';
    END IF;
    
    -- 检查是否存在 valid_from 字段，如果不存在则添加
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 't_role' AND column_name = 'valid_from'
    ) THEN
        ALTER TABLE t_role ADD COLUMN valid_from TIMESTAMP DEFAULT NULL COMMENT '角色生效时间';
    END IF;
    
    -- 检查是否存在 valid_to 字段，如果不存在则添加
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 't_role' AND column_name = 'valid_to'
    ) THEN
        ALTER TABLE t_role ADD COLUMN valid_to TIMESTAMP DEFAULT NULL COMMENT '角色失效时间';
    END IF;
END $$;

-- 为用户表添加扩展字段
DO $$
BEGIN
    -- 检查是否存在 last_login_time 字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 't_user' AND column_name = 'last_login_time'
    ) THEN
        ALTER TABLE t_user ADD COLUMN last_login_time TIMESTAMP DEFAULT NULL COMMENT '最后登录时间';
    END IF;
    
    -- 检查是否存在 login_count 字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 't_user' AND column_name = 'login_count'
    ) THEN
        ALTER TABLE t_user ADD COLUMN login_count INTEGER DEFAULT 0 COMMENT '登录次数';
    END IF;
END $$;

-- 为菜单表添加扩展字段
DO $$
BEGIN
    -- 检查是否存在 menu_type 字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 't_menu' AND column_name = 'menu_type'
    ) THEN
        ALTER TABLE t_menu ADD COLUMN menu_type VARCHAR(20) DEFAULT 'MENU' COMMENT '菜单类型：MENU-菜单，BUTTON-按钮';
    END IF;
    
    -- 检查是否存在 permission_code 字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 't_menu' AND column_name = 'permission_code'
    ) THEN
        ALTER TABLE t_menu ADD COLUMN permission_code VARCHAR(100) DEFAULT NULL COMMENT '权限编码';
    END IF;
END $$;

-- 创建新的索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_role_valid_time ON t_role(valid_from, valid_to);
CREATE INDEX IF NOT EXISTS idx_user_last_login ON t_user(last_login_time);
CREATE INDEX IF NOT EXISTS idx_menu_type ON t_menu(menu_type);
CREATE INDEX IF NOT EXISTS idx_menu_permission_code ON t_menu(permission_code);

-- 添加新的约束（如果不存在）
DO $$
BEGIN
    -- 检查角色名称长度约束
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'chk_role_name_length'
    ) THEN
        ALTER TABLE t_role ADD CONSTRAINT chk_role_name_length 
        CHECK (char_length(role_name) <= 20);
    END IF;
    
    -- 检查用户组名称长度约束
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.check_constraints 
        WHERE constraint_name = 'chk_group_name_length'
    ) THEN
        ALTER TABLE t_user_group ADD CONSTRAINT chk_group_name_length 
        CHECK (char_length(group_name) <= 50);
    END IF;
END $$;

-- 更新现有数据（如果需要）
-- 为现有菜单设置默认的菜单类型
UPDATE t_menu SET menu_type = 'MENU' WHERE menu_type IS NULL;

-- 为现有菜单设置权限编码（基于菜单编码）
UPDATE t_menu SET permission_code = menu_code WHERE permission_code IS NULL;

-- 插入新的菜单项（如果需要）
-- 审计日志菜单已移除

-- 为系统管理员添加新菜单权限
INSERT INTO t_role_menu (role_id, menu_id)
SELECT r.id, m.id
FROM t_role r, t_menu m
WHERE r.role_code = 'deptAdmin' 
  AND m.menu_code = 'audit_log'
  AND NOT EXISTS (
    SELECT 1 FROM t_role_menu rm 
    WHERE rm.role_id = r.id AND rm.menu_id = m.id
  );

-- 创建视图（如果需要）
CREATE OR REPLACE VIEW v_user_permissions AS
SELECT DISTINCT
    u.id as user_id,
    u.username,
    u.user_code,
    m.id as menu_id,
    m.menu_name,
    m.menu_code,
    m.menu_path,
    m.permission_code,
    'DIRECT' as permission_source,
    r.role_name
FROM t_user u
JOIN t_user_role ur ON u.id = ur.user_id
JOIN t_role r ON ur.role_id = r.id
JOIN t_role_menu rm ON r.id = rm.role_id
JOIN t_menu m ON rm.menu_id = m.id
WHERE u.status = 1 AND r.status = 1 AND m.status = 1

UNION

SELECT DISTINCT
    u.id as user_id,
    u.username,
    u.user_code,
    m.id as menu_id,
    m.menu_name,
    m.menu_code,
    m.menu_path,
    m.permission_code,
    'GROUP' as permission_source,
    r.role_name
FROM t_user u
JOIN t_user_group_member ugm ON u.id = ugm.user_id
JOIN t_user_group ug ON ugm.user_group_id = ug.id
JOIN t_user_group_role ugr ON ug.id = ugr.user_group_id
JOIN t_role r ON ugr.role_id = r.id
JOIN t_role_menu rm ON r.id = rm.role_id
JOIN t_menu m ON rm.menu_id = m.id
WHERE u.status = 1 AND ug.status = 1 AND r.status = 1 AND m.status = 1;

-- 添加视图注释
COMMENT ON VIEW v_user_permissions IS '用户权限视图，包含直接分配和用户组继承的权限';

-- 创建函数（如果需要）
CREATE OR REPLACE FUNCTION get_user_menu_tree(p_user_id BIGINT)
RETURNS TABLE (
    menu_id BIGINT,
    menu_name VARCHAR,
    menu_code VARCHAR,
    parent_id BIGINT,
    menu_path VARCHAR,
    menu_icon VARCHAR,
    sort_order INTEGER,
    menu_level INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT
        m.id,
        m.menu_name,
        m.menu_code,
        m.parent_id,
        m.menu_path,
        m.menu_icon,
        m.sort_order,
        m.menu_level
    FROM v_user_permissions vup
    JOIN t_menu m ON vup.menu_id = m.id
    WHERE vup.user_id = p_user_id
    ORDER BY m.menu_level, m.sort_order;
END;
$$ LANGUAGE plpgsql;

-- 添加函数注释
COMMENT ON FUNCTION get_user_menu_tree(BIGINT) IS '获取用户菜单树函数';

-- 记录升级日志
-- 审计日志功能已移除，不再记录升级日志
