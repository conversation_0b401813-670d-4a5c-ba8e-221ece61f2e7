-- 删除不再使用的字段
-- 版本: V1.0.2
-- 描述: 删除t_menu表的菜单图标和状态字段，删除t_role、t_user、t_user_group表的状态字段

-- 1. 删除t_menu表的字段和索引
-- 删除菜单图标字段
ALTER TABLE t_menu DROP COLUMN IF EXISTS menu_icon;

-- 删除状态字段
ALTER TABLE t_menu DROP COLUMN IF EXISTS status;

-- 删除状态字段相关索引
DROP INDEX IF EXISTS idx_menu_status;

-- 2. 删除t_role表的字段和索引
-- 删除状态字段
ALTER TABLE t_role DROP COLUMN IF EXISTS status;

-- 删除状态字段相关索引
DROP INDEX IF EXISTS idx_role_status;

-- 3. 删除t_user表的字段和索引
-- 删除状态字段
ALTER TABLE t_user DROP COLUMN IF EXISTS status;

-- 删除状态字段相关索引
DROP INDEX IF EXISTS idx_user_status;

-- 4. 删除t_user_group表的字段和索引
-- 删除状态字段
ALTER TABLE t_user_group DROP COLUMN IF EXISTS status;

-- 删除状态字段相关索引
DROP INDEX IF EXISTS idx_user_group_status;

-- 5. 更新视图定义（如果存在）
-- 重新创建用户权限视图，移除status条件
DROP VIEW IF EXISTS v_user_permissions;

CREATE VIEW v_user_permissions AS
SELECT DISTINCT
    u.id as user_id,
    u.username,
    u.user_code,
    m.id as menu_id,
    m.menu_name,
    m.menu_code,
    m.menu_path,
    m.permission_code,
    'DIRECT' as permission_source,
    r.role_name
FROM t_user u
JOIN t_user_role ur ON u.id = ur.user_id
JOIN t_role r ON ur.role_id = r.id
JOIN t_role_menu rm ON r.id = rm.role_id
JOIN t_menu m ON rm.menu_id = m.id

UNION

SELECT DISTINCT
    u.id as user_id,
    u.username,
    u.user_code,
    m.id as menu_id,
    m.menu_name,
    m.menu_code,
    m.menu_path,
    m.permission_code,
    'GROUP' as permission_source,
    r.role_name
FROM t_user u
JOIN t_user_group_member ugm ON u.id = ugm.user_id
JOIN t_user_group ug ON ugm.user_group_id = ug.id
JOIN t_user_group_role ugr ON ug.id = ugr.user_group_id
JOIN t_role r ON ugr.role_id = r.id
JOIN t_role_menu rm ON r.id = rm.role_id
JOIN t_menu m ON rm.menu_id = m.id;

-- 添加视图注释
COMMENT ON VIEW v_user_permissions IS '用户权限视图，包含直接分配和用户组继承的权限（已移除状态字段过滤）';

-- 6. 更新函数定义（如果存在）
-- 重新创建获取用户菜单树函数，移除status条件
DROP FUNCTION IF EXISTS get_user_menu_tree(BIGINT);

CREATE OR REPLACE FUNCTION get_user_menu_tree(p_user_id BIGINT)
RETURNS TABLE (
    menu_id BIGINT,
    menu_name VARCHAR,
    menu_code VARCHAR,
    parent_id BIGINT,
    menu_path VARCHAR,
    sort_order INTEGER,
    menu_level INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT DISTINCT
        m.id,
        m.menu_name,
        m.menu_code,
        m.parent_id,
        m.menu_path,
        m.sort_order,
        m.menu_level
    FROM v_user_permissions vup
    JOIN t_menu m ON vup.menu_id = m.id
    WHERE vup.user_id = p_user_id
    ORDER BY m.menu_level, m.sort_order;
END;
$$ LANGUAGE plpgsql;

-- 添加函数注释
COMMENT ON FUNCTION get_user_menu_tree(BIGINT) IS '获取用户菜单树函数（已移除状态字段过滤）';

-- 记录升级完成
-- 由于审计日志功能已移除，此处仅添加注释记录
-- 升级脚本V1.0.2执行完成：删除了menu_icon字段和所有表的status字段
