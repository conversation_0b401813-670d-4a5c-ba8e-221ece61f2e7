@echo off

set RUNHOME=%~dp0%
echo ### RUNHOME: %RUNHOME%

if exist "%RUNHOME%setenv.bat" (
  call "%RUNHOME%setenv.bat"
)


echo ================== ENV_INFO  =============================================
echo RUNHOME=%RUNHOME%
echo JAVA_BASE=%JAVA_BASE%
echo Main_Class=%Main_Class%
echo APP_INFO=%APP_INFO%
echo Main_JAR=%Main_JAR%
echo Main_Conf=%Main_Conf%
echo ==========================================================================

title %APP_INFO%
echo ### Starting %APP_INFO%

set JAVA="%JAVA_HOME%/bin/java"
set JAVA_OPTS=-Xms512m -Xmx768m
set JAVA_OPTS=%JAVA_OPTS% -DMS_APP_NAME=JMS_DEMO_MICROSERVICENAME
set port=8779
rem set JAVA_OPTS=%JAVA_OPTS% -Xdebug -Xnoagent -Djava.compiler=NONE -Xrunjdwp:transport=dt_socket,address=%port%,server=y,suspend=n
set CLASS_PATH=%LIB_DIRS%;%RUNHOME%;%RUNHOME%%Main_JAR%


echo ================== RUN_INFO  =============================================
echo ### JAVA_HOME: %JAVA_HOME%
echo ### JAVA: %JAVA%
echo ### jvm_opts: %JAVA_OPTS%
echo ### class_path: %CLASS_PATH%
echo ==========================================================================

echo @JAVA@ %JAVA%
echo @JAVA_CMD@
%JAVA% -classpath %CLASS_PATH% %JAVA_OPTS% %Main_Class%