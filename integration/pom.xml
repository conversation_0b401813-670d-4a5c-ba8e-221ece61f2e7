<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.zte.uedm.digital.energy</groupId>
        <artifactId>digitalEnergyTechPlatform</artifactId>
        <version>${digital.energy.service.version}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>integration</artifactId>
    <name>服务打包</name>
    <packaging>pom</packaging>

    <dependencies>
        <dependency>
            <groupId>com.zte.uedm.digital.energy</groupId>
            <artifactId>${ms.manager.security.manager}</artifactId>
            <version>${digital.energy.service.version}</version>
            <type>tar.gz</type>
        </dependency>
        <dependency>
            <groupId>com.zte.uedm.digital.energy</groupId>
            <artifactId>${ms.manager.data.manager}</artifactId>
            <version>${digital.energy.service.version}</version>
            <type>tar.gz</type>
        </dependency>
        <dependency>
            <groupId>com.zte.uedm.digital.energy</groupId>
            <artifactId>${ms.manager.iui}</artifactId>
            <version>${digital.energy.service.version}</version>
            <type>tar.gz</type>
        </dependency>
        <dependency>
            <groupId>com.zte.uedm.digital.energy</groupId>
            <artifactId>digital-energy-common</artifactId>
            <version>${digital.energy.service.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>

                        <id>msj-copy-resources-blueprint</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${service.dir}/</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/service</directory>
                                    <filtering>true</filtering>
                                    <excludes>
                                        <exclude>**/*.gz</exclude>
                                    </excludes>
                                </resource>
                                <resource>
                                    <directory>src/service</directory>
                                    <filtering>false</filtering>
                                    <includes>
                                        <include>**/*.gz</include>
                                    </includes>
                                </resource>
                            </resources>
                            <overwrite>true</overwrite>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-resources-oki</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${release.dir}</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/oki</directory>
                                    <filtering>true</filtering>
                                    <includes>
                                        <include>**/*</include>
                                    </includes>
                                </resource>
                            </resources>
                            <overwrite>true</overwrite>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>digital-copy-security-manager-docker-zip</id>
                        <goals>
                            <goal>copy</goal>
                        </goals>
                        <!-- here the phase you need -->
                        <phase>generate-resources</phase>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>com.zte.uedm.digital.energy</groupId>
                                    <artifactId>${ms.manager.security.manager}</artifactId>
                                    <type>tar.gz</type>
                                </artifactItem>

                            </artifactItems>
                            <outputDirectory>${service.dir}/${manager.security.manager.artifactId}</outputDirectory>
                            <overWriteReleases>false</overWriteReleases>
                            <overWriteSnapshots>true</overWriteSnapshots>
                        </configuration>
                    </execution>
                    <execution>
                        <id>digital-copy-data-manager-docker-zip</id>
                        <goals>
                            <goal>copy</goal>
                        </goals>
                        <!-- here the phase you need -->
                        <phase>generate-resources</phase>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>com.zte.uedm.digital.energy</groupId>
                                    <artifactId>${ms.manager.data.manager}</artifactId>
                                    <type>tar.gz</type>
                                </artifactItem>

                            </artifactItems>
                            <outputDirectory>${service.dir}/${manager.data.manager.artifactId}</outputDirectory>
                            <overWriteReleases>false</overWriteReleases>
                            <overWriteSnapshots>true</overWriteSnapshots>
                        </configuration>
                    </execution>
                    <execution>
                        <id>digital-copy-iui-manager-docker-zip</id>
                        <goals>
                            <goal>copy</goal>
                        </goals>
                        <!-- here the phase you need -->
                        <phase>generate-resources</phase>
                        <configuration>
                            <artifactItems>
                                <artifactItem>
                                    <groupId>com.zte.uedm.digital.energy</groupId>
                                    <artifactId>${ms.manager.iui}</artifactId>
                                    <type>tar.gz</type>
                                </artifactItem>

                            </artifactItems>
                            <outputDirectory>${service.dir}/${manager.iui.artifactId}</outputDirectory>
                            <overWriteReleases>false</overWriteReleases>
                            <overWriteSnapshots>true</overWriteSnapshots>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <id>distribution</id>
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target name="distribution">
                                <tar
                                        destfile="${release.dir}/${service.name}-${project.parent.version}.tar.gz"
                                        longfile="posix" compression="gzip">
                                    <tarfileset dir="${output.dir}" filemode="0540" dirmode="0755"
                                                uid="3001" gid="3001" username="oes" group="oes">
                                        <include name="${service.name}/**" />
                                    </tarfileset>
                                </tar>
                                <attachartifact
                                        file="${release.dir}/${service.name}-${project.parent.version}.tar.gz"
                                        type="tar.gz" />
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>