{"data_name": "@service.name@", "version": "@image.version@", "description": "@service.name@ services.", "service_list": [{"service_name": "@service.name@", "version": "@image.version@", "common_service_list": [{"common_service_name": "kafka-0", "resource_name": "zenap_kafka"}, {"common_service_name": "redis-0", "resource_name": "zenap_redis"}, {"common_service_name": "postgresql-0", "resource_name": "digital-energy-pg"}], "volume_list": [{"name": "<EMAIL>@", "key": "path", "type": "hostPath", "value": "/paasdata/op-tenant/{TENENT}/uedm/logs/digital-energy/digital-energy-security-manager"}, {"name": "<EMAIL>@", "key": "path", "type": "hostPath", "value": "/paasdata/op-tenant/{TENENT}/uedm/dumps/digital-energy/digital-energy-security-manager"}, {"name": "<EMAIL>@", "key": "path", "type": "hostPath", "value": "/paasdata/op-tenant/{TENENT}/uedm/gclogs/digital-energy/digital-energy-security-manager"}, {"name": "<EMAIL>>@", "key": "path", "type": "hostPath", "value": "/paasdata/op-tenant/{TENENT}/uedm/logs/digital-energy/digital-energy-data-manager"}, {"name": "<EMAIL>>@", "key": "path", "type": "hostPath", "value": "/paasdata/op-tenant/{TENENT}/uedm/dumps/digital-energy/digital-energy-data-manager"}, {"name": "<EMAIL>>@", "key": "path", "type": "hostPath", "value": "/paasdata/op-tenant/{TENENT}/uedm/gclogs/digital-energy/digital-energy-data-manager"}], "env_list": [], "other_list": []}]}