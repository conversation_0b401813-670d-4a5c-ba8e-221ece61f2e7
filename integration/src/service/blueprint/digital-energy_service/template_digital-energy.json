{
	"kind": "Template",
	"apiVersion": "v1",
	"namespace": "${NAMESPACE}",
	"metadata": {
		"name": "@service.name@",
		"labels": {
			"name": "@service.name@"
		}
	},
	"objects": [
		{
			"kind": "Deployment",
			"apiVersion": "apps/v1",
			"metadata": {
				"name": "@ms.manager.security.manager@",
				"namespace": "${NAMESPACE}"
			},
			"spec": {
				"selector": {
					"matchLabels": {
						"name": "@ms.manager.security.manager@"
					}
				},
				"template": {
					"metadata": {
						"labels": {
							"name": "@ms.manager.security.manager@"
						},
						"annotations": {
							"cnrm.knitter.io/enhance_config": "{\"containers\":[{\"name\":\"@ms.manager.security.manager@\",\"oomKillDisable\":false}]}",
							"zte.com.cn/numa_request": "{\"numaAffinityPolicy\":\"none\"}"
						}
					},
					"spec": {
						"containers": [
							{
								"name": "@ms.manager.security.manager@",
								"image": "/@tenant.id@/<EMAIL>@:@image.version@",
								"imagePullPolicy": "Always",
								"tty": false,
								"securityContext": {
									"runAsNonRoot": "#{runAsNonRoot}",
									"runAsUser": "#{runAsUser}",
									"runAsGroup":"#{runAsGroup}",
									"allowPrivilegeEscalation": false,
									"privileged": false,
									"capabilities": {
										"drop": ["NET_RAW"]
									}
								},
								"stdin": false,
								"command": [],
								"env": [
									{
										"name": "permit_root_start",
										"value": "${permit_root_start}"
									},{
										"name": "pvc_type",
										"value": "${pvc_type}"
									},
									{
										"name": "TZ",
										"value": "${TZ}"
									},
									{
										"name": "OPENPALETTE_KAFKA_ADDRESS",
										"value": "get_property:[${kafka-0},OPENPALETTE_KAFKA_ADDRESS]"
									},
									{
										"name": "OPENPALETTE_KAFKA_PORT",
										"value": "get_property:[${kafka-0},OPENPALETTE_KAFKA_PORT]"
									},
									{
										"name": "OPENPALETTE_REDIS_ADDRESS",
										"value": "get_property:[${redis-0},OPENPALETTE_REDIS_ADDRESS]"
									},
									{
										"name": "OPENPALETTE_REDIS_PORT",
										"value": "get_property:[${redis-0},OPENPALETTE_REDIS_PORT]"
									},
									{
										"name": "OPENPALETTE_REDIS_PASSWORD",
										"value": "get_property:[${redis-0},OPENPALETTE_REDIS_PASSWORD]"
									},
									{
										"name": "OPENPALETTE_REDIS_SENTINEL_ADDRESS",
										"value": "get_property:[${redis-0},OPENPALETTE_REDIS_SENTINEL_ADDRESS]"
									},
									{
										"name": "OPENPALETTE_REDIS_SENTINEL_PORT",
										"value": "get_property:[${redis-0},OPENPALETTE_REDIS_SENTINEL_PORT]"
									},
									{
										"name": "OPENPALETTE_REDIS_SENTINEL_MASTERNAME",
										"value": "get_property:[${redis-0},OPENPALETTE_REDIS_SENTINEL_MASTERNAME]"
									},
									{
										"name": "OPENPALETTE_PG_ADDRESS",
										"value": "get_property:[${postgresql-0},OPENPALETTE_PG_ADDRESS]"
									},
									{
										"name": "OPENPALETTE_PG_PORT",
										"value": "get_property:[${postgresql-0},OPENPALETTE_PG_PORT]"
									},
									{
										"name": "OPENPALETTE_PG_DBNAME",
										"value": "get_property:[${postgresql-0},OPENPALETTE_PG_DBNAME]"
									},
									{
										"name": "OPENPALETTE_PG_USERNAME",
										"value": "get_property:[${postgresql-0},OPENPALETTE_PG_USERNAME]"
									},
									{
										"name": "OPENPALETTE_PG_PASSWORD",
										"value": "get_property:[${postgresql-0},OPENPALETTE_PG_PASSWORD]"
									},
									{
										"name": "net_api_eth",
										"value": "eth0"
									}
								],
								"ports": [
									{
										"protocol": "TCP",
										"containerPort": @manager.security.manager.port@
									}
								],
								"volumeMounts": [
									{
										"name": "<EMAIL>@",
										"mountPath": "/home/<USER>/logs",
										"readOnly": false
									},
									{
										"name": "<EMAIL>@",
										"mountPath": "/home/<USER>/dump",
										"readOnly": false
									},
									{
										"name": "<EMAIL>@",
										"mountPath": "/home/<USER>/gclog",
										"readOnly": false
									}
								],
								"resources": {
									"requests": {
										"cpu": 0.1,
										"memory": "0.1Gi"
									},
									"limits": {
										"cpu": "2",
										"memory": "4Gi"
									}
								}
							}
						],
						"initContainers": [
							{
								"name": "@ms.manager.security.manager@-init-nonroot",
								"image": "/@tenant.id@/<EMAIL>@:@image.version@",
								"imagePullPolicy": "Always",
								"tty": false,
								"securityContext": {
									"privileged": false,
									"allowPrivilegeEscalation": false
								},
								"stdin": false,
								"command": ["/home/<USER>/chmod_file.sh"],
								"env": [
									{
										"name": "permit_root_start",
										"value": "${permit_root_start}"
									},
									{
										"name": "pvc_type",
										"value": "${pvc_type}"
									},
									{
										"name": "LOG_FILE_LOG",
										"value": "/home/<USER>/logs"
									},
									{
										"name": "LOG_FILE_GCLOG",
										"value": "/home/<USER>/gclog"
									},
									{
										"name": "OES_CHOWN_DIR_GCLOG",
										"value": "/home/<USER>/gclog"
									},
									{
										"name": "OES_CHOWN_DIR_LOG",
										"value": "/home/<USER>/logs"
									},
									{
										"name": "OES_CHOWN_DIR_DUMP",
										"value": "/home/<USER>/dump"
									}
								],
								"ports": [],
								"volumeMounts": [
									{
										"name": "<EMAIL>@",
										"mountPath": "/home/<USER>/logs",
										"readOnly": false
									},
									{
										"name": "<EMAIL>@",
										"mountPath": "/home/<USER>/dump",
										"readOnly": false
									},
									{
										"name": "<EMAIL>@",
										"mountPath": "/home/<USER>/gclog",
										"readOnly": false
									}
								],
								"resources": {
									"requests": {
										"cpu": 0.1,
										"memory": "0.1Gi"
									},
									"limits": {
										"cpu": "2",
										"memory": "4Gi"
									}
								}
							}
						],
						"restartPolicy": "Always",
						"volumes": [
							{
								"name": "<EMAIL>@",
								"${<EMAIL>@_type}": {
									"${<EMAIL>@_key}": "${<EMAIL>@_value}"
								}
							},
							{
								"name": "<EMAIL>@",
								"${<EMAIL>@_type}": {
									"${<EMAIL>@_key}": "${<EMAIL>@_value}"
								}
							},
							{
								"name": "<EMAIL>@",
								"${<EMAIL>@_type}": {
									"${<EMAIL>@_key}": "${<EMAIL>@_value}"
								}
							}
						],
						"terminationGracePeriodSeconds": 30,
						"hostNetwork": false,
						"securityContext": {
							"sysctls": []
						},
						"hostIPC": false
					}
				},
				"replicas": "1",
				"strategy": {
					"type": "RollingUpdate",
					"rollingUpdate": {
						"maxSurge": "25%",
						"maxUnavailable": "25%"
					}
				}
			}
		},
		{
			"kind": "Deployment",
			"apiVersion": "apps/v1",
			"metadata": {
				"name": "@ms.manager.data.manager@",
				"namespace": "${NAMESPACE}"
			},
			"spec": {
				"selector": {
					"matchLabels": {
						"name": "@ms.manager.data.manager@"
					}
				},
				"template": {
					"metadata": {
						"labels": {
							"name": "@ms.manager.data.manager@"
						},
						"annotations": {
							"cnrm.knitter.io/enhance_config": "{\"containers\":[{\"name\":\"@ms.manager.data.manager@\",\"oomKillDisable\":false}]}",
							"zte.com.cn/numa_request": "{\"numaAffinityPolicy\":\"none\"}"
						}
					},
					"spec": {
						"containers": [
							{
								"name": "@ms.manager.data.manager@",
								"image": "/@tenant.id@/<EMAIL>@:@image.version@",
								"imagePullPolicy": "Always",
								"tty": false,
								"securityContext": {
									"runAsNonRoot": "#{runAsNonRoot}",
									"runAsUser": "#{runAsUser}",
									"runAsGroup":"#{runAsGroup}",
									"allowPrivilegeEscalation": false,
									"privileged": false,
									"capabilities": {
										"drop": ["NET_RAW"]
									}
								},
								"stdin": false,
								"command": [],
								"env": [
									{
										"name": "permit_root_start",
										"value": "${permit_root_start}"
									},
									{
										"name": "pvc_type",
										"value": "${pvc_type}"
									},
									{
										"name": "TZ",
										"value": "${TZ}"
									},
									{
										"name": "OPENPALETTE_KAFKA_ADDRESS",
										"value": "get_property:[${kafka-0},OPENPALETTE_KAFKA_ADDRESS]"
									},
									{
										"name": "OPENPALETTE_KAFKA_PORT",
										"value": "get_property:[${kafka-0},OPENPALETTE_KAFKA_PORT]"
									},
									{
										"name": "OPENPALETTE_REDIS_ADDRESS",
										"value": "get_property:[${redis-0},OPENPALETTE_REDIS_ADDRESS]"
									},
									{
										"name": "OPENPALETTE_REDIS_PORT",
										"value": "get_property:[${redis-0},OPENPALETTE_REDIS_PORT]"
									},
									{
										"name": "OPENPALETTE_REDIS_PASSWORD",
										"value": "get_property:[${redis-0},OPENPALETTE_REDIS_PASSWORD]"
									},
									{
										"name": "OPENPALETTE_REDIS_SENTINEL_ADDRESS",
										"value": "get_property:[${redis-0},OPENPALETTE_REDIS_SENTINEL_ADDRESS]"
									},
									{
										"name": "OPENPALETTE_REDIS_SENTINEL_PORT",
										"value": "get_property:[${redis-0},OPENPALETTE_REDIS_SENTINEL_PORT]"
									},
									{
										"name": "OPENPALETTE_REDIS_SENTINEL_MASTERNAME",
										"value": "get_property:[${redis-0},OPENPALETTE_REDIS_SENTINEL_MASTERNAME]"
									},
									{
										"name": "OPENPALETTE_PG_ADDRESS",
										"value": "get_property:[${postgresql-0},OPENPALETTE_PG_ADDRESS]"
									},
									{
										"name": "OPENPALETTE_PG_PORT",
										"value": "get_property:[${postgresql-0},OPENPALETTE_PG_PORT]"
									},
									{
										"name": "OPENPALETTE_PG_DBNAME",
										"value": "get_property:[${postgresql-0},OPENPALETTE_PG_DBNAME]"
									},
									{
										"name": "OPENPALETTE_PG_USERNAME",
										"value": "get_property:[${postgresql-0},OPENPALETTE_PG_USERNAME]"
									},
									{
										"name": "OPENPALETTE_PG_PASSWORD",
										"value": "get_property:[${postgresql-0},OPENPALETTE_PG_PASSWORD]"
									},
									{
										"name": "net_api_eth",
										"value": "eth0"
									}
								],
								"ports": [
									{
										"protocol": "TCP",
										"containerPort": @manager.data.manager.port@
									}
								],
								"volumeMounts": [
									{
										"name": "<EMAIL>@",
										"mountPath": "/home/<USER>/logs",
										"readOnly": false
									},
									{
										"name": "<EMAIL>@",
										"mountPath": "/home/<USER>/dump",
										"readOnly": false
									},
									{
										"name": "<EMAIL>@",
										"mountPath": "/home/<USER>/gclog",
										"readOnly": false
									}
								],
								"resources": {
									"requests": {
										"cpu": 0.1,
										"memory": "0.1Gi"
									},
									"limits": {
										"cpu": "2",
										"memory": "4Gi"
									}
								}
							}
						],
						"initContainers": [
							{
								"name": "@ms.manager.data.manager@-init-nonroot",
								"image": "/@tenant.id@/<EMAIL>@:@image.version@",
								"imagePullPolicy": "Always",
								"tty": false,
								"securityContext": {
									"privileged": false,
									"allowPrivilegeEscalation": false
								},
								"stdin": false,
								"command": ["/home/<USER>/chmod_file.sh"],
								"env": [
									{
										"name": "permit_root_start",
										"value": "${permit_root_start}"
									},
									{
										"name": "pvc_type",
										"value": "${pvc_type}"
									},
									{
										"name": "LOG_FILE_LOG",
										"value": "/home/<USER>/logs"
									},
									{
										"name": "LOG_FILE_GCLOG",
										"value": "/home/<USER>/gclog"
									},
									{
										"name": "OES_CHOWN_DIR_GCLOG",
										"value": "/home/<USER>/gclog"
									},
									{
										"name": "OES_CHOWN_DIR_LOG",
										"value": "/home/<USER>/logs"
									},
									{
										"name": "OES_CHOWN_DIR_DUMP",
										"value": "/home/<USER>/dump"
									}
								],
								"ports": [],
								"volumeMounts": [
									{
										"name": "<EMAIL>@",
										"mountPath": "/home/<USER>/logs",
										"readOnly": false
									},
									{
										"name": "<EMAIL>@",
										"mountPath": "/home/<USER>/dump",
										"readOnly": false
									},
									{
										"name": "<EMAIL>@",
										"mountPath": "/home/<USER>/gclog",
										"readOnly": false
									}
								],
								"resources": {
									"requests": {
										"cpu": 0.1,
										"memory": "0.1Gi"
									},
									"limits": {
										"cpu": "2",
										"memory": "4Gi"
									}
								}
							}
						],
						"restartPolicy": "Always",
						"volumes": [
							{
								"name": "<EMAIL>@",
								"${<EMAIL>@_type}": {
									"${<EMAIL>@_key}": "${<EMAIL>@_value}"
								}
							},
							{
								"name": "<EMAIL>@",
								"${<EMAIL>@_type}": {
									"${<EMAIL>@_key}": "${<EMAIL>@_value}"
								}
							},
							{
								"name": "<EMAIL>@",
								"${<EMAIL>@_type}": {
									"${<EMAIL>@_key}": "${<EMAIL>@_value}"
								}
							}
						],
						"terminationGracePeriodSeconds": 30,
						"hostNetwork": false,
						"securityContext": {
							"sysctls": []
						},
						"hostIPC": false
					}
				},
				"replicas": "1",
				"strategy": {
					"type": "RollingUpdate",
					"rollingUpdate": {
						"maxSurge": "25%",
						"maxUnavailable": "25%"
					}
				}
			}
		},
		{
			"kind": "DeploymentConfig",
			"apiVersion": "apps/v1",
			"metadata": {
				"name": "@ms.manager.iui@",
				"namespace": "${NAMESPACE}"
			},
			"spec": {
				"selector": {
					"matchLabels": {
						"name": "@ms.manager.iui@"
					}
				},
				"template": {
					"metadata": {
						"labels": {
							"name": "@ms.manager.iui@"
						},
						"annotations": {
							"cnrm.knitter.io/enhance_config": "{\"containers\":[{\"name\":\"@ms.manager.iui@\",\"oomKillDisable\":false}]}",
							"zte.com.cn/numa_request": "{\"numaAffinityPolicy\":\"none\"}"
						}
					},
					"spec": {
						"containers": [
							{
								"name": "@ms.manager.iui@",
								"image": "/@tenant.id@/<EMAIL>@:@image.version@",
								"imagePullPolicy": "Always",
								"tty": false,
								"securityContext": {
									"runAsNonRoot": "#{runAsNonRoot}",
									"runAsUser": "#{runAsUser}",
									"runAsGroup":"#{runAsGroup}",
									"allowPrivilegeEscalation": false,
									"privileged": false,
									"capabilities": {
										"drop": ["NET_RAW"]
									}
								},
								"stdin": false,
								"command": [],
								"env": [
									{
										"name": "permit_root_start",
										"value": "${permit_root_start}"
									},
									{
										"name": "pvc_type",
										"value": "${pvc_type}"
									},
									{
										"name": "net_api_eth",
										"value": "eth1"
									}
								],
								"ports": [
									{
										"protocol": "TCP",
										"containerPort": @iui.port@
									}
								],
								"volumeMounts": [],
								"resources": {
									"requests": {
										"cpu": 0.1,
										"memory": "0.1Gi"
									},
									"limits": {
										"cpu": "1",
										"memory": "1Gi"
									}
								}
							}
						],
						"initContainers": [
							{
								"name": "@ms.manager.iui@-init-nonroot",
								"image": "/@tenant.id@/<EMAIL>@:@image.version@",
								"imagePullPolicy": "Always",
								"tty": false,
								"securityContext": {
									"privileged": false,
									"allowPrivilegeEscalation": false
								},
								"stdin": false,
								"command": ["/home/<USER>/chmod_file.sh"],
								"env": [
									{
										"name": "permit_root_start",
										"value": "${permit_root_start}"
									},
									{
										"name": "pvc_type",
										"value": "${pvc_type}"
									},
									{
										"name": "LOG_FILE_LOG",
										"value": "/home/<USER>/logs"
									},
									{
										"name": "LOG_FILE_GCLOG",
										"value": "/home/<USER>/gclog"
									},
									{
										"name": "OES_CHOWN_DIR_GCLOG",
										"value": "/home/<USER>/gclog"
									},
									{
										"name": "OES_CHOWN_DIR_LOG",
										"value": "/home/<USER>/logs"
									},
									{
										"name": "OES_CHOWN_DIR_AUTH_CONFIG",
										"value": "/etc/goweb/conf/auth-config.properties"
									}
								],
								"ports": [],
								"volumeMounts": [],
								"resources": {
									"requests": {
										"cpu": 0.1,
										"memory": "0.1Gi"
									},
									"limits": {
										"cpu": "1",
										"memory": "1Gi"
									}
								}
							}
						],
						"restartPolicy": "Always",
						"volumes": [],
						"terminationGracePeriodSeconds": 30,
						"hostNetwork": false,
						"securityContext": {
							"sysctls": []
						},
						"hostIPC": false
					}
				},
				"replicas": "1",
				"strategy": {
					"type": "RollingUpdate",
					"rollingUpdate": {
						"timeoutSeconds": "600",
						"maxSurge": "25%",
						"maxUnavailable": "25%"
					}
				}
			}
		}
	],
	"vnpm_param": {
		"vnpm_object": [
			{
				"name": "@ms.manager.security.manager@",
				"route_list": [
					{
						"serviceName": "${@ms.manager.security.manager@}",
						"protocol": "REST",
						"port": "@manager.security.manager.port@",
						"visualRange": "0",
						"network_plane_type": "net_api",
						"version": "v1",
						"nic_name": "eth0",
						"url": "/api/security-manager/v1",
						"path": "/api/security-manager/v1",
						"lb_policy": "round-robin",
						"function": "std",
						"enable_client_verify": false,
						"enable_tls": false,
						"enable_http2": false,
						"enable_ssl": false,
						"labels": []
					}
				],
				"common_service": [
					{
						"logicName": "${kafka-0}"
					},
					{
						"logicName": "${redis-0}"
					},
					{
						"logicName": "${postgresql-0}"
					}
				],
				"isUseServiceDiscovery": true,
				"cluster_info": {
					"cluster_type": "kubernetes",
					"labelselector": []
				},
				"microservice_labels": {},
				"networks": {
					"ports": [
						{
							"attach_to_network": "net_api",
							"attributes": {
								"nic_name": "eth0",
								"function": "std",
								"nic_type": "normal",
								"combinable": "true",
								"layer_type": "layer3"
							}
						},
						{
							"attach_to_network": "lan",
							"attributes": {
								"nic_name": "eth1",
								"function": "std",
								"nic_type": "normal",
								"combinable": "true",
								"layer_type": "layer3"
							}
						}
					],
					"version": "v1"
				}
			},
			{
				"name": "@ms.manager.data.manager@",
				"route_list": [
					{
						"serviceName": "${@ms.manager.data.manager@}",
						"protocol": "REST",
						"port": "@manager.data.manager.port@",
						"visualRange": "0",
						"network_plane_type": "net_api",
						"version": "v1",
						"nic_name": "eth0",
						"url": "/api/data-manager/v1",
						"path": "/api/data-manager/v1",
						"lb_policy": "round-robin",
						"function": "std",
						"enable_client_verify": false,
						"enable_tls": false,
						"enable_http2": false,
						"enable_ssl": false,
						"labels": []
					}
				],
				"common_service": [
					{
						"logicName": "${kafka-0}"
					},
					{
						"logicName": "${redis-0}"
					},
					{
						"logicName": "${postgresql-0}"
					}
				],
				"isUseServiceDiscovery": true,
				"cluster_info": {
					"cluster_type": "kubernetes",
					"labelselector": []
				},
				"microservice_labels": {},
				"networks": {
					"ports": [
						{
							"attach_to_network": "net_api",
							"attributes": {
								"nic_name": "eth0",
								"function": "std",
								"nic_type": "normal",
								"combinable": "true",
								"layer_type": "layer3"
							}
						},
						{
							"attach_to_network": "lan",
							"attributes": {
								"nic_name": "eth1",
								"function": "std",
								"nic_type": "normal",
								"combinable": "true",
								"layer_type": "layer3"
							}
						}
					],
					"version": "v1"
				}
			},
			{
				"name": "@ms.manager.iui@",
				"route_list": [
					{
						"serviceName": "${@ms.manager.iui@}",
						"protocol": "UI",
						"port": "@iui.port@",
						"visualRange": "0",
						"network_plane_type": "net_api",
						"version": "v1",
						"nic_name": "eth0",
						"url": "/iui/@service.name@",
						"path": "/ROOT_PATH",
						"lb_policy": "round-robin",
						"function": "std",
						"enable_client_verify": false,
						"enable_tls": false,
						"enable_http2": false,
						"enable_ssl": false,
						"labels": []
					}
				],
				"common_service": [],
				"isUseServiceDiscovery": true,
				"cluster_info": {
					"cluster_type": "kubernetes",
					"labelselector": []
				},
				"microservice_labels": {},
				"networks": {
					"ports": [
						{
							"attach_to_network": "net_api",
							"attributes": {
								"nic_name": "eth0",
								"function": "std",
								"nic_type": "normal",
								"combinable": "true",
								"layer_type": "layer3"
							}
						},
						{
							"attach_to_network": "lan",
							"attributes": {
								"nic_name": "eth1",
								"function": "std",
								"nic_type": "normal",
								"combinable": "true",
								"layer_type": "layer3"
							}
						}
					],
					"version": "v1"
				}
			}
		],
		"service_labels": {}
	},
	"eps_param": {
		"auto_policy": {
			"digital-energy-data-manager": {
				"digital-energy-data-manager": [{
					"param_name": "cpu_usage_rate",
					"isDefault": true,
					"defaultValue": [
						30,
						80
					],
					"value": ""
				},
					{
						"param_name": "HeapMemoryUsage",
						"isDefault": true,
						"defaultValue": [
							45,
							75
						],
						"value": ""
					},
					{
						"param_name": "memory_usage_rate",
						"isDefault": true,
						"defaultValue": [
							45,
							75
						],
						"value": ""
					}]
			},
			"digital-energy-security-manager": {
				"digital-energy-security-manager": [{
					"param_name": "cpu_usage_rate",
					"isDefault": true,
					"defaultValue": [
						30,
						80
					],
					"value": ""
				},
					{
						"param_name": "HeapMemoryUsage",
						"isDefault": true,
						"defaultValue": [
							45,
							75
						],
						"value": ""
					},
					{
						"param_name": "memory_usage_rate",
						"isDefault": true,
						"defaultValue": [
							45,
							75
						],
						"value": ""
					}]
			},
			"digital-energy-iui": {
				"digital-energy-iui": []
			}
		},
		"scale_alg": {
			"@ms.manager.security.manager@": {
				"scale_in_forbidden_window": 300,
				"scale_out_forbidden_window": 180,
				"scale_decision_period": 30,
				"kpi_sample_num": 2,
				"scale_start_delay": 300,
				"step_params": {
					"step_mode": "auto"
				}
			},
			"@ms.manager.data.manager@": {
				"scale_in_forbidden_window": 300,
				"scale_out_forbidden_window": 180,
				"scale_decision_period": 30,
				"kpi_sample_num": 2,
				"scale_start_delay": 300,
				"step_params": {
					"step_mode": "auto"
				}
			},
			"@ms.manager.iui@": {
				"scale_in_forbidden_window": 300,
				"scale_out_forbidden_window": 180,
				"scale_decision_period": 30,
				"kpi_sample_num": 2,
				"scale_start_delay": 300,
				"step_params": {
					"step_mode": "auto"
				}
			}
		},
		"replicasPara_list": [
			{
				"ms_name": "@ms.manager.security.manager@",
				"replicasMin": "1",
				"replicasMax": "1"
			},
			{
				"ms_name": "@ms.manager.data.manager@",
				"replicasMin": "1",
				"replicasMax": "1"
			},
			{
				"ms_name": "@ms.manager.iui@",
				"replicasMin": "1",
				"replicasMax": "1"
			}
		],
		"pod_migration": {
			"@ms.manager.security.manager@": {
				"enable": false,
				"migration_mode": "deleted_first"
			},
			"@ms.manager.data.manager@": {
				"enable": false,
				"migration_mode": "deleted_first"
			},
			"@ms.manager.iui@": {
				"enable": false,
				"migration_mode": "deleted_first"
			}
		}
	},
	"parameters": [
		{
			"displayName": "",
			"name": "permit_root_start",
			"section": "env",
			"value": "true",
			"type": "string",
			"description": ""
		},
		{
			"displayName": "",
			"name": "pvc_type",
			"section": "env",
			"value": "nfs",
			"type": "string",
			"description": ""
		},
		{
			"name": "runAsNonRoot",
			"displayName": "runAsNonRoot",
			"value":false,
			"section": "other",
			"type": "boolean",
			"description": ""
		},
		{
			"name": "runAsUser",
			"displayName": "runAsUser",
			"value": 0,
			"section": "other",
			"type": "int",
			"description": ""
		},
		{
			"name": "runAsGroup",
			"displayName": "runAsGroup",
			"value": 0,
			"section": "other",
			"type": "int",
			"description": ""
		},
		{
			"name": "kafka-0",
			"displayName": "kafka-0",
			"description": "kafka-0",
			"value": "",
			"section": "commonService",
			"subSection": "Kafka",
			"type": "string"
		},
		{
			"name": "redis-0",
			"displayName": "redis-0",
			"description": "redis-0",
			"value": "",
			"section": "commonService",
			"subSection": "redis",
			"type": "string"
		},
		{
			"name": "postgresql-0",
			"displayName": "postgresql-0",
			"description": "postgresql-0",
			"value": "",
			"section": "commonService",
			"subSection": "PostgreSQL",
			"type": "string"
		},
		{
			"name": "TZ",
			"displayName": "TZ",
			"value": "Asia/Shanghai",
			"section": "env",
			"type": "string",
			"description": ""
		},
		{
			"name": "<EMAIL>@_type",
			"displayName": "<EMAIL>@",
			"description": "",
			"value": "hostPath",
			"section": "volumes",
			"type": "string"
		},
		{
			"name": "<EMAIL>@_key",
			"displayName": "<EMAIL>@",
			"description": "",
			"value": "path",
			"section": "volumes",
			"type": "string"
		},
		{
			"name": "<EMAIL>@_value",
			"displayName": "<EMAIL>@",
			"description": "",
			"value": "/paasdata/op-tenant/@tenant.id@/@tenant.id@/logs/@ms.manager.security.manager@",
			"section": "volumes",
			"type": "string"
		},
		{
			"name": "<EMAIL>@_type",
			"displayName": "<EMAIL>@",
			"description": "",
			"value": "hostPath",
			"section": "volumes",
			"type": "string"
		},
		{
			"name": "<EMAIL>@_key",
			"displayName": "<EMAIL>@",
			"description": "",
			"value": "path",
			"section": "volumes",
			"type": "string"
		},
		{
			"name": "<EMAIL>@_value",
			"displayName": "<EMAIL>@",
			"description": "",
			"value": "/paasdata/op-tenant/@tenant.id@/@tenant.id@/dumps/@ms.manager.security.manager@",
			"section": "volumes",
			"type": "string"
		},
		{
			"name": "<EMAIL>@_type",
			"displayName": "<EMAIL>@",
			"description": "",
			"value": "hostPath",
			"section": "volumes",
			"type": "string"
		},
		{
			"name": "<EMAIL>@_key",
			"displayName": "<EMAIL>@",
			"description": "",
			"value": "path",
			"section": "volumes",
			"type": "string"
		},
		{
			"name": "<EMAIL>@_value",
			"displayName": "<EMAIL>@",
			"description": "",
			"value": "/paasdata/op-tenant/@tenant.id@/@tenant.id@/gclogs/@ms.manager.security.manager@",
			"section": "volumes",
			"type": "string"
		},
		{
			"name": "@ms.manager.security.manager@",
			"displayName": "@ms.manager.security.manager@",
			"value": "@ms.manager.security.manager@",
			"section": "route",
			"type": "string",
			"description": ""
		},
		{
			"name": "@service.name@",
			"displayName": "@service.name@",
			"value": "@service.name@",
			"section": "route",
			"type": "string",
			"description": ""
		},
		{
			"name": "NAMESPACE",
			"displayName": "",
			"description": "",
			"value": "@tenant.id@",
			"section": "None",
			"type": "string"
		},
		{
			"name": "<EMAIL>@_type",
			"displayName": "<EMAIL>@",
			"description": "",
			"value": "hostPath",
			"section": "volumes",
			"type": "string"
		},
		{
			"name": "<EMAIL>@_key",
			"displayName": "<EMAIL>@",
			"description": "",
			"value": "path",
			"section": "volumes",
			"type": "string"
		},
		{
			"name": "<EMAIL>@_value",
			"displayName": "<EMAIL>@",
			"description": "",
			"value": "/paasdata/op-tenant/@tenant.id@/@tenant.id@/logs/@ms.manager.data.manager@",
			"section": "volumes",
			"type": "string"
		},
		{
			"name": "<EMAIL>@_type",
			"displayName": "<EMAIL>@",
			"description": "",
			"value": "hostPath",
			"section": "volumes",
			"type": "string"
		},
		{
			"name": "<EMAIL>@_key",
			"displayName": "<EMAIL>@",
			"description": "",
			"value": "path",
			"section": "volumes",
			"type": "string"
		},
		{
			"name": "<EMAIL>@_value",
			"displayName": "<EMAIL>@",
			"description": "",
			"value": "/paasdata/op-tenant/@tenant.id@/@tenant.id@/dumps/@ms.manager.data.manager@",
			"section": "volumes",
			"type": "string"
		},
		{
			"name": "<EMAIL>@_type",
			"displayName": "<EMAIL>@",
			"description": "",
			"value": "hostPath",
			"section": "volumes",
			"type": "string"
		},
		{
			"name": "<EMAIL>@_key",
			"displayName": "<EMAIL>@",
			"description": "",
			"value": "path",
			"section": "volumes",
			"type": "string"
		},
		{
			"name": "<EMAIL>@_value",
			"displayName": "<EMAIL>@",
			"description": "",
			"value": "/paasdata/op-tenant/@tenant.id@/@tenant.id@/gclogs/@ms.manager.data.manager@",
			"section": "volumes",
			"type": "string"
		},
		{
			"name": "@ms.manager.data.manager@",
			"displayName": "@ms.manager.data.manager@",
			"value": "@ms.manager.data.manager@",
			"section": "route",
			"type": "string",
			"description": ""
		},
		{
			"name": "@ms.manager.iui@",
			"displayName": "@ms.manager.iui@",
			"value": "@ms.manager.iui@",
			"section": "route",
			"type": "string",
			"description": ""
		}
	],
	"view_params": [
		{
			"name": "net_api",
			"position": {
				"width": 6,
				"height": 600,
				"left": 720,
				"top": 100
			},
			"children": []
		},
		{
			"name": "lan",
			"position": {
				"width": 6,
				"height": 600,
				"left": 750,
				"top": 100
			},
			"children": []
		},
		{
			"name": "kafka-0",
			"position": {
				"width": 180,
				"height": 50,
				"left": 80,
				"top": 60
			},
			"children": []
		},
		{
			"name": "redis-0",
			"position": {
				"width": 180,
				"height": 50,
				"left": 80,
				"top": 160
			},
			"children": []
		},
		{
			"name": "postgresql-0",
			"position": {
				"width": 180,
				"height": 50,
				"left": 80,
				"top": 270
			},
			"children": []
		},
		{
			"name": "@service.name@",
			"position": {
				"width": 180,
				"height": 50,
				"left": 350,
				"top": 100
			},
			"children": [
				{
					"name": "@ms.manager.security.manager@",
					"position": {
						"width": 180,
						"height": 50,
						"left": 28,
						"top": 7
					},
					"children": [
						{
							"name": "@ms.manager.security.manager@",
							"position": {
								"width": 180,
								"height": 50,
								"left": 34,
								"top": 3
							},
							"children": [
								{
									"name": "@ms.manager.security.manager@",
									"position": {
										"width": 16,
										"height": 16,
										"left": 1,
										"top": 31
									},
									"children": []
								}
							]
						},
						{
							"name": "eth0",
							"position": {
								"width": 16,
								"height": 16
							},
							"children": []
						},
						{
							"name": "eth1",
							"position": {
								"width": 16,
								"height": 16
							},
							"children": []
						}
					]
				},
				{
					"name": "@ms.manager.data.manager@",
					"position": {
						"width": 180,
						"height": 50,
						"left": 28,
						"top": 7
					},
					"children": [
						{
							"name": "@ms.manager.data.manager@",
							"position": {
								"width": 180,
								"height": 50,
								"left": 34,
								"top": 3
							},
							"children": [
								{
									"name": "@ms.manager.data.manager@",
									"position": {
										"width": 16,
										"height": 16,
										"left": 1,
										"top": 31
									},
									"children": []
								}
							]
						},
						{
							"name": "eth0",
							"position": {
								"width": 16,
								"height": 16
							},
							"children": []
						},
						{
							"name": "eth1",
							"position": {
								"width": 16,
								"height": 16
							},
							"children": []
						}
					]
				},
				{
					"name": "@ms.manager.iui@",
					"position": {
						"width": 180,
						"height": 50,
						"left": 28,
						"top": 7
					},
					"children": [
						{
							"name": "@ms.manager.iui@",
							"position": {
								"width": 180,
								"height": 50,
								"left": 34,
								"top": 3
							},
							"children": [
								{
									"name": "@ms.manager.iui@",
									"position": {
										"width": 16,
										"height": 16,
										"left": 1,
										"top": 31
									},
									"children": []
								}
							]
						},
						{
							"name": "eth0",
							"position": {
								"width": 16,
								"height": 16
							},
							"children": []
						},
						{
							"name": "eth1",
							"position": {
								"width": 16,
								"height": 16
							},
							"children": []
						}
					]
				}
			]
		}
	]
}